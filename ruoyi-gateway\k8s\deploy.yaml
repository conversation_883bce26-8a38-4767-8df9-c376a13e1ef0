apiVersion: apps/v1
kind: Deployment
metadata:
  name: 695-gateway
  namespace: "695"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: 695-gateway
  template:
    metadata:
      labels:
        app: 695-gateway
    spec:
      containers:
        - name: 695-gateway
          image: ************/695/ruoyi-gateway:latest
          env:
          - name: NACOS_HOST
            value: "nacos-headless.nacos"
          - name: REDIS_HOST
            value: "svc-redis.dev"
          - name: REDIS_PWD
            value: "123456"
          ports:
            - containerPort: 8080
      imagePullSecrets:
        - name: harbor-login
---

apiVersion: v1
kind: Service
metadata:
  name: svc-695-gateway
  namespace: "695"
spec:
  selector:
    app: 695-gateway
  type: NodePort
  ports:
    - port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30080
