package com.ruoyi.production.utils;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.production.domain.BaseFillerType;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.domain.vo.TrayInfoVo;
import com.ruoyi.production.enums.StorageFillerTypeEnum;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IBaseFillerTypeService;
import com.ruoyi.production.service.IProdWorkOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class TrayInfoUtils {

    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private IBaseFillerTypeService fillerTypeService;

    public static TrayInfoVo getEmptyTrayInfo(String rackId, String tag) {
        TrayInfoVo trayInfoVo = new TrayInfoVo();
        trayInfoVo.setRackId(rackId);
        trayInfoVo.setTag(tag);
        trayInfoVo.setFillerType(StorageFillerTypeEnum.EMPTY.getCode());
        trayInfoVo.setFillerTypeName(StorageFillerTypeEnum.EMPTY.getInfo());
        return trayInfoVo;
    }

    /**
     * 补全托盘参数
     *
     * @param trayInfoVos
     * @param actionCell
     * @param line2RoomDto
     * @return
     */
    public static JSONObject fillIotTrayParam(List<TrayInfoVo> trayInfoVos
            , List<String> actionCell
            , JSONObject line2RoomDto) {
        //添加托架参数
        line2RoomDto.put("dockid", "dock." + trayInfoVos.get(0).getRackId());
        //空参数格式

        List<String> needToAdd = new ArrayList<>();
        needToAdd = trayInfoVos.stream().filter(e -> !actionCell.contains(e.getTag())).map(e -> e.getTag()).collect(Collectors.toList());
        for (String s : needToAdd) {
            JSONObject nullObj = new JSONObject();
            nullObj.put("obj", null);
            nullObj.put("box", "unset");
            line2RoomDto.put(s, nullObj);
        }
        return line2RoomDto;
    }


    public static TrayInfoVo getPartTrayInfo(String rackId, String tag, ProdWorkOrderVo workOrder) {
        TrayInfoVo trayInfoVo = new TrayInfoVo();
        trayInfoVo.setRackId(rackId);
        trayInfoVo.setTag(tag);
        trayInfoVo.setFillerType(StorageFillerTypeEnum.PART.getCode());
        trayInfoVo.setFillerTypeName(StorageFillerTypeEnum.PART.getInfo());
        trayInfoVo.setMatName(workOrder.getMatName());
        trayInfoVo.setTicketNumber(workOrder.getTicketNumber());
        trayInfoVo.setOperationId(workOrder.getOperationId());
        trayInfoVo.setWorkOrderStatus(workOrder.getStatus());
        trayInfoVo.setEqpCode(workOrder.getEqpCode());
        trayInfoVo.setWorkOrderStatusName(WorkOrderStatusEnum.getInfoByKey(workOrder.getStatus()));
        //2023年4月18日 09:42:12添加
        trayInfoVo.setEqpGroup(workOrder.getEqpGroup());
        trayInfoVo.setRealEqp(workOrder.getRealEqp());
        return trayInfoVo;

    }

    public List<TrayInfoVo> fillterByLine(Long lineId, List<TrayInfoVo> list) {
        if (5l == lineId) {
            for (TrayInfoVo trayInfoVo : list) {
                if (trayInfoVo.getTag().equals("b1")) {
                    list.remove(trayInfoVo);
                    break;
                }
            }
        }
        return list;
    }

//    public static TrayInfoVo getTrayInfo(String rackId, String tag, String trayType) {
//        TrayInfoVo trayInfoVo = new TrayInfoVo();
//        trayInfoVo.setRackId(rackId);
//        trayInfoVo.setTag(tag);
//        trayInfoVo.setFillerType(TransStorageFillerTypeEnum.getInfoByValue(trayType));
//        trayInfoVo.setFillerTypeName(StorageFillerTypeEnum.getInfoByKey(TransStorageFillerTypeEnum.getInfoByValue(trayType)));
//        return trayInfoVo;
//    }

//    public JSONObject transferIotObj(TrayInfoVo trayInfoVo) {
//        JSONObject jsonObject = new JSONObject();
//        if (trayInfoVo.getOperationId() != null)
//            trayInfoVo.setFillerType(StorageFillerTypeEnum.PART.getCode());
//        if (StorageFillerTypeEnum.EMPTY.getCode() == trayInfoVo.getFillerType()) {
//            jsonObject = null;
//        } else if (StorageFillerTypeEnum.PART.getCode() == trayInfoVo.getFillerType()) {
//            ProdWorkOrder prodWorkOrder = orderService.getEntityByOperationId(trayInfoVo.getOperationId());
//            JSONObject part = new JSONObject();
//            part.put("procid", trayInfoVo.getOperationId());
//            part.put("no", prodWorkOrder.getTicketNumber());
//            part.put("mat", prodWorkOrder.getMaterialDesc());
//            part.put("machid", prodWorkOrder.getEqpCode());
//            part.put("status", WorkOrderStatusEnum.DONE.getCode() == prodWorkOrder.getStatus() ? "pod" : "mat");
//            jsonObject.put("part", part);
//            jsonObject.put("objtype", "part");
//            jsonObject.put("tray", null);
//        } else {
//            JSONObject tray = new JSONObject();
//            tray.put("trayid", TransStorageFillerTypeEnum.getInfoByKey(trayInfoVo.getFillerType()));
//            jsonObject.put("tray", tray);
//            jsonObject.put("objtype", "tray");
//            jsonObject.put("part", null);
//        }
//        return jsonObject;
//    }

    public JSONObject transferIotObj(Long lineId, TrayInfoVo trayInfoVo) {
        JSONObject jsonObject = new JSONObject();

        if (trayInfoVo.getOperationId() != null)
            trayInfoVo.setFillerType(StorageFillerTypeEnum.PART.getCode());

        BaseFillerType baseFillerType = fillerTypeService.getByFillerType(lineId, trayInfoVo.getFillerType());
        if (StorageFillerTypeEnum.EMPTY.getCode() == baseFillerType.getFillerType()) {
            jsonObject = null;
        } else if (StorageFillerTypeEnum.PART.getCode() == baseFillerType.getFillerType()) {
            ProdWorkOrder prodWorkOrder = orderService.getEntityByOperationId(trayInfoVo.getOperationId());
            JSONObject part = new JSONObject();
            part.put("procid", trayInfoVo.getOperationId());
            part.put("no", prodWorkOrder.getTicketNumber());
            part.put("mat", prodWorkOrder.getMaterialDesc());
            part.put("machid", prodWorkOrder.getEqpCode());
            part.put("status", WorkOrderStatusEnum.DONE.getCode() == prodWorkOrder.getStatus() ? "pod" : "mat");
            jsonObject.put("part", part);
            jsonObject.put("objtype", "part");
            jsonObject.put("tray", null);
        } else {
            JSONObject tray = new JSONObject();
            tray.put("trayid", baseFillerType.getAlias());
            jsonObject.put("tray", tray);
            jsonObject.put("objtype", "tray");
            jsonObject.put("part", null);
        }
        return jsonObject;
    }

    public JSONObject transferIotObjMod(Long lineId, TrayInfoVo trayInfoVo) {
        JSONObject jsonObject = new JSONObject();

//        if (trayInfoVo.getOperationId() != null)
//            trayInfoVo.setFillerType(StorageFillerTypeEnum.PART.getCode());

        BaseFillerType baseFillerType = fillerTypeService.getByFillerType(lineId, trayInfoVo.getFillerType());
        if (StorageFillerTypeEnum.EMPTY.getCode() == baseFillerType.getFillerType()) {
            jsonObject = null;
        } else if (StorageFillerTypeEnum.PART.getCode() == baseFillerType.getFillerType()) {
            ProdWorkOrder prodWorkOrder = orderService.getEntityByOperationId(trayInfoVo.getOperationId());
            JSONObject part = new JSONObject();
            part.put("procid", trayInfoVo.getOperationId());
            part.put("no", prodWorkOrder.getTicketNumber());
            part.put("mat", prodWorkOrder.getMaterialDesc());
            part.put("machid", prodWorkOrder.getEqpCode());
            part.put("status", WorkOrderStatusEnum.DONE.getCode() == prodWorkOrder.getStatus() ? "pod" : "mat");
            jsonObject.put("part", part);
            jsonObject.put("objtype", "part");
            jsonObject.put("tray", null);
        } else {
            JSONObject tray = new JSONObject();
            tray.put("trayid", baseFillerType.getAlias());
            jsonObject.put("tray", tray);
            jsonObject.put("objtype", "tray");
            jsonObject.put("part", null);
        }
        return jsonObject;
    }

}
