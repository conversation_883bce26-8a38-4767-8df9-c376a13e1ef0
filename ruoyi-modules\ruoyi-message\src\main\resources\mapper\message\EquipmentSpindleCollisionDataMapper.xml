<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.message.mapper.EquipmentSpindleCollisionDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.message.domian.EquipmentSpindleCollisionData">
    <result column="id" property="id" />
    <result column="remark" property="remark" />
    <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="coordinate" property="coordinate" />
        <result column="value" property="value" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        deleted,
        create_by, create_time, update_by, update_time, coordinate, value,sync_time,mach
    </sql>

    <select id="listByCondition" resultMap="BaseResultMap">
        select * from equipment_spindle_collision_data
        <where>
<!--            <if test="params.startTime != null and params.lineId != ''">-->
<!--                and line_id = #{params.lineId}-->
<!--            </if>-->
<!--            <if test="params.status != null and params.status != ''">-->
<!--                and status = #{params.status}-->
<!--            </if>-->
            <if test="params.startTime != null">
                and sync_time &lt;= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and sync_time  &gt;= #{params.endTime}
            </if>
        </where>
        order by mach,sync_time
    </select>
</mapper>
