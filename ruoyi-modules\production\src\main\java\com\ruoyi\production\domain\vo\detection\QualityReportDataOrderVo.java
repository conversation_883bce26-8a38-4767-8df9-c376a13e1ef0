package com.ruoyi.production.domain.vo.detection;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class QualityReportDataOrderVo {
    @ApiModelProperty("物料名称")
    private String matName;
    @ApiModelProperty("任务号")
    private String billCode;
    @ApiModelProperty("工序号")
    private String schedulCode;
    @ApiModelProperty("工序名")
    private String schedulName;
    @ApiModelProperty("物料编码")
    private String matCode;
    @ApiModelProperty("加工设备型号")
    private String eqpSystem;
    @ApiModelProperty("生产数量")
    private Integer quanity;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndTime;
}
