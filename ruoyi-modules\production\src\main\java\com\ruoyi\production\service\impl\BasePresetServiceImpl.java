package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.BasePreset;
import com.ruoyi.production.mapper.BasePresetMapper;
import com.ruoyi.production.service.IBasePresetService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 工作组（一个预调台+一个中转站） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Service
public class BasePresetServiceImpl extends ServiceImpl<BasePresetMapper, BasePreset> implements IBasePresetService {

    @Override
    public BasePreset getEntityByAlias(String alias) {
        QueryWrapper<BasePreset> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("alias", alias);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BasePreset> getLitByLineId(Long lineId) {
        QueryWrapper<BasePreset> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("line_id", lineId);
        return this.baseMapper.selectList(queryWrapper);
    }

}
