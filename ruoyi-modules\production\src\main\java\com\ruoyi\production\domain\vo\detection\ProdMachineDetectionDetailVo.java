package com.ruoyi.production.domain.vo.detection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.production.domain.ProdMachineDetectionDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProdMachineDetectionDetailVo extends ProdMachineDetectionDetail {

    private String checkerName;

    private String operationSplit;

    @ApiModelProperty(value = "加工机床")
    private String eqpCode;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "工序名称")
    private String schedulName;

    @ApiModelProperty(value = "实际加工开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realStartTime;

    @ApiModelProperty(value = "实际加工结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndTime;

    @ApiModelProperty(value = "真实加工机床")
    private String realEqp;

}
