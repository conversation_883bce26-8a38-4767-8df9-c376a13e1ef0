package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-05-07
 */
public interface ProdOperationTraceMapper extends BaseMapper<ProdOperationTrace> {
    List<ProdOperationTraceVo> listByCondition(@Param("params") ProdOperationTrace operationTrace);

    IPage<ProdOperationTraceVo> listForPage(IPage<ProdOperationTraceVo> page, @Param("params") ProdOperationTrace operationTrace);

    IPage<ProdOperationTraceVo> logInfoListForPage(IPage<ProdOperationTraceVo> page, @Param("params") ProdOperationTrace operationTrace);
}
