package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.production.domain.ProdOperationTraceDetail;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDetailDto;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-05-30
 */
public interface ProdOperationTraceDetailMapper extends BaseMapper<ProdOperationTraceDetail> {

    List<ProdOperationTraceDetailVo> getDetailList(@Param("params") ProdOperationTraceDetailDto traceDetailDto);

}
