package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdOrderMachRelation;
import com.ruoyi.production.domain.dto.ordermachconfig.OrderMachConfigDto;
import com.ruoyi.production.domain.vo.OrderMachConfigVo;

import java.util.List;

/**
 * <p>
 *  IProdOrderMachRelationService 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
public interface IProdOrderMachRelationService extends IService<ProdOrderMachRelation> {

    List<OrderMachConfigVo> getOrderMachConfig(OrderMachConfigDto dto);

    void saveOrderMachConfig(OrderMachConfigDto dto);

}
