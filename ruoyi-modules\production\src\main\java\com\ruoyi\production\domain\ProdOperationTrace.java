package com.ruoyi.production.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-05-07
 */
@ApiModel(value = "ProdOperationTrace对象", description = "")
public class ProdOperationTrace extends MyBaseEntity {


    private Long lineId;

    private String lineCode;

    private Long processId;

    private String processCode;

    @ApiModelProperty(value = "指令代码")
    private String cmd;

    @ApiModelProperty(value = "0：待执行，1：执行中，2：完成，3：错误")
    private String status;

    private String stopReason;

    private String currentAction;

    @ApiModelProperty(value = "动作设备")
    private Long actionEqpId;
    @ApiModelProperty(value = "取点设备")
    private Long fromEqpId;
    @ApiModelProperty(value = "放点设备")
    private Long toEqpId;

    @ApiModelProperty(value = "下操作原始数据")
    private String operationData;

    @TableField(exist = false)
    private Integer endFlag;

    public Integer getEndFlag() {
        return endFlag;
    }

    public void setEndFlag(Integer endFlag) {
        this.endFlag = endFlag;
    }

    public String getOperationData() {
        return operationData;
    }

    public void setOperationData(String operationData) {
        this.operationData = operationData;
    }

    public Long getActionEqpId() {
        return actionEqpId;
    }

    public void setActionEqpId(Long actionEqpId) {
        this.actionEqpId = actionEqpId;
    }

    public Long getFromEqpId() {
        return fromEqpId;
    }

    public void setFromEqpId(Long fromEqpId) {
        this.fromEqpId = fromEqpId;
    }

    public Long getToEqpId() {
        return toEqpId;
    }

    public void setToEqpId(Long toEqpId) {
        this.toEqpId = toEqpId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public String getProcessCode() {
        return processCode;
    }

    public void setProcessCode(String processCode) {
        this.processCode = processCode;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }


    public String getStopReason() {
        return stopReason;
    }

    public void setStopReason(String stopReason) {
        this.stopReason = stopReason;
    }

    public String getCurrentAction() {
        return currentAction;
    }

    public void setCurrentAction(String currentAction) {
        this.currentAction = currentAction;
    }
}
