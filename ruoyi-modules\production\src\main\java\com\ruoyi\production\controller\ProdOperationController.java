package com.ruoyi.production.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.core.annotation.CheckIOTAutoProcess;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.BaseProcess;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.dto.operation.InOutStockDto;
import com.ruoyi.production.domain.vo.BaseLineStorageVo;
import com.ruoyi.production.domain.vo.TrayInfoVo;
import com.ruoyi.production.domain.vo.iot.InOrOutStockVo;
import com.ruoyi.production.domain.vo.iot.OperationVo;
import com.ruoyi.production.domain.vo.iot.ToBenchVo;
import com.ruoyi.production.domain.vo.iot.ToDockVo;
import com.ruoyi.production.enums.OpCmdEnum;
import com.ruoyi.production.enums.OperationStatusEnum;
import com.ruoyi.production.enums.StorageFillerTypeEnum;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.*;
import com.ruoyi.production.utils.TrayInfoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("operation")
@Api(value = "ProdOperationController", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class ProdOperationController extends BaseModuleController {

    @Autowired
    private IProdOperationTraceService operationTraceService;
    @Autowired
    private IBaseProcessService baseProcessService;
    @Autowired
    private IProdWorkOrderService workOrderService;
    @Autowired
    private IBaseLineStorageService storageService;
    @Autowired
    private TrayInfoUtils trayInfoUtils;

    @ApiOperation("出库")
    @PostMapping("outstock")
//    @CheckIOTAutoProcess
    public AjaxResult outstock(@RequestBody InOutStockDto requestParams) {
        String billcode = requestParams.getBillcode();
        String schedulCode = requestParams.getSchedulCode();
        String positionCode = requestParams.getPositionCode();
        String key = OpCmdEnum.OUTSTOCK.getCode();
        //校验动作是否可做
//        if (null != prodRedisUtil.getAssociation(ProductionConstans.OPERATION_PREFIX, getLineId(), key))
//            return AjaxResult.error("1分钟内请勿重复操作该动作！");
        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");

        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTrace.setOperationData(JSON.toJSONString(requestParams));
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

        //新的操作接口参数
        InOrOutStockVo inOrOutStockVo = InOrOutStockVo.builder()
                .num(-1)
                .processid(schedulCode)
                .wms_box(positionCode)
                .taskid(billcode)
                .build();
        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .data(inOrOutStockVo)
                .build();

/*        //构建接口操作记录参数主体
        JSONObject op = new JSONObject();
        op.put("opid", operationTrace.getId());
        op.put("line", getLineId());//todo 后续自动
        op.put("cmd", "outstock");//
        op.put("proclist", procList);//工单列表参数*/

        //管控端处理url
        String iotUrl = "";
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();
        //发送请求
        String result = null;
        try {
            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            //operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        }

//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());
        return AjaxResult.success(operationVo);
    }

    @ApiOperation("入库")
    @PostMapping("instock")
    //@Transactional
    public AjaxResult instock(@RequestBody InOutStockDto requestParams
    ) {
        String billcode = requestParams.getBillcode();
        String schedulCode = requestParams.getSchedulCode();
        Integer instockNum = requestParams.getInstockNum();

        //20220608 查询数量 校验入库数量是否超过已完成数量
        ProdWorkOrderDto dto = new ProdWorkOrderDto();
        dto.setBillCode(billcode);
        dto.setSchedulCode(schedulCode);
        dto.setStatus(WorkOrderStatusEnum.DONE.getCode());
        List<ProdWorkOrder> allList = workOrderService.getEntityListByParam(dto);
        List<ProdWorkOrder> selfDetectedList = allList.stream().filter(e -> null != e.getSelfDetectionStatus()).collect(Collectors.toList());
        if (selfDetectedList.size() < instockNum)
            throw new ServiceException(billcode + "-" + schedulCode + ":入库数量超过待检测中已自检的工件数量，已自检数量:" + selfDetectedList.size());

        String key = OpCmdEnum.INSTOCK.getCode();
        //校验动作是否可做
//        if (null != prodRedisUtil.getAssociation(ProductionConstans.OPERATION_PREFIX, getLineId(), key))
//            return AjaxResult.error("1分钟内请勿重复操作该动作！");
        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");

        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());//todo 后期从用户获取
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());//todo 后期待确认
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTrace.setOperationData(JSON.toJSONString(requestParams));
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

        //新的操作接口参数
        InOrOutStockVo inOrOutStockVo = InOrOutStockVo.builder()
                .num(instockNum)
                .processid(schedulCode)
                .taskid(billcode)
                .build();
        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .data(inOrOutStockVo)
                .build();

        //管控端处理url
        String iotUrl = "";
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();
        //发送请求
        String result = null;
        try {
            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }

        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            //operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        } else {//将工单更新为已入库
            List<ProdWorkOrder> updateList = new ArrayList<>();
            for (int i = 0; i < selfDetectedList.size(); i++) {
                ProdWorkOrder order = selfDetectedList.get(i);
                if (i < instockNum) {
                    order.setStatus(WorkOrderStatusEnum.INSTOCK.getCode());
                    updateList.add(order);
//                workOrderService.updateWorkOrderWithStatus(order);
                } else {
                    workOrderService.updateBatchById(updateList);
                    break;
                }
            }
        }
//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());

        return AjaxResult.success(operationVo);
    }

    @ApiOperation("从线边呼叫")
    @PostMapping("line2room")
    @CheckIOTAutoProcess
    public AjaxResult line2room(@RequestBody List<TrayInfoVo> trayInfoVos) {
        String key = OpCmdEnum.PULL.getCode();

        //2024-7-19 添加 三四号线互斥逻辑
        long lineId = getLineId();
        long queryLineId = 0;
        if(lineId == 3)
            queryLineId = 4;
        if(lineId == 4)
            queryLineId = 3;
        if(queryLineId >0){
            LambdaQueryWrapper<ProdOperationTrace> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProdOperationTrace::getDeleted,0);
            queryWrapper.eq(ProdOperationTrace::getLineId,queryLineId);
            queryWrapper.eq(ProdOperationTrace::getCmd,key);
            queryWrapper.ne(ProdOperationTrace::getStatus,"done");
            List<ProdOperationTrace> list = operationTraceService.list(queryWrapper);
            if(list.size()>0)
                return AjaxResult.error(queryLineId+"号线已有<从线边呼叫>操作，接驳站已被占用，请等待结束后执行！");
        }


//        log.info("************* 从线边呼叫》》》" + trayInfoVos);


        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");

        //2024-07-01 添加 过滤与plc信号不一致的线边库
        //start
        boolean hasBoxKey = false;
        String boxKey = ProductionConstans.PROD_EQP_STAT + getLineId() + ":" + 99;
        Map<String, Boolean> signalMap = new HashMap<>();
        if (redisService.hasKey(boxKey)) {
            signalMap = redisService.getCacheMap(boxKey);
            hasBoxKey = true;
        }
        //新的操作接口参数

        //拼接接口data参数
        List<String> actionCell = new ArrayList<>();
        JSONObject line2RoomDto = new JSONObject();
//        line2RoomDto.put("dockid", "dock.1");//todo dockid获取
        List<Long> filterBoxIdList = new ArrayList<>();
        Set<Long> filterBoxIdSet = new HashSet<>();
        for (TrayInfoVo trayInfoVo : trayInfoVos) {
            if (null != trayInfoVo.getUpdated() && trayInfoVo.getUpdated()) {
                JSONObject cell = new JSONObject();
                BaseLineStorage storage = storageService.getById(trayInfoVo.getBoxId());
                String boxAlias = storageService.getById(trayInfoVo.getBoxId()).getAlias();
                //2024-07-01 添加 过滤与plc信号不一致的线边库
                if(hasBoxKey){
                    if(signalMap.containsKey(boxAlias)){
                        Boolean plcHas =  signalMap.get(boxAlias);
                        if(!plcHas){
                            return AjaxResult.error("选择的库位号 "+storage.getAlias() +" 校验失败， PLC信号为无物品，本系统为有物品，请确认实际情况并修改！");
                        }
                    }
                }
                cell.put("box", boxAlias);
                cell.put("obj", trayInfoUtils.transferIotObj(getLineId(), trayInfoVo));
                line2RoomDto.put(trayInfoVo.getTag(), cell);
                actionCell.add(trayInfoVo.getTag());

                filterBoxIdList.add(trayInfoVo.getBoxId());
                filterBoxIdSet.add(trayInfoVo.getBoxId());
            }
        }
        if (filterBoxIdList.size() != filterBoxIdSet.size()) {
            log.error("@@@@@@@@@@  库位选择重复，请确认！" + filterBoxIdList.toString());
            return AjaxResult.error("库位选择重复，请确认！");
        }
        //校验接口参数 是否同一库位


        //补全托盘参数
//        List<String> needToAdd = new ArrayList<>();
//        needToAdd = trayInfoVos.stream().filter(e -> !actionCell.contains(e.getTag())).map(e -> e.getTag()).collect(Collectors.toList());
//        for (String s : needToAdd) {
//            line2RoomDto.put(s, null);
//        }

        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());//todo 后期从用户获取
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());//todo 后期待确认
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTrace.setOperationData(JSON.toJSONString(trayInfoVos));
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);


        line2RoomDto = TrayInfoUtils.fillIotTrayParam(trayInfoVos, actionCell, line2RoomDto);

        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .data(line2RoomDto)
                .build();

        //管控端处理url
        String iotUrl = "";
//        System.out.println(operationVo.toString());
//        System.out.println(JSON.toJSONString(operationVo));
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();

        //发送请求
        String result = null;
        try {

            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            //operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        }

//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());
        return AjaxResult.success(operationVo);
    }

    @ApiOperation("向线边推送")
    @PostMapping("room2line")
    @CheckIOTAutoProcess
    public AjaxResult room2line() {
        log.info("************* 向线边推送》》》");
        String key = OpCmdEnum.PUSH.getCode();

        //校验动作是否可做
//        if (null != prodRedisUtil.getAssociation(ProductionConstans.OPERATION_PREFIX, getLineId(), key))
//            return AjaxResult.error("1分钟内请勿重复操作该动作！");
        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");

/*        //构建管控端接口参数
        JSONArray procList = new JSONArray();*/
        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .build();

/*        //构建操作记录参数主体
        JSONObject op = new JSONObject();
        op.put("opid", operationTrace.getId());
        op.put("line", getLineId());//todo 后续自动
        op.put("cmd", "dockx2line");//
        op.put("proclist", procList);//工单列表参数*/

        //管控端处理url
        String iotUrl = "";
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();
        //发送请求
        String result = null;
        try {
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();

        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            //operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        }
//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());
        return AjaxResult.success(operationVo);
    }

    @Deprecated
    @ApiOperation("预调台->立库接驳台")
    @PostMapping("bench2dock0")
    public AjaxResult bench2dock0() {
        String key = OpCmdEnum.BOXGO.getCode();

        //校验动作是否可做
//        if (null != prodRedisUtil.getAssociation(ProductionConstans.OPERATION_PREFIX, getLineId(), key))
//            return AjaxResult.error("1分钟内请勿重复操作该动作！");
        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");
        //构建管控端接口参数
        JSONArray procList = new JSONArray();
        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);


        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .build();

/*        //构建操作记录参数主体
        JSONObject op = new JSONObject();
        op.put("opid", operationTrace.getId());
        op.put("line", getLineId());
        op.put("cmd", "bench2dock0");//
        op.put("proclist", procList);//工单列表参数*/

        //管控端处理url
        String iotUrl = "";
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();
        //发送请求
        String result = null;
        try {
            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            //operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        }

//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());
        return AjaxResult.success(operationVo);
    }

    @Deprecated
    @ApiOperation("立库接驳台->预调台")
    @PostMapping("dock02bench")
    public AjaxResult dock02bench() {
        String key = OpCmdEnum.BOXCOME.getCode();

        //校验动作是否可做
//        if (null != prodRedisUtil.getAssociation(ProductionConstans.OPERATION_PREFIX, getLineId(), key))
//            return AjaxResult.error("1分钟内请勿重复操作该动作！");

        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");
        //构建管控端接口参数
        JSONArray procList = new JSONArray();

        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());//todo 后期从用户获取
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());//todo 后期待确认
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

/*        //构建操作记录参数主体
        JSONObject op = new JSONObject();
        op.put("opid", operationTrace.getId());
        op.put("line", getLineId());//todo 后续自动
        op.put("cmd", "dock02bench");//
        op.put("proclist", procList);//工单列表参数*/

        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .build();


        //管控端处理url
        String iotUrl = "";
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();
        //发送请求
        String result = null;
        try {
            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            //operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        }

//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());
        return AjaxResult.success(operationVo);
    }

    @ApiOperation("预调台->预调室产线接驳台")
    @PostMapping("bench2dockx")
    @CheckIOTAutoProcess
    //@Transactional//托盘位 和 工单号
    public AjaxResult bench2dockx(@RequestBody TrayInfoVo trayInfoVo) {
        String key = OpCmdEnum.TODOCK.getCode();

        //此接口如果是物料，前端传的fillerType也是0，需要格外注意参数
        String operationId = trayInfoVo.getOperationId();
//        String trayPosition = dto.getTrayPosition();
        Integer fillerType = trayInfoVo.getFillerType() == null ? StorageFillerTypeEnum.PART.getCode() : trayInfoVo.getFillerType();

        //校验动作是否可做
//        if (!redisService.tryLock(ProductionConstans.OPERATION_PREFIX + getLineId(), key, 60, -1))
//            return AjaxResult.error("1分钟内请勿重复操作该动作！");
        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");

        //校验-------
        // 工单是否存在
        ProdWorkOrder workOrder = workOrderService.getEntityByOperationId(operationId);
        if (null != operationId && null == workOrder)
            throw new ServiceException("未查询到工单信息:" + operationId);
        //工单状态是否正确
        if (null != workOrder && workOrder.getStatus() > 2)
            throw new ServiceException("当前工单->" + workOrder.getTicketNumber() + "状态为->" + WorkOrderStatusEnum.getInfoByKey(workOrder.getStatus()) + ",不允许装调！");

        //校验 结束-------


        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTrace.setOperationData(JSON.toJSONString(trayInfoVo));
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

        //新的操作接口参数
        ToDockVo toDockVo = ToDockVo.builder()
                .to(trayInfoVo.getTag())
                .obj(trayInfoUtils.transferIotObj(getLineId(), trayInfoVo))
                .build();
        //添加机床字段
//        if (StorageFillerTypeEnum.PART.getCode() == trayInfoVo.getFillerType()) {
//            toDockVo.getObj().getJSONObject("part").put("machid", workOrder.getEqpCode());
//        }
        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .data(toDockVo)
                .build();

        //管控端处理url
        String iotUrl = "";
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();
        //发送请求
        String result = null;
        try {
            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            //operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        }
        //更新工单信息为已装调 && 添加工单托盘信息
        if (null != operationId) {
            workOrder.setBz1(trayInfoVo.getTag());
            workOrder.setStatus(WorkOrderStatusEnum.LOAD.getCode());
            workOrderService.updateWorkOrderWithStatus(workOrder);
        }
//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());
//        prodRedisUtil.setAssociation(getLineId(), key, operationTrace.getId());


        return AjaxResult.success(operationVo);
    }

    @ApiOperation("产线接驳台->预调台")
    @PostMapping("dockx2bench")
    @CheckIOTAutoProcess
    //@Transactional//只给托盘位置
    public AjaxResult dockx2bench(@RequestBody TrayInfoVo trayInfoVo) {

        String key = OpCmdEnum.TOBENCH.getCode();
        //校验动作是否可做
//        if (null != prodRedisUtil.getAssociation(ProductionConstans.OPERATION_PREFIX, getLineId(), key))
//            return AjaxResult.error("1分钟内请勿重复操作该动作！");

        //校验指令是否存在
        BaseProcess baseProcess = baseProcessService.getEntityByProcessCode(0l, key);
        if (baseProcess == null)
            return AjaxResult.error("未查询到 " + key + " 指令流程信息");

        //生成主操作记录
        ProdOperationTrace operationTrace = new ProdOperationTrace();
        operationTrace.setProcessId(baseProcess.getId());
        operationTrace.setCmd(key);
        operationTrace.setLineId(getLineId());
        operationTrace.setCreateTime(new Date());
        operationTrace.setStatus(OperationStatusEnum.WAIT.getCode());
        operationTrace.setRemark(OperationStatusEnum.WAIT.getInfo());
        operationTrace.setOperationData(JSON.toJSONString(trayInfoVo));
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

        //新的操作接口参数
        ToBenchVo toBenchVo = ToBenchVo.builder()
                .from(trayInfoVo.getTag())
                .obj(trayInfoUtils.transferIotObj(getLineId(), trayInfoVo))
                .build();
        OperationVo operationVo = OperationVo.builder()
                .opid(operationTrace.getId())
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .cmd(key)
                .data(toBenchVo)
                .build();

        //管控端处理url
        String iotUrl = "";
        //查询管控端接口
        iotUrl = platformApiService.getEntityByCode("iot_newop").getUrl();
        //发送请求
        String result = null;
        try {
            result = HttpRequest.post(iotUrl)
                    .body(JSON.toJSONString(operationVo))
                    .timeout(3000)
                    .execute().body();
            log.info("****************打印调用参数》》》 " + JSON.toJSONString(operationVo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("控制系统<下操作接口>调用失败，请检查网络是否畅通！");
        }
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
//            operationTraceService.deleteOperation(operationTrace);
            operationTrace.setStatus(OperationStatusEnum.ERROR.getCode());
            operationTrace.setRemark(jsonObject.getString("message"));
            operationTraceService.saveOrUpdateOperationTrace(operationTrace);
            return AjaxResult.error(jsonObject.getString("message"));
        }
//        prodRedisUtil.setAssociation(prodRedisUtil.contractKey(ProductionConstans.OPERATION_PREFIX, getLineId(), key), operationTrace.getId());

        return AjaxResult.success(operationVo);
    }

}
