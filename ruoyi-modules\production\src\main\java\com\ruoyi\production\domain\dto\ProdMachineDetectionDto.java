package com.ruoyi.production.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProdMachineDetectionDto {

    /*  工单表字段  */
    @ApiModelProperty(value = "工序号")
    private String schedulCode;
    @ApiModelProperty(value = "任务号")
    private String billCode;
    @ApiModelProperty(value = "机床号")
    private String eqpCode;
    @ApiModelProperty(value = "工单状态")
    private String woStatus;
    @ApiModelProperty(value = "真实加工机床号")
    private String realEqp;

    /*  机内检测*字段  */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private String updateBy;
}
