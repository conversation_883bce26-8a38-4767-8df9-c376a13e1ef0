<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi</artifactId>
        <version>3.5.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>ruoyi-auth</artifactId>
	
    <description>
        ruoyi-auth认证授权中心
    </description>
    
    <dependencies>
        
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
		
        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
		
        <!-- RuoYi Common Security-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-security</artifactId>
        </dependency>
        
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.3.6</version>
                <configuration>
                    <repository>${project.artifactId}</repository>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
            <!--            Docker构建配置信息 begin 岳琪版本-->
<!--            <plugin>-->
<!--                &lt;!&ndash;                <groupId>com.spotify</groupId>&ndash;&gt;-->
<!--                <groupId>io.fabric8</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>0.33.0</version>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash;这一部分是为了实现对远程docker容器的控制&ndash;&gt;-->
<!--                    &lt;!&ndash;docker主机地址,用于完成docker各项功能,注意是tcp不是http!&ndash;&gt;-->
<!--                    <dockerHost>tcp://*************:2375</dockerHost>-->
<!--                    <images>-->
<!--                        <image>-->
<!--                            <name>${docker.registry}/${project.artifactId}:${project.modelVersion}</name>-->
<!--                            <registry>${docker.registry}</registry>-->
<!--                            <build>-->
<!--                                &lt;!&ndash; 指定dockerfile文件的位置&ndash;&gt;-->
<!--                                <dockerFile>${project.basedir}/src/main/docker/Dockerfile</dockerFile>-->
<!--                                &lt;!&ndash;                                <buildOptions>&ndash;&gt;-->
<!--                                &lt;!&ndash;                                    &lt;!&ndash; 网络的配置，与宿主主机共端口号&ndash;&gt;&ndash;&gt;-->
<!--                                &lt;!&ndash;                                    <network>host</network>&ndash;&gt;-->
<!--                                &lt;!&ndash;                                </buildOptions>&ndash;&gt;-->
<!--                            </build>-->
<!--                        </image>-->
<!--                    </images>-->
<!--                    <buildArgs>-->
<!--                        &lt;!&ndash; dockerfile参数，指定jar路径 &ndash;&gt;-->
<!--                        <JAR_FILE>${project.build.directory}/${project.build.finalName}.jar</JAR_FILE>-->
<!--                    </buildArgs>-->
<!--                </configuration>-->
<!--            </plugin>-->

        </plugins>
    </build>
   
</project>
