package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.ProdOperationTraceDetail;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDetailDto;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceDetailVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-05-30
 */
public interface IProdOperationTraceDetailService extends IService<ProdOperationTraceDetail> {

    void saveDetailByOperation(ProdOperationTrace operationTrace);

    void deleteDetailByOperation(ProdOperationTrace operationTrace);

    List<ProdOperationTraceDetailVo> getDetailList(ProdOperationTraceDetailDto traceDetailDto);

//    Boolean checkOpreationLegal();
}
