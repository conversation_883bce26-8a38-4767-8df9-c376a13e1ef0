apiVersion: apps/v1
kind: Deployment
metadata:
  name: 695-auth
  namespace: "695"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: 695-auth
  template:
    metadata:
      labels:
        app: 695-auth
    spec:
      containers:
        - name: 695-auth
          image: ************/695/ruoyi-auth:latest
          env:
          - name: NACOS_HOST
            value: "nacos-headless.nacos"
          - name: REDIS_HOST
            value: "svc-redis.dev"
          - name: REDIS_PWD
            value: "123456"
          ports:
            - containerPort: 9200
      imagePullSecrets:
        - name: harbor-login
---

apiVersion: v1
kind: Service
metadata:
  name: svc-695-auth
  namespace: "695"
spec:
  selector:
    app: 695-auth
  type: NodePort
  ports:
    - port: 9200
      protocol: TCP
      targetPort: 9200
      nodePort: 30920
