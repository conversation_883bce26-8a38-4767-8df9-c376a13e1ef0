package com.ruoyi.message.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.message.domian.MessageGroup;
import com.ruoyi.message.domian.vo.MessageGroupButtonVo;
import com.ruoyi.message.mapper.MessageGroupMapper;
import com.ruoyi.message.service.IMessageGroupService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Service
public class MessageGroupServiceImpl extends ServiceImpl<MessageGroupMapper, MessageGroup> implements IMessageGroupService {

    @Override
    public List<MessageGroupButtonVo> getAll() {
        QueryWrapper queryWrapper = new QueryWrapper();
        return this.baseMapper.selectList(queryWrapper);
    }
}
