package com.ruoyi.production.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.core.observable.AutoProcessEvent;
import com.ruoyi.production.domain.BaseFillerType;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.SysPlatformApi;
import com.ruoyi.production.domain.dto.equipment.AutoProcessDto;
import com.ruoyi.production.domain.dto.equipment.UpdateProcessingDto;
import com.ruoyi.production.domain.vo.TrayInfoVo;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.enums.LHStorageFillerTypeEnum;
import com.ruoyi.production.enums.StorageFillerTypeEnum;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IBaseFillerTypeService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.utils.RabbitMsgUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("equipment")
@Api(value = "设备controller", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class BaseEquipmentController extends BaseModuleController {

    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private IBaseFillerTypeService fillerTypeService;
    @Autowired
    private RabbitMsgUtils rabbitMsgUtils;

    @GetMapping("getTopStatusList")
    @ApiOperation("获取公共设备状态列表")
    public AjaxResult getTopStatusList() {
        //获取产线id
        Long lineId = getLineId();
        List<BaseEquipment> showEqpList = new ArrayList<>();
        //查询预调室移栽机器人状态
        BaseEquipment eqpParam = new BaseEquipment();
        eqpParam.setLineId(11L);
        eqpParam.setType(EqpTypeEnum.BOT.getCode());
        List<BaseEquipment> eqpList = equipmentService.listByCondition(eqpParam);
        showEqpList.addAll(eqpList);
        //查询产线线机器人状态
        eqpParam.setLineId(getLineId());
        eqpParam.setType(EqpTypeEnum.BOT.getCode());
        eqpList = equipmentService.listByCondition(eqpParam);
        showEqpList.addAll(eqpList);
        //查询公共agv状态
        eqpParam.setLineId(11L);
        eqpParam.setType(EqpTypeEnum.AGV.getCode());
        eqpList = equipmentService.listByCondition(eqpParam);
        showEqpList.addAll(eqpList);
        //查询产线agv状态
        eqpParam.setLineId(getLineId());
        eqpParam.setType(EqpTypeEnum.AGV.getCode());
        eqpList = equipmentService.listByCondition(eqpParam);
        showEqpList.addAll(eqpList);
        return AjaxResult.success(showEqpList);
    }

    @PostMapping("botAction/{action}/{eqpId}")
    @ApiOperation("机器人动作")
    public AjaxResult botAction(@PathVariable("action") @ApiParam("启动：start；急停：stop；重置：reset") String action,
                                @PathVariable("eqpId") @ApiParam("设备ID") Long eqpId) {
        JSONObject requestParam = new JSONObject();
        requestParam.put("type", action);
        String apiCode, url, result = "";
        //查询动作地址
        apiCode = "iot_bot_" + eqpId;
        url = Optional.ofNullable(platformApiService.getEntityByCode(apiCode))
                .map(SysPlatformApi::getUrl)
                .orElseThrow(() -> new ServiceException("未能获取到BOT接口地址信息，请检查是否配置"));
//        url = platformApiService.getEntityByCode(apiCode).getUrl();
        //发送请求
        result = HttpRequest.post(url)
                .body(requestParam.toJSONString())
                .timeout(3000)
                .execute().body();

        JSONObject jsonObject = JSON.parseObject(result);

        if (!"0".equals(jsonObject.getString("code")))
            throw new ServiceException(jsonObject.toJSONString());

        return AjaxResult.success(result);
    }

    @PostMapping("agvAction/{action}/{eqpId}")
    @ApiOperation("AGV动作")
    public AjaxResult agvAction(@PathVariable("action") @ApiParam("启动：continue；暂停：stop") String action,
                                @PathVariable("eqpId") @ApiParam("设备ID") Long eqpId) {
        BaseEquipment baseEquipment = equipmentService.getById(eqpId);

        String apiCode, url, result = "";
        //查询动作地址
        apiCode = "iot_agv";
        url = Optional.ofNullable(platformApiService.getEntityByCode(apiCode))
                .map(SysPlatformApi::getUrl)
                .orElseThrow(() -> new ServiceException("未能获取到AGV接口地址信息，请检查是否配置"));
//        url = platformApiService.getEntityByCode(apiCode).getUrl();
        //发送请求
        url = url + baseEquipment.getEqpCode() + "/" + action;
        result = HttpRequest.get(url)
                .timeout(3000)
                .execute().body();

        JSONObject jsonObject = JSON.parseObject(result);

        if (!"0".equals(jsonObject.getString("code")))
            throw new ServiceException(jsonObject.toJSONString());

        return AjaxResult.success(result);
    }

    @ApiOperation("产线确认")
    @PostMapping("confirm/{action}")
    public AjaxResult confirm(@PathVariable("action") @ApiParam("启动：enable；停止：disable") String action) {
        //获取产线id
        Long lineId = getLineId();
        //设置管控系统为自动流程
        //查询动作地址
        String url = platformApiService.getEntityByCode("iot_" + lineId + "_startflow").getUrl();
        //发送请求
        String result = HttpRequest.get(url)
                .timeout(3000)
                .execute().body();
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            throw new ServiceException(jsonObject.toJSONString());
        }
        return AjaxResult.success(result);
    }

    @ApiOperation("系统启动")
    @PostMapping("startSystem")
    public AjaxResult startSystem() {
        applicationContext.publishEvent(new AutoProcessEvent(this, getLineId()));
        return AjaxResult.success();
    }

    @ApiOperation("获取产线托盘数据")
    @GetMapping("getTrayInfo")
    public AjaxResult getTrayInfo() {

        //获取产线id
        Long lineId = getLineId();

        JSONObject requestParam = new JSONObject();
        //查询动作地址
        List<TrayInfoVo> list = new ArrayList<>();

        String actionUrl = platformApiService.getEntityByCode("iot_line_tray_info").getUrl();
        requestParam.put("line", lineId);
        String result = HttpRequest.post(actionUrl).timeout(3000)
                .body(requestParam.toJSONString())
                .execute().body();
        JSONArray trayResult = JSON.parseArray(result);

        for (int i = 0; i < trayResult.size(); i++) {
            TrayInfoVo trayInfoVo = new TrayInfoVo();
            JSONObject jsonObject = trayResult.getJSONObject(i);
            if (null == jsonObject) {
                trayInfoVo.setFillerType(StorageFillerTypeEnum.EMPTY.getCode());
            } else {
                String operationId = jsonObject.getString("proc-id") == null ? null : jsonObject.getString("proc-id");
                if (StringUtils.isNotBlank(operationId)) {
                    ProdWorkOrder workOrder = orderService.getEntityByOperationId(operationId);
                    trayInfoVo.setBillCode(workOrder.getBillCode());
                    trayInfoVo.setSchedulCode(workOrder.getSchedulCode());
                    trayInfoVo.setMatName(workOrder.getMatName());
                    trayInfoVo.setTicketNumber(workOrder.getTicketNumber());
                    trayInfoVo.setWorkOrderStatus(workOrder.getStatus());
                }
                //trayInfoVo.setFillerType(StorageFillerTypeEnum.getInfoByValue(jsonObject.getString("proc-type")));
                trayInfoVo.setFillerType(LHStorageFillerTypeEnum.getInfoByValue(jsonObject.getString("proc-type")));
            }
            trayInfoVo.setTrayPosition(i + 1);
            list.add(trayInfoVo);
        }

        //接口测试
/*        TrayInfoVo trayInfoVo = new TrayInfoVo();
        trayInfoVo.setFillerType(StorageFillerTypeEnum.PART.getCode());
        trayInfoVo.setBillCode("2022-05");
        trayInfoVo.setSchedulCode("66");
        trayInfoVo.setMatName("阀体");
        trayInfoVo.setTicketNumber("100063176-01_20_01");
        trayInfoVo.setWorkOrderStatus(WorkOrderStatusEnum.WAIT.getCode());
        list.add(trayInfoVo);

        TrayInfoVo v1 = new TrayInfoVo();
        v1.setFillerType(2);
        list.add(v1);*/

        list.forEach(vo -> {
            BaseFillerType baseFillerType = fillerTypeService.getByFillerType(getLineId(), vo.getFillerType());
            if (null != baseFillerType)
                vo.setFillerTypeName(baseFillerType.getFillerName());
//            Integer code = vo.getFillerType();
//            vo.setFillerTypeName(StorageFillerTypeEnum.getInfoByKey(code));

            Integer code = vo.getWorkOrderStatus();
            vo.setWorkOrderStatusName(WorkOrderStatusEnum.getInfoByKey(code));
        });

        return AjaxResult.success(list);
    }

    @ApiOperation("根据参数获取设备信息")
    @PostMapping("listByCondition")
    public AjaxResult listByCondition(@RequestBody BaseEquipment equipment) {
        //获取产线id
        equipment.setLineId(getLineId());
        return AjaxResult.success(equipmentService.listByCondition(equipment));
    }

    @ApiOperation("自动加工开启/关闭")
    @PutMapping("autoProcess")
    public AjaxResult updateAutoProcess(@Validated @RequestBody AutoProcessDto dto) {
        equipmentService.autoProcess(dto);
        rabbitMsgUtils.updateProcessTraceList(getLineId());
        return AjaxResult.success();
    }

    @ApiOperation("(状态回退)强制解除设备工单绑定")
    @PutMapping("updateProcessing")
    public AjaxResult updateProcessing(@Validated @RequestBody UpdateProcessingDto dto) {
        equipmentService.updateProcessing(dto);
        rabbitMsgUtils.updateProcessTraceList(getLineId());
        return AjaxResult.success();
    }

    @ApiOperation("iot自动流程开/关控制")
    @PutMapping("iotAutoProcess/{switchOn}")
    public AjaxResult iotAutoProcess(@PathVariable("switchOn") Boolean switchOn) {
        SysPlatformApi api = platformApiService.getEntityByCode("iot_flowCtl_" + getLineId());
        if (null == api)
            throw new ServiceException("未查询到控制系统流程开关接口url");
        JSONObject param = new JSONObject();
        if (!switchOn)
            param.put("ctl", "start");
        else
            param.put("ctl", "stop");

        String response = HttpRequest.post(api.getUrl())
                .body(param.toJSONString())
                .timeout(3000)
                .execute().body();
        redisService.setCacheObject("liaohui:flowStatus:" + getLineId(), !switchOn);
        rabbitMsgUtils.updateIOTAutoProcess(getLineId());
        log.info("iot自动流程开/关控制-->" + response);
        return AjaxResult.success(!switchOn);
    }

    @ApiOperation("iot自动流程状态获取")
    @GetMapping("getIotAutoProcess")
    public AjaxResult getIotAutoProcess() {
        Boolean flowStatus = redisService.getCacheObject("liaohui:flowStatus:" + getLineId());
        if (null == flowStatus)
            flowStatus = true;
        return AjaxResult.success(!flowStatus);
    }

    /* ********* InnerAuth ********* */

    @InnerAuth
    @GetMapping("getEquipmentByMqttAlias/{mqttAlias}")
    public R<BaseEquipment> getEquipmentByMqttAlias(@PathVariable("mqttAlias") String mqttAlias) {
        //获取产线id
        BaseEquipment equipment = new BaseEquipment();
        equipment.setMqttAlias(mqttAlias);
        return R.ok(equipmentService.getEquipmentByMqttAlias(mqttAlias));
    }

    public static void main(String[] args) {
        SysPlatformApi api = SysPlatformApi.builder().url("1").build();
        String url;
        url = Optional.ofNullable(api)
                .map(SysPlatformApi::getUrl)
                .orElseThrow(() -> new ServiceException("未能获取到接口地址信息，请检查是否配置"));
        System.out.println(url);
    }

}