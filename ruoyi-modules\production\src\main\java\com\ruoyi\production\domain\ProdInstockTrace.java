package com.ruoyi.production.domain;


import io.swagger.annotations.ApiModel;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-05-19
 */
@ApiModel(value = "ProdInstockTrace对象", description = "")
public class ProdInstockTrace implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String instockReqId;

    private String instockOpId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInstockReqId() {
        return instockReqId;
    }

    public void setInstockReqId(String instockReqId) {
        this.instockReqId = instockReqId;
    }

    public String getInstockOpId() {
        return instockOpId;
    }

    public void setInstockOpId(String instockOpId) {
        this.instockOpId = instockOpId;
    }
}
