package com.ruoyi.production.enums;

public enum OrderTraceStatusEnum {

    ONGO(0, "执行中"),
    DONE(1, "完成"),
    STOP(2, "错误");

    private final Integer code;
    private final String info;

    OrderTraceStatusEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(Integer code) {
        for (OrderTraceStatusEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
