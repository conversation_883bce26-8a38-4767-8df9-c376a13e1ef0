package com.ruoyi.production.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.BaseLine;
import com.ruoyi.production.mapper.BaseLineMapper;
import com.ruoyi.production.service.IBaseLineService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Service
public class BaseLineServiceImpl extends ServiceImpl<BaseLineMapper, BaseLine> implements IBaseLineService {
    @Override
    public List<BaseLine> listByCondition(BaseLine baseLine) {
        if (null == baseLine)
            return null;

        QueryWrapper<BaseLine> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(baseLine.getLineCode()))
            queryWrapper.eq("line_code", baseLine.getLineCode());
        if (StringUtils.isNotBlank(baseLine.getId() + ""))
            queryWrapper.eq("id", baseLine.getId());

        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public BaseLine getEntityByCode(String code) {
        if (StringUtils.isBlank(code))
            return null;
        QueryWrapper<BaseLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("line_code", code);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public BaseLine getEntityByAlias(String alias) {
        QueryWrapper<BaseLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("alias", alias);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public List<BaseLine> getDistinctLineList() {
        QueryWrapper<BaseLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct *");
        return this.getBaseMapper().selectList(queryWrapper);
    }
}
