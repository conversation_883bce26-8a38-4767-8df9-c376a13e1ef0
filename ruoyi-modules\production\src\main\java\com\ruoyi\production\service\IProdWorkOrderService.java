package com.ruoyi.production.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.domain.vo.detection.DetectionReportVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface IProdWorkOrderService extends IService<ProdWorkOrder> {

    /**
     * 根据条件查询信息
     *
     * @param prodWorkOrderDto
     * @return
     */
    List<ProdWorkOrderVo> listByCondition(ProdWorkOrderDto prodWorkOrderDto);

    /**
     * 根据条件查询信息
     *
     * @return
     */
    List<Object> getOperationIdList(ProdWorkOrder prodWorkOrder);

    /**
     * 根据条件查询信息
     *
     * @return
     */
    List<ProdWorkOrder> getEntityListByParam(ProdWorkOrderDto prodWorkOrderDto);

    /**
     * 根据条件查询信息
     *
     * @return
     */
    List<ProdWorkOrderVo> getTotalList(ProdWorkOrder prodWorkOrder);

    List<ProdWorkOrderVo> getProcessTraceList(ProdWorkOrder prodWorkOrder);

    List<Object> getBillCodeList(ProdWorkOrder prodWorkOrder);

    List<Object> getDoneBillCodeList(ProdWorkOrder prodWorkOrder);

    List<Object> getSchedulCodeList(String billcode);

    ProdWorkOrder getEntityByOperationId(String operationId);

    ProdWorkOrderVo getEntityByParam(ProdWorkOrderDto prodWorkOrderDto);

    IPage<ProdWorkOrderVo> listForPage(ProdWorkOrderDto prodWorkOrderDto, Integer current, Integer pageSiz);

    IPage<ProdWorkOrderVo> listForPageToDock(ProdWorkOrderDto prodWorkOrderDto, Integer current, Integer pageSiz);

    IPage<ProdWorkOrderVo> pageList(ProdWorkOrderDto prodWorkOrderDto, Integer current, Integer pageSiz);

    void updateCompleteByOperationId(String operationId);

    List<ProdWorkOrder> getListByOperationId(String operationId);

    Integer deletedByBillSchedulCode(ProdWorkOrderDto prodWorkOrderDto);

    void orderStatusStepForward(String operationId);

    void orderStatusStepRollback(String operationId);

    void updateWorkOrderWithStatus(ProdWorkOrder workOrder);

    void forceDoneWorkOrder(ProdWorkOrder order);

    List<DetectionReportVo> getDetectionReportData(ProdWorkOrder prodWorkOrder);

    IPage<DetectionReportVo> listForPageDetectionReport(ProdWorkOrderDto prodWorkOrder,Integer current, Integer pageSiz);

    List<ProdWorkOrderVo> apiGetWorkOrderList(ProdWorkOrderDto prodWorkOrderDto);


//    List<ProdWorkOrderVo> getWorkOrderListByParams(Long lineId);
}
