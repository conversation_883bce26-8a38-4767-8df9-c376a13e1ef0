<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProdOperationTraceMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.ProdOperationTrace">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
        <result column="line_id" property="lineId"/>
        <result column="line_code" property="lineCode"/>
        <result column="process_id" property="processId"/>
        <result column="process_code" property="processCode"/>
        <result column="cmd" property="cmd"/>
        <result column="status" property="status"/>
        <result column="stop_reason" property="stopReason"/>
        <result column="current_action" property="currentAction"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        remark, line_id, line_code, process_id, process_code, cmd, status, stop_reason, current_action
    </sql>

    <select id="listByCondition" resultType="com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo">
        select ot.*, bp.process_name
        from prod_operation_trace ot
        left join base_process bp on ot.process_id = bp.id
        <where>
            ot.deleted = 0
            <if test="params.toEqpId != null">
                and ot.to_eqp_id = #{params.toEqpId}
            </if>
            <if test="params.fromEqpId != null">
                and ot.from_eqp_id = #{params.fromEqpId}
            </if>
            <if test="params.lineId != null">
                and ot.line_id = #{params.lineId}
            </if>
            <if test="params.processId != null">
                and ot.process_id = #{params.processId }
            </if>
            <if test="params.endFlag != null and params.endFlag==0">
                and ot.status not in ('done','error','unknow')
            </if>
            <if test="params.endFlag != null and params.endFlag==1">
                and ot.status in ('done','error','unknow')
            </if>
        </where>
        order by ot.create_time desc
    </select>

    <select id="listForPage" resultType="com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo">
        select ot.*, bp.process_name
        from prod_operation_trace ot
        left join base_process bp on ot.process_id = bp.id
        <!--            where ot.status != 'done'-->
        <where>
            ot.deleted = 0
            <if test="params.timeStart != null">
                and ot.create_time &gt;= #{params.timeStart}
            </if>
            <if test="params.timeEnd != null">
                and ot.create_time &lt;= #{params.timeEnd}
            </if>
            <if test="params.lineId != null">
                and ot.line_id = #{params.lineId}
            </if>
        </where>
        order by ot.create_time desc
    </select>

    <select id="logInfoListForPage" resultType="com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo">
        select ot.*, bp.process_name
        from prod_operation_trace ot
        left join base_process bp on ot.process_id = bp.id
        <!--            where ot.status != 'done'-->
        <where>
            ot.deleted = 0 and ot.status='error'
            <if test="params.timeStart != null">
                and ot.create_time &gt;= #{params.timeStart}
            </if>
            <if test="params.timeEnd != null">
                and ot.create_time &lt;= #{params.timeEnd}
            </if>
        </where>
        order by ot.create_time desc
    </select>
</mapper>
