package com.ruoyi.message.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.message.api.domain.MessageMqRecord;
import com.ruoyi.message.api.factory.RemoteMessageNewsFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteMessageMqRecordService", value = ServiceNameConstants.MESSAGE_SERVICE, fallbackFactory = RemoteMessageNewsFallbackFactory.class)
public interface RemoteMessageMqRecordService {

    @PostMapping("/mq/add")
    public R<Boolean> add(@RequestBody MessageMqRecord mqRecord, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/mq/list")
    public R<List<MessageMqRecord>> list(@RequestBody MessageMqRecord mqRecord, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/mq/update")
    public R<Boolean> update(@RequestBody MessageMqRecord mqRecord, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/mq/updateByMesgId")
    public R<Integer> updateByMesgId(@RequestBody MessageMqRecord mqRecord, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/mq/getByMesgId/{msgId}")
    public R<MessageMqRecord> getByMesgId(@PathVariable("msgId") String msgId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
