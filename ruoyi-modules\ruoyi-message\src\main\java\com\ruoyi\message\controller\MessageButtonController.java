package com.ruoyi.message.controller;


import com.ruoyi.common.core.domain.R;
import com.ruoyi.message.domian.vo.MessageGroupButtonVo;
import com.ruoyi.message.service.IMessageButtonService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("button")
public class MessageButtonController extends BaseModuleController {

    @Autowired
    private IMessageButtonService buttonService;

    @ApiOperation("根据组id查询按钮")
    @GetMapping("getlistByGroupId/{id}")
//    @Cacheable(value = "message:buttongroup", key = "#id")
    public R<List<MessageGroupButtonVo>> getButtonsByGroupId(@PathVariable("id") Long id) {
        return R.ok(buttonService.getButtonsByGroupId(id));
    }

}
