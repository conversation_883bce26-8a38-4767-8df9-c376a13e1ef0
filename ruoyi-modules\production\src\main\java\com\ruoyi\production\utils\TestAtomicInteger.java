package com.ruoyi.production.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * AtomicInteger和Integer区别
 * AtomicInteger可保证多线程中保持原子操作,适用于在多线程中的计算
 * <AUTHOR>
 */
public class TestAtomicInteger {

    static Integer result = 0;

    static AtomicInteger ai = new AtomicInteger();

    public static void main(String[] args) throws InterruptedException, ExecutionException {
        List<FutureTask<Integer>> ftList = new ArrayList<FutureTask<Integer>>();
        for (int i = 0; i < 10000; i++) {
            FutureTask<Integer> ft = new FutureTask<Integer>(new Callable<Integer>() {
                @Override
                public Integer call() throws Exception {
                    result+= 1;
                    return ai.incrementAndGet();
                }
            });
            new Thread(ft).start();
            ftList.add(ft);
        }
        for (int i = 0; i < ftList.size(); i++) {
            ftList.get(i).get();//保证所有的线程执行完成
        }
        System.out.println("result====="+result);
        System.out.println("ai===="+ai.get());
    }

}
