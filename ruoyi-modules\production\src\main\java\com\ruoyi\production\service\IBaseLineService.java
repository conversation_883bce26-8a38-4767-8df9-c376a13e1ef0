package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.BaseLine;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface IBaseLineService extends IService<BaseLine> {
    /**
     * 根据条件查询信息
     *
     * @param baseEquipment
     * @return
     */
    List<BaseLine> listByCondition(BaseLine baseEquipment);

    BaseLine getEntityByCode(String code);

    BaseLine getEntityByAlias(String alias);

    List<BaseLine> getDistinctLineList();
}
