package com.ruoyi.production.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.ProdMachineDetectionDetail;
import com.ruoyi.production.domain.dto.detection.AddDetailColumnDto;
import com.ruoyi.production.domain.dto.detection.DeleteDetailColumnDto;
import com.ruoyi.production.domain.dto.detection.QualityReportDataDto;
import com.ruoyi.production.domain.vo.detection.QualityReportColumnDataVo;
import com.ruoyi.production.domain.vo.detection.QualityReportDataVo;
import com.ruoyi.production.service.IProdMachineDetectionDetailService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("machineDetectionDetail")
public class ProdMachineDetectionDetailController extends BaseModuleController {

    @Autowired
    private IProdMachineDetectionDetailService detailService;

    @ApiOperation("获取机内检测信息")
    @PostMapping("listByCondition")
    public AjaxResult listByCondition(@RequestBody ProdMachineDetectionDetail detail) {
        return AjaxResult.success(detailService.listByCondition(detail));
    }

    @ApiOperation("分页查询")
    @PostMapping("listForPage")
    public AjaxResult listForPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdMachineDetectionDetail detail) {
        return AjaxResult.success(detailService.listForPage(detail, pageNum, pageSize));
    }

    @ApiOperation("批量保存")
    @PostMapping("updateBatch")
    public AjaxResult updateBatch(@RequestBody List<ProdMachineDetectionDetail> detailList) {
        for (ProdMachineDetectionDetail detail : detailList) {
            detail.setUpdateBy(getSysUser().getNickName());
            detail.setVerifyTime(new Date());
        }
        return AjaxResult.success(detailService.updateDetectionDetail(detailList));
    }

    @GetMapping("/getColumns/{barcode}")
    public AjaxResult getQualityReportColumns(@PathVariable("barcode") String barcode) {
        return AjaxResult.success(detailService.getColumns(barcode));
    }

    @ApiOperation("获取工序记录表获取列名列表")
    @GetMapping("/getColumnsByParam")
    public AjaxResult getColumnsByParam(QualityReportDataDto dto) {
        return AjaxResult.success(detailService.getColumnsByParam(dto));
    }

    @GetMapping("/getQualityReportData")
    public AjaxResult getQualityReport(QualityReportDataDto dto,
                                       @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                       @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        QualityReportDataVo dataVo = detailService.getQualityReportData(dto);
        List<List<QualityReportColumnDataVo>> pageList = dataVo.getPageList();
        dataVo.setTotal(dataVo.getOrderVo().getQuanity());
        dataVo.setCurrent(pageNum);
        if (pageNum <= pageList.size())
            dataVo.setRecords(pageList.get(dataVo.getCurrent() - 1));
        dataVo.setPageList(null);
        return AjaxResult.success(dataVo);
    }

    //    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportQualityReport")
    public void exportQualityReport(HttpServletResponse response, @RequestBody QualityReportDataDto dto) {
        detailService.exportQualityReportExcel(response, dto);
    }

    @PostMapping("/batchSaveMetaData")
    public AjaxResult batchSave(@RequestBody List<ProdMachineDetectionDetail> detailList) {
        return AjaxResult.success(detailService.saveOrUpdateBatch(detailList));
    }

    @PostMapping("/addDetailColumn")
    public AjaxResult addDetailColumn(@Validated @RequestBody AddDetailColumnDto dto) {
        detailService.addDetailColumn(dto);
        return AjaxResult.success();
    }

    @DeleteMapping("/deleteDetailColumn")
    public AjaxResult deleteDetailColumn(@Validated @RequestBody DeleteDetailColumnDto dto) {
        return AjaxResult.success(detailService.deleteDetailColumn(dto));
    }

}
