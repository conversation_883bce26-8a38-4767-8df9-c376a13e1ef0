package com.ruoyi.auth.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * redisson配置
 *
 * <AUTHOR>
 */
@Configuration
public class RedissonConfig {

    @Autowired
    private Environment env;

    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient redissonClient() {
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer();
        singleServerConfig.setAddress("redis://" + env.getProperty("spring.redis.host") + ":" + env.getProperty("spring.redis.port"));
        singleServerConfig.setDatabase(Integer.parseInt(env.getProperty("spring.redis.database")));
        singleServerConfig.setPassword(env.getProperty("spring.redis.password"));
        singleServerConfig.setTimeout(10000);
        return Redisson.create(config);
//        Config config = new Config();
//        config.useSingleServer().setAddress("redis://" + host + ":" + port).setDatabase(8); // 更多.set
//        return Redisson.create(config);
    }
}