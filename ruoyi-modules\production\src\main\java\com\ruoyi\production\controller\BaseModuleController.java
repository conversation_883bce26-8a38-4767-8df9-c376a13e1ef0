package com.ruoyi.production.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.production.domain.BaseLine;
import com.ruoyi.production.domain.BasePreset;
import com.ruoyi.production.service.IBaseLineService;
import com.ruoyi.production.service.IBasePresetService;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.system.api.domain.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;

@Component
public class BaseModuleController {

    @Autowired
    protected TokenService tokenService;
    @Autowired
    protected RedisService redisService;

    @Autowired
    protected ISysPlatformApiService platformApiService;
    @Autowired
    protected IBasePresetService presetService;
    @Autowired
    protected IBaseLineService baseLineService;
    @Autowired
    protected ApplicationContext applicationContext;

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageUtils.startPage();
    }

    /**
     * 清理分页的线程变量
     */
    protected void clearPage() {
        PageUtils.clearPage();
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected AjaxResult getDataPage(List<?> list) {
        //TableDataInfo rspData = new TableDataInfo();
        //rspData.setItems(list);
        //rspData.setTotal(new PageInfo(list).getTotal());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        return AjaxResult.success(rspData);
    }

    protected SysUser getSysUser() {
        return tokenService.getLoginUser(ServletUtils.getRequest()).getSysUser();
    }


    protected Long getLineId() {
        return tokenService.getLoginUser(ServletUtils.getRequest()).getSysUser().getLineId();
    }

    protected BaseLine getBaseLine() {
        Long lineId = tokenService.getLoginUser(ServletUtils.getRequest()).getSysUser().getLineId();
        return baseLineService.getById(lineId);
    }

    protected BasePreset getPresetInfo() {
        Long presetId = tokenService.getLoginUser(ServletUtils.getRequest()).getSysUser().getPresetId();
        return presetService.getById(presetId);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }
}
