package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.ProdOrderMachRelation;
import com.ruoyi.production.domain.dto.ordermachconfig.OrderMachConfigDto;
import com.ruoyi.production.domain.vo.OrderMachConfigVo;
import com.ruoyi.production.mapper.ProdOrderMachRelationMapper;
import com.ruoyi.production.service.IProdOrderMachRelationService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@Service
public class ProdWorderMachRelationServiceImpl extends ServiceImpl<ProdOrderMachRelationMapper, ProdOrderMachRelation> implements IProdOrderMachRelationService {

    @Override
    public List<OrderMachConfigVo> getOrderMachConfig(OrderMachConfigDto dto) {
        List<OrderMachConfigVo> orderMachConfigVoList = this.baseMapper.getOrderMachConfig(dto);
        return orderMachConfigVoList.stream().filter(distinctByKey(OrderMachConfigVo::getEqpCode)).collect(Collectors.toList());
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void saveOrderMachConfig(OrderMachConfigDto dto) {
//        //删除原来机床配置
//        QueryWrapper<ProdOrderMachRelation> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("bill_code", dto.getBillCode());
//        queryWrapper.eq("schedul_code", dto.getSchedulCode());
//        this.remove(queryWrapper);
        //保存新配置
        List<ProdOrderMachRelation> list = new ArrayList<>();
        for (OrderMachConfigVo orderMachConfigVo : dto.getVoList()) {
            ProdOrderMachRelation relation = new ProdOrderMachRelation();
            relation.setId(orderMachConfigVo.getId());
            relation.setBillCode(dto.getBillCode());
            relation.setSchedulCode(dto.getSchedulCode());
            relation.setBillSchedulCode(relation.getBillCode()+"_"+relation.getSchedulCode());
            relation.setEqpId(orderMachConfigVo.getEqpId());
            relation.setEqpCode(orderMachConfigVo.getEqpCode());
            relation.setEnable(orderMachConfigVo.getEnable());
            relation.setLineId(dto.getLineId());
            list.add(relation);
        }
        this.saveOrUpdateBatch(list);
    }

    //distinctByKey自己定义
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
