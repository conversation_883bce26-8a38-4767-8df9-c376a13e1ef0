//package com.ruoyi.production.controller;
//
//
//import com.ruoyi.common.core.web.controller.BaseController;
//import com.ruoyi.common.core.web.domain.AjaxResult;
//import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
//import com.ruoyi.production.service.IBaseProcessService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * <p>
// * 前端控制器
// * </p>
// *
// * <AUTHOR> * @since 2022-04-26
// */
//
//@RestController
//@RequestMapping("process")
//@Api(value = "BaseProcessController", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//public class BaseProcessController extends BaseController {
//
//    @Autowired
//    private IBaseProcessService baseProcessService;
//
//
//
//
//
//
//}
