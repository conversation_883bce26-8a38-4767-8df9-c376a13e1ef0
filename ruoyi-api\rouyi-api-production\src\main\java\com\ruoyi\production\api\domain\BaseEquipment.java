package com.ruoyi.production.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Data
@ApiModel(value = "BaseEquipment对象", description = "")
public class BaseEquipment extends MyBaseEntity {

    private Integer aipId;

    @ApiModelProperty(value = "设备编码")
    private String eqpCode;

    @ApiModelProperty(value = "设备名称")
    private String eqpName;

    @ApiModelProperty(value = "设备类型")
    private Integer type;

    private Long lineId;

    @ApiModelProperty(value = "状态 0：在线；1：离线")
    private Integer status;

    @ApiModelProperty(value = "别名")
    private String alias;

    @ApiModelProperty(value = "设备数据")
    private String data;

    @ApiModelProperty(value = "mqtt别名")
    private String mqttAlias;

    @ApiModelProperty(value = "系统型号")
    private String eqpSystem;

    @ApiModelProperty(value = "自动加工")
    private Integer autoProcess;

    @ApiModelProperty(value = "加工中")
    private Integer processing;

    @ApiModelProperty(value = "正在加工工单")
    @TableField(value = "operation_id")
    private String operationId;

//    @Version
//    private Long version;
}
