package com.ruoyi.production.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.BaseProcess;
import com.ruoyi.production.domain.vo.BaseProcessVo;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface IBaseProcessService extends IService<BaseProcess> {

    /**
     * 根据条件查询信息
     *
     * @param baseProcess
     * @return
     */
    List<BaseProcessVo> listByCondition(BaseProcess baseProcess);

    List<BaseProcess> getProcessLinksByParentId(Long id);

    List<BaseProcess> getMainProcessLinksByLineId(Long lineId);

    BaseProcess getEntityByProcessCode(Long lineId,String code);

    BaseProcess getNextNode(Long lineId,String code);

    Boolean isBeforeInMach(Long lineId,String processCode);






}
