package com.ruoyi.production.core.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.production.utils.WebSocketServer;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;

//@Component
public class FanoutWebSocketListener {

    @Autowired
    private RemoteUserService remoteUserService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${spring.cloud.client.ip-address}:${server.port}-ws", durable = "false", autoDelete = "true"),
            exchange = @Exchange(value = "websocket_fanout_exchange", type = ExchangeTypes.FANOUT)))
    public void handleMessage(String data) {
        WebSocketVo webSocketVo = JSON.toJavaObject(JSON.parseObject(data), WebSocketVo.class);
        webSocketVo.setType("interface");

        SysUser sysUser = new SysUser();
        sysUser.setLineId(webSocketVo.getLineId());
        List<SysUser> userList = remoteUserService.listByCondition(sysUser, SecurityConstants.INNER).getData();
        for (SysUser user : userList) {
            try {
                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), user.getUserId() + "");
//                Thread.sleep(1000);
//                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), user.getUserId() + "");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
//        System.out.println(data);

    }
}
