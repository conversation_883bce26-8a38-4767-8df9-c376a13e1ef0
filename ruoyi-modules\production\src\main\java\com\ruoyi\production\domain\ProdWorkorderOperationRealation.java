package com.ruoyi.production.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-05-30
 */
@ApiModel(value = "ProdWorkorderOperationRealation对象", description = "")
public class ProdWorkorderOperationRealation extends MyBaseEntity {

    @ApiModelProperty("操作id")
    private Long operationId;

    @ApiModelProperty("工单id")
    private String workOrderId;

    public Long getOperationId() {
        return operationId;
    }

    public void setOperationId(Long operationId) {
        this.operationId = operationId;
    }

    public String getWorkOrderId() {
        return workOrderId;
    }

    public void setWorkOrderId(String workOrderId) {
        this.workOrderId = workOrderId;
    }
}
