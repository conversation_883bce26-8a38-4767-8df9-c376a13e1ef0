package com.ruoyi.production.core.aspect;

import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.production.core.observable.AutoProcessEvent;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.production.utils.SyncEqpStatusUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 分布式锁/防重复提交 aop
 */
@Aspect
@Component
@Slf4j
public class CheckIOTAutoProcessAop {

    @Autowired
    private ISysPlatformApiService apiService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private SyncEqpStatusUtil eqpStatusUtil;

    public CheckIOTAutoProcessAop() {
    }

    /**
     * 切点，拦截被 @ResubmitLock 修饰的方法
     */
    @Pointcut("@annotation(com.ruoyi.production.core.annotation.CheckIOTAutoProcess)")
    public void pointcut() {
    }

//    @Before("pointcut()")
//    public void before(JoinPoint joinPoint) {
//        Object[] args = joinPoint.getArgs();    //获取方法入参
//        System.out.println("原方法的入参是："+args[0]);
//        System.out.println("原方法执行前会先执行我！！");
//    }

    @Around("pointcut()")
    public Object doAround(ProceedingJoinPoint joinPoint) {
        // 注解鉴权
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        //开始---------------------------------------------------------------
        long lineId;
        Object[] objects = joinPoint.getArgs();
        if (objects.length > 0 && objects[0] instanceof AutoProcessEvent) {
            lineId = ((AutoProcessEvent) objects[0]).getLineId();
        } else {
            lineId = tokenService.getLoginUser(ServletUtils.getRequest()).getSysUser().getLineId();
        }
        //同步设备状态
//        eqpStatusUtil.syncEqpStatus(lineId);
        Boolean flowStatus = redisService.getCacheObject("liaohui:flowStatus:" + lineId);
        if (null == flowStatus || flowStatus) {
            Object result = null;
            try {
                result = joinPoint.proceed();
            } catch (Throwable e) {
                /*异常通知方法*/
                log.error("异常通知方法>目标方法名{},异常为：{}", method.getName(), e);
            }
            return result;
        } else {
            return AjaxResult.error("产线自动流程已关闭，若需操作，请开启。");
        }
    }

}
