package com.ruoyi.production.core.listener;

import com.ruoyi.production.core.observable.OrderStatusChangedEvent;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.utils.CallMesUtil;
import com.ruoyi.production.utils.SmbUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class OrderStatusChangedListener implements ApplicationListener<OrderStatusChangedEvent> {

    @Autowired
    private CallMesUtil callMesUtil;
    @Autowired
    private SmbUtil smbUtil;

    @Override
    public void onApplicationEvent(OrderStatusChangedEvent event) {
        ProdWorkOrder order = event.getWorkOrder();
        System.out.println(order.getStatus());
        //待加工
        if (order.getStatus().equals(WorkOrderStatusEnum.WAIT.getCode())) {
            //通知mes物料齐套
            callMesUtil.requestMaterail(order);
        }
        //待检测
        if (order.getStatus().equals(WorkOrderStatusEnum.DONE.getCode())) {
            //报工mes
            callMesUtil.reportCompleted(order);
            //在线监测数据添加
            smbUtil.getFileAndSave(order);
        }
//        if (!(event.getSource() instanceof WebSocketVo)) {
//            return;
//        }
//        WebSocketVo webSocketVo = (WebSocketVo) event.getSource();
//        SysUser sysUser = new SysUser();
//        sysUser.setLineId(webSocketVo.getLineId());
//        List<SysUser> userList = remoteUserService.listByCondition(sysUser, SecurityConstants.INNER).getData();
//        for (SysUser user : userList) {
//            try {
//                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), user.getUserId() + "");
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
    }
}
