package com.ruoyi.message.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.message.domian.MessageButton;
import com.ruoyi.message.domian.vo.MessageGroupButtonVo;
import com.ruoyi.message.mapper.MessageButtonMapper;
import com.ruoyi.message.service.IMessageButtonService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@Service
public class MessageButtonServiceImpl extends ServiceImpl<MessageButtonMapper, MessageButton> implements IMessageButtonService {

    @Override
    public List<MessageGroupButtonVo> getButtonsByGroupId(Long id) {
        return this.baseMapper.getButtonsByGroupId(id);
    }
}
