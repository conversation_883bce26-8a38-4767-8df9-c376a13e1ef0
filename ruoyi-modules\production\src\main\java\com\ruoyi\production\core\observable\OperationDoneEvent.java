package com.ruoyi.production.core.observable;

import com.ruoyi.production.domain.ProdOperationTrace;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class OperationDoneEvent extends ApplicationEvent {
    private ProdOperationTrace operationTrace;
    public OperationDoneEvent(Object source, ProdOperationTrace operationTrace) {
        super(source);
        this.operationTrace = operationTrace;
    }
}
