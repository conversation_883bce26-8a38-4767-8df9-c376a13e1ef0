package com.ruoyi.common.core.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;

public class BasisEntity extends BaseEntity{

    /** Id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除标识（0-否；1-是）
     */
    @TableLogic
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}
