package com.ruoyi.message.domian;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "EquipmentSpindleCollisionData对象", description = "")
public class EquipmentSpindleCollisionData extends MyBaseEntity {

    private String coordinate;

    private String value;

    private String mach;

    private Date syncTime;


}
