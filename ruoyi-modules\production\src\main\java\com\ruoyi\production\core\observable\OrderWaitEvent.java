package com.ruoyi.production.core.observable;

import com.ruoyi.production.domain.ProdWorkOrder;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class OrderWaitEvent extends ApplicationEvent {
    private ProdWorkOrder workOrder;
    public OrderWaitEvent(Object source, ProdWorkOrder workOrder) {
        super(source);
        this.workOrder = workOrder;
    }

}
