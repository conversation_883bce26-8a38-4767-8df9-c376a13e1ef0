package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.domain.dto.equipment.AutoProcessDto;
import com.ruoyi.production.domain.dto.equipment.UpdateProcessingDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface IBaseEquipmentService extends IService<BaseEquipment> {
    /**
     * 根据条件查询信息
     *
     * @param baseEquipment
     * @return
     */
    List<BaseEquipment> listByCondition(BaseEquipment baseEquipment);

    BaseEquipment getEntityByCode(Long lineId,Integer type,String code);
    BaseEquipment getEquipmentByMqttAlias(String mqttAlias);

    void checkMachMatNums(Long lineId,String eqpCode);

    void autoProcess(AutoProcessDto dto);

    void updateProcessing(UpdateProcessingDto dto);


}
