package com.ruoyi.production.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.production.domain.ProdWorkOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProdWorkOrderVo", description = "富勘工单Vo对象")
public class ProdWorkOrderVo extends ProdWorkOrder {

    @ApiModelProperty(value = "相同任务号和工序号的数量根据时间统计")
    private Integer total;

    @ApiModelProperty(value = "剩余加工数量根据时间统计")
    private Integer remainNum;

    @ApiModelProperty(value = "已完工数量根据时间统计")
    private Integer doneNum;

    @ApiModelProperty(value = "相同任务号和工序号的数量统计")
    private Integer allTotal;

    @ApiModelProperty(value = "剩余加工数量")
    private Integer allRemainNum;

    @ApiModelProperty(value = "已完工数量")
    private Integer allDoneNum;

    @ApiModelProperty(value = "已完工时间开销")
    private Integer allDoneTimeCost;

    @ApiModelProperty(value = "已完工平均时间开销--分钟")
    private String aveDoneTimeCost;

    @ApiModelProperty(value = "预计剩余加工时间")
    private String remainTime;

    @ApiModelProperty(value = "工单状态中文")
    private String statusName;

    /*（给数字孪生添加）*/
    @ApiModelProperty(value = "投产数量")
    private Integer num;

    @ApiModelProperty(value = "订单")
    private String orderNo;

    @ApiModelProperty(value = "生产工单")
    private String workOrderNo;

    @ApiModelProperty(value = "线边库中对应加工机床已备料数量")
    private String machPreparedNums;

    @ApiModelProperty(value = "订单加工开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderStartDate;

    @ApiModelProperty(value = "订单加工结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderEndDate;

    @ApiModelProperty(value = "订单计划开始时间（年月日）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date schedulStartNyr;

    @ApiModelProperty(value = "订单计划结束时间（年月日）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date schedulEndNyr;

    @ApiModelProperty(value = "设备组")
    private String eqpGroup;

    //数字孪生
    private String state;

    //真实加工时长分钟
    private float realCost;


}
