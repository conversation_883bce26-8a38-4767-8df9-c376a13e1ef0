package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.lineStorage.UpdateStorageByWODto;
import com.ruoyi.production.domain.vo.BaseLineStorageVo;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
public interface IBaseLineStorageService extends IService<BaseLineStorage> {

    /**
     * 根据条件查询信息
     *
     * @param lineStorage
     * @return
     */
    List<BaseLineStorageVo> listByCondition(BaseLineStorage lineStorage);

    BaseLineStorage getEntityByOperationId(BaseLineStorage lineStorage, Boolean exceptSelf);

    Boolean updateLineStorageWithRealtion(BaseLineStorage lineStorage);

    void updateLineStorage(UpdateStorageByWODto entity);

    BaseLineStorage getEntityByAlias(Long lineId,String alisa);

    Integer getRemainMatNums(Long eqpId);

    List<BaseLineStorageVo> getReadyOrderList(BaseLineStorage vo);

    void checkWorkOrderNums(Long lineId, ProdWorkOrder order);
}
