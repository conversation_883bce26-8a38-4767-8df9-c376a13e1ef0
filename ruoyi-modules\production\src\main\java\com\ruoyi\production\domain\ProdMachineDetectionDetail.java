package com.ruoyi.production.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
@ApiModel(value = "ProdMachineDetectionDetail对象", description = "")
public class ProdMachineDetectionDetail extends MyBaseEntity {

    @ApiModelProperty(value = "0：未检测，1：合格，2：超差，3:无结论")
    private Integer status;

    private Long pid;

    private String name;

    private String targetValue;

    private String upperTolerance;

    private String lowerTolerance;

    private String realValue;

    private Boolean programResult;
    @ApiModelProperty(value = "自检值")
    private String verifyValue;
    @ApiModelProperty(value = "检测结果+差值")
    private String statusValue;

    @ApiModelProperty(value = "（0：不符；1：一致）")
    private Integer compareResult;
    @ApiModelProperty(value = "（0：超差，1：合格）")
    private Integer verifyResult;

    @ApiModelProperty(value = "专检结论")
    private String specialValue;
    @ApiModelProperty(value = "（0：超差，1：合格）")
    private Integer specialResult;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "verify_time", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "自检时间")
    private Date verifyTime;

    @ApiModelProperty(value = "（0，1）")
    private Integer reportData;


}
