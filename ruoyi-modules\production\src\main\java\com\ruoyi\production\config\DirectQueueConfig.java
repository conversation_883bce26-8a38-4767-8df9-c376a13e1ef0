package com.ruoyi.production.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DirectQueueConfig {

    //声明交换机
    @Bean
    public DirectExchange directBotExchange() {

        return new DirectExchange("direct_bot_exchange", true, false);
    }

    //声明交换机
    @Bean
    public DirectExchange directInterfaceExchange() {
        return new DirectExchange("direct_interface_exchange", true, false);
    }

    /*---------------------------------------------------------------------------------*/

    //声明队列
    @Bean
    public Queue directBotQueue() {
        return new Queue("bot.direct.queue", true);
    }

    //声明队列
    @Bean
    public Queue directXyzQueue() {
        return new Queue("xyz.direct.queue", true);
    }

    //声明队列
    @Bean
    public Queue directPopQueue() {
        return new Queue("pop.direct.queue", true);
    }

    //声明队列
    @Bean
    public Queue directNormalQueue() {
        return new Queue("normal.direct.queue", true);
    }

    //声明队列
    @Bean
    public Queue directWarnQueue() {
        return new Queue("warn.direct.queue", true);
    }

    /*---------------------------------------------------------------------------------*/
    //完成交换机和队列绑定
    @Bean
    public Binding directBotBinding() {
        return BindingBuilder.bind(directBotQueue()).to(directBotExchange()).with("bot");
    }
    //完成交换机和队列绑定
    @Bean
    public Binding directXyzBinding() {
        return BindingBuilder.bind(directXyzQueue()).to(directInterfaceExchange()).with("xyz");
    }
    //完成交换机和队列绑定
    @Bean
    public Binding directPopBinding() {
        return BindingBuilder.bind(directPopQueue()).to(directInterfaceExchange()).with("pop");
    }
    //完成交换机和队列绑定
    @Bean
    public Binding directNormalBinding() {
        return BindingBuilder.bind(directNormalQueue()).to(directInterfaceExchange()).with("normal");
    }
    //完成交换机和队列绑定
    @Bean
    public Binding directWarnBinding() {
        return BindingBuilder.bind(directWarnQueue()).to(directInterfaceExchange()).with("warn");
    }
}
