package com.ruoyi.message.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.message.api.RemoteMessageMqRecordService;
import com.ruoyi.message.api.domain.MessageMqRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteMessageMqRecordFallbackFactory implements FallbackFactory<RemoteMessageMqRecordService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteMessageMqRecordFallbackFactory.class);

    @Override
    public RemoteMessageMqRecordService create(Throwable throwable) {
        log.error("@@@@@@@@@@  消息服务调用失败:{}", throwable.getMessage());
        return new RemoteMessageMqRecordService() {
            @Override
            public R<Boolean> add(MessageMqRecord mqRecord, String source) {
                return R.fail("mq记录添加 失败:" + throwable.getMessage());
            }

            @Override
            public R<List<MessageMqRecord>> list(MessageMqRecord mqRecord, String source) {
                return R.fail("mq记录查询 失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> update(MessageMqRecord mqRecord, String source) {
                return R.fail("mq记录更新 失败:" + throwable.getMessage());
            }

            @Override
            public R<Integer> updateByMesgId(MessageMqRecord mqRecord, String source) {
                return R.fail("mq记录更新 失败:" + throwable.getMessage());
            }

            @Override
            public R<MessageMqRecord> getByMesgId(String msgId, String source) {
                return R.fail("mq记录获取 失败:" + throwable.getMessage());
            }
        };
    }
}
