package com.ruoyi.message.api;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.message.api.factory.RemoteMessageNewsFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteMessageNewsService", value = ServiceNameConstants.MESSAGE_SERVICE, fallbackFactory = RemoteMessageNewsFallbackFactory.class)
public interface RemoteMessageNewsService {

    @PostMapping("/news/add")
    public R add(@RequestBody MessageNews messageNews, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/news/getPopNewsByLineId/{id}")
    public R<JSONObject> getPopNewsByLineId(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/news/reset/{id}")
    public R<Boolean> reset(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);



}
