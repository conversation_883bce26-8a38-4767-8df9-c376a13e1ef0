apiVersion: apps/v1
kind: Deployment
metadata:
  name: 695-message
  namespace: "695"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: 695-message
  template:
    metadata:
      labels:
        app: 695-message
    spec:
      containers:
        - name: 695-message
          image: ************/695/ruoyi-message:latest
          env:
          - name: NACOS_HOST
            value: "nacos-headless.nacos"
          - name: REDIS_HOST
            value: "svc-redis.dev"
          - name: REDIS_PWD
            value: "123456"
          - name: MYSQL_HOST
            value: "svc-mysql.dev"
          - name: MYSQL_USER
            value: "root"
          - name: MYSQL_PASS
            value: "123456"
          - name: RABBITMQ_HOST
            value: "rabbitmq.dev"
          ports:
            - containerPort: 9225
      imagePullSecrets:
        - name: harbor-login
---

apiVersion: v1
kind: Service
metadata:
  name: svc-695-message
  namespace: "695"
spec:
  selector:
    app: 695-message
  type: NodePort
  ports:
    - port: 9225
      protocol: TCP
      targetPort: 9225
      nodePort: 30225
