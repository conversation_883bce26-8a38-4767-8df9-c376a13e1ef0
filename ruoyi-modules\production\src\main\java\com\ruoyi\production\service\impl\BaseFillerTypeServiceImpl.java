package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.BaseFillerType;
import com.ruoyi.production.mapper.BaseFillerTypeMapper;
import com.ruoyi.production.service.IBaseFillerTypeService;
import com.ruoyi.production.utils.FillerTypeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-13
 */
@Service
public class BaseFillerTypeServiceImpl extends ServiceImpl<BaseFillerTypeMapper, BaseFillerType> implements IBaseFillerTypeService {

    @Override
    public List<BaseFillerType> listByCondition(BaseFillerType BaseFillerType) {
        QueryWrapper<BaseFillerType> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(BaseFillerType.getLineId() + ""))
            queryWrapper.eq("line_id", BaseFillerType.getLineId());
        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public List<BaseFillerType> getListByLineId(Long lineId) {
        List<BaseFillerType> fillerTypes = FillerTypeUtils.getFillerTypeCache(lineId + "");
        if (null == fillerTypes) {
            QueryWrapper<BaseFillerType> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("line_id", lineId);
            fillerTypes = this.getBaseMapper().selectList(queryWrapper);
            FillerTypeUtils.setFillerTypeCache(lineId + "", fillerTypes);
        }
        return fillerTypes;
    }

    @Override
    public BaseFillerType getByFillerType(Long lineId, Integer fillerType) {
        BaseFillerType entity = null;
        List<BaseFillerType> fillerTypes = FillerTypeUtils.getFillerTypeCache(lineId + "");
        if(null == fillerTypes){
            fillerTypes = this.getListByLineId(lineId);
            FillerTypeUtils.setFillerTypeCache(lineId + "", fillerTypes);
        }
        for (BaseFillerType baseFillerType : fillerTypes) {
            if (baseFillerType.getFillerType().equals(fillerType)) {
                entity = baseFillerType;
                break;
            }
        }
        return entity;
    }

    @Override
    public BaseFillerType getByAlias(Long lineId, String alisa) {
        BaseFillerType entity = null;
        List<BaseFillerType> fillerTypes = FillerTypeUtils.getFillerTypeCache(lineId + "");
        if(null == fillerTypes){
            fillerTypes = this.getListByLineId(lineId);
            FillerTypeUtils.setFillerTypeCache(lineId + "", fillerTypes);
        }
        for (BaseFillerType fillerType : fillerTypes) {
            if (fillerType.getAlias().equals(alisa)) {
                entity = fillerType;
                break;
            }

        }
        return entity;
    }
}
