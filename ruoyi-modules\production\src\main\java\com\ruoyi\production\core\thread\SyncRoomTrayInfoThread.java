package com.ruoyi.production.core.thread;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.production.domain.BasePreset;
import com.ruoyi.production.service.IBasePresetService;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.production.utils.RabbitMsgUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;

public class SyncRoomTrayInfoThread implements Callable {

    private static final Logger logger = LoggerFactory.getLogger(SyncRoomTrayInfoThread.class);
    private Long presetId;
    private static ISysPlatformApiService apiService;
    private static RedisService redisService;
    private static IBasePresetService presetService;
//    private static RabbitTemplate rabbitTemplate;
    private static RabbitMsgUtils rabbitMsgUtils;

    public SyncRoomTrayInfoThread(Long presetId) {
        this.presetId = presetId;
    }

    static {
        apiService = SpringUtils.getBean(ISysPlatformApiService.class);
        redisService = SpringUtils.getBean(RedisService.class);
        presetService = SpringUtils.getBean(IBasePresetService.class);
//        rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
        rabbitMsgUtils = SpringUtils.getBean(RabbitMsgUtils.class);
    }

    @Override
    public Object call() {
        try {
            //todo 查询托盘对应托盘信息
            String url = apiService.getEntityByCode("iot_rack_wbc").getUrl();
            JSONObject requestParam = new JSONObject();
            BasePreset preset = presetService.getById(presetId);
            if(null == preset)
            {
                return "预调台id不存在";
            }
            requestParam.put("wbcid", preset.getAlias());
//            Thread.sleep(1000);
            String result = HttpRequest.post(url).timeout(3000)
                    .body(requestParam.toJSONString())
                    .execute().body();
//            String result = "[{\"_id\":\"rack\"}]";
            String key = ProductionConstans.tray_info_room + presetId;

            //1相同，跳过
            if (result.equals(redisService.getCacheObject(key))) {
                return "********** 预调台id:" + presetId + " 预调室托盘信息无变化";
            } else {
                //2不相同，设置缓存，生产消息
//                SysPlatformApi api = apiService.getEntityByCode("dcs_getRoomTrayInfo");
//                WebSocketVo webSocketVo = new WebSocketVo();
//                webSocketVo.setUrl(api.getUrl());
//                webSocketVo.setMethod(api.getApiMethod());
//                webSocketVo.setLineId(presetId);
                redisService.setCacheObject(key, result);
//                rabbitTemplate.convertAndSend("direct_interface_exchange","normal", JSON.toJSONString(webSocketVo));
                rabbitMsgUtils.updateRoomTrayInfo(presetId);
//                RabbitMsgUtils.SendMessage(webSocketVo, ProductionConstans.mq_websocket_fanout_exchange);
            }
        } catch (Exception e) {
//            e.printStackTrace();
            logger.error("@@@@@@@@@@ 预调台id:" + presetId + " 预调室托盘信息更新失败>>>" + e.getMessage());
            return "@@@@@@@@@@ 预调台id:" + presetId + " 预调室托盘信息更新失败>>>" + e.getMessage();
        }
        return "********** 预调台id:" + presetId + " 预调室托盘信息更新成功";
    }
}
