package com.ruoyi.production.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Data
@Builder
@ApiModel(value = "SysPlatformApi对象", description = "")
public class SysPlatformApi extends MyBaseEntity {

    private String provider;

    private String url;

    private String apiMethod;

    private String apiCode;

    private String description;
}
