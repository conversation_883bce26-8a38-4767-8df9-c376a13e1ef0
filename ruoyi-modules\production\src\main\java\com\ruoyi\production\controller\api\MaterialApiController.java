package com.ruoyi.production.controller.api;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.production.controller.BaseModuleController;
import com.ruoyi.production.domain.ProdInstockTrace;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.service.IProdInstockTraceService;
import com.ruoyi.production.service.IProdOperationTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "入库接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("api")
public class MaterialApiController extends BaseModuleController {

    @Autowired
    private IProdInstockTraceService instockTraceService;
    @Autowired
    private IProdOperationTraceService operationTraceService;

    @ApiOperation("1-创建易库WMS入库任务")
    @PostMapping({"/wmsInStockCreate"})
    public Object wmsInStockCreate(ModelMap map, @RequestBody JSONObject jsonObject) {

        log.info("********** 1-创建易库WMS入库任务：" + jsonObject.toJSONString());
        String opid = jsonObject.getString("opid");

        String reqId = jsonObject.getString("taskid");
        String itemCode = "M1";
        String process = jsonObject.getString("processid");
        int digit = jsonObject.getInteger("num");
        int itemStatus = 0;
        int processStatus = 1;
        String workShop = "LINE5";
        ProdInstockTrace trace = new ProdInstockTrace();
        trace.setInstockOpId(opid);
        trace.setInstockReqId(reqId);
        ProdInstockTrace stock = instockTraceService.getEntityByOpId(opid);
        if (null == stock)
            instockTraceService.save(trace);
        else
            instockTraceService.updateById(trace);
        String wmsApi = "http://10.72.31.228:20216/api/order/inStockCreate";
        JSONObject data = new JSONObject();
        data.put("opid", opid);
        data.put("reqId", reqId);
        data.put("itemCode", itemCode);
        data.put("digit", digit);
        data.put("itemStatus", Integer.valueOf(itemStatus));
        data.put("processStatus", processStatus);
        data.put("process", process);
        data.put("workShop", workShop);
        new JSONObject();
        String result = HttpRequest.post(wmsApi).timeout(3000).body(data.toJSONString()).execute().body();
        JSONObject response = JSON.parseObject(result);
        this.updateOpreationTrace(Long.parseLong(opid.split("-")[0]), "1-创建易库WMS入库任务 返回");
        log.info("********** 返回>>>" + response.toJSONString());
        JSONObject response1 = new JSONObject();
        response1.put("opid", opid);
        response1.put("code", 0);
        response1.put("msg", null);
        return response1;
    }

    @ApiOperation("2-接收易库WMS可以入库的通知")
    @PostMapping({"/wmsInStockNotice"})
    public Object wmsInStockNotice(ModelMap map, @RequestBody JSONObject jsonObject) {
        log.info("********** 2-接收易库WMS可以入库的通知：" + jsonObject.toJSONString());

        String opid = jsonObject.getString("opid");
        this.updateOpreationTrace(Long.parseLong(opid.split("-")[0]), "2-接收易库WMS可以入库的通知");

        String reqId = jsonObject.getString("reqId");
        Object data = this.callAGVToWMS(map, jsonObject);
        JSONObject response1 = new JSONObject();
        response1.put("code", 0);
        response1.put("msg", null);
        return response1;
    }

    @ApiOperation("3-调度AGV到入库口位置")
    @PostMapping({"/callAGVToWMS"})
    public Object callAGVToWMS(ModelMap map, @RequestBody JSONObject jsonObject) {
        log.info("********** 3-调度AGV到入库口位置：" + jsonObject.toJSONString());
        String agvApi = "http://10.72.1.117:8182/rcms/services/rest/hikRpcService/genAgvSchedulingTask";
        String startPoint = "C7";
        String endPoint = "LTK";
        String opid = jsonObject.getString("opid");
        String reqId = jsonObject.getString("reqId");
        JSONObject para = new JSONObject();
        long timeNew = System.currentTimeMillis();
        int randomNum = (int) ((Math.random() * 9.0D + 1.0D) * 100000.0D);
        String reqCode = Long.valueOf(timeNew).toString() + Integer.valueOf(randomNum).toString();
        para.put("reqCode", reqCode);
        para.put("reqTime", "");
        para.put("clientCode", "");
        para.put("tokenCode", "");
        para.put("taskTyp", "YW2");
        para.put("sceneTyp", "");
        para.put("ctnrTyp", "");
        para.put("ctnrCode", "");
        para.put("wbCode", "");
        JSONArray positionCodePath = new JSONArray();
        JSONObject fromPos = new JSONObject();
        fromPos.put("positionCode", startPoint);
        fromPos.put("type", "00");
        JSONObject toPos = new JSONObject();
        toPos.put("positionCode", endPoint);
        toPos.put("type", "00");
        positionCodePath.add(fromPos);
        positionCodePath.add(toPos);
        para.put("positionCodePath", positionCodePath);
        para.put("podCode", "");
//        para.put("podCode", "100001");
        para.put("podDir", "0");
        para.put("podTyp", "");
        para.put("materialLot", "");
        para.put("priority", "1");
        para.put("agvCode", "9764");
        para.put("taskCode", "");
        JSONObject wmsData = new JSONObject();
        wmsData.put("opid", opid);
        wmsData.put("reqId", reqId);
        para.put("data", wmsData);

        String result = HttpRequest.post(agvApi).timeout(3000).body(para.toJSONString()).execute().body();
        log.info("********** 调度AGV到入库口位置 result>>>" + result);
        JSONObject data = JSON.parseObject(result);
        this.updateOpreationTrace(Long.parseLong(opid.split("-")[0]), "3-调度AGV到入库口位置 >>>" + data.getString("message"));

        JSONObject data1 = new JSONObject();
        data1.put("opid", opid);
        data1.put("code", 0);
        data1.put("msg", data.getString("message"));
        return data1;
    }

    @ApiOperation("4-接收AGV到达入库口通知")
    @PostMapping({"/agvCallbackService/agvCallback"})
    public Object agvCallback(ModelMap map, @RequestBody JSONObject jsonObject) {
        log.info("********** 4-接收AGV到达入库口通知：" + jsonObject.toJSONString());
        String opid = jsonObject.getJSONObject("data").getString("opid");
        this.updateOpreationTrace(Long.parseLong(opid.split("-")[0]), "4-接收AGV到达入库口通知");

        if (jsonObject.containsKey("opid")) {
            JSONObject callEdge = new JSONObject();
            callEdge.put("opid", jsonObject.getString("opid"));
            callEdge.put("code", jsonObject.getString("0"));
            callEdge.put("msg", jsonObject.getString("null"));
            this.wmsFinishNotice(map, callEdge);
        }

        Object data = this.wmsInStockExecute(map, jsonObject);
        JSONObject response1 = new JSONObject();
        response1.put("code", 0);
        response1.put("msg", null);
        return response1;
    }

    @ApiOperation("5-执行WMS入库放料接口")
    @PostMapping({"/wmsInStockExecute"})
    public Object wmsInStockExecute(ModelMap map, @RequestBody JSONObject jsonObject) {
        log.info("********** 5-执行WMS入库放料接口：" + jsonObject.toJSONString());
        JSONObject wmsData = jsonObject.getJSONObject("data");

        String opid = wmsData.getString("opid");
        ProdInstockTrace trace = instockTraceService.getEntityByOpId(opid);
        String reqId = trace.getInstockReqId();

        JSONObject data;
        String wmsApi = "http://10.72.31.228:20216/api/order/inStockAgv";
        data = new JSONObject();
        data.put("reqId", reqId);
        String result = HttpRequest.post(wmsApi).timeout(3000).body(data.toJSONString()).execute().body();
        JSONObject response = JSON.parseObject(result);
        this.updateOpreationTrace(Long.parseLong(opid.split("-")[0]), "5-执行WMS入库放料接口");

        JSONObject data1 = new JSONObject();
        data1.put("opid", opid);
        data1.put("code", "0");
        data1.put("msg", response.getString("msg"));
        return data1;
    }

    @ApiOperation("6-接收易库入库完成通知")
    @PostMapping({"/wmsYiKuCallback"})
    public Object finishInstockCallback(ModelMap map, @RequestBody JSONObject jsonObject) {
        log.info("********** 6-接收易库入库完成通知：" + jsonObject.toJSONString());

        String opid = jsonObject.getString("opid");
        this.updateOpreationTrace(Long.parseLong(opid.split("-")[0]), "6-接收易库入库完成通知 >>>" + jsonObject.getString("msg"));

        String code = jsonObject.getString("code");
        String msg = jsonObject.getString("msg");
        Object data = this.wmsFinishNotice(map, jsonObject);
        JSONObject response1 = new JSONObject();
        response1.put("code", 0);
        response1.put("msg", null);
        return response1;
    }

    @ApiOperation("7-入库完成通知EDGE管控系统")
    @PostMapping({"/wmsFinishNotice"})
    public Object wmsFinishNotice(ModelMap map, @RequestBody JSONObject jsonObject) {
        log.info("********** 7-入库完成通知EDGE管控系统：" + jsonObject.toJSONString());
        String opid = jsonObject.getString("opid");
        String code = jsonObject.getString("code");
        String msg = jsonObject.getString("msg");

        JSONObject data;
        String edgeApi = "http://10.72.31.88:5555/callback/instock";
        data = new JSONObject();
        data.put("opid", opid);
        data.put("code", code);
        data.put("msg", msg);
        new JSONObject();
        String result = HttpRequest.post(edgeApi).timeout(3000).body(data.toJSONString()).execute().body();
        JSONObject response = JSON.parseObject(result);
        JSONObject data1 = new JSONObject();
        data1.put("opid", opid);
        data1.put("code", 0);
        data1.put("msg", msg);
        return data1;
    }

    private void updateOpreationTrace(Long opreationId, String msg) {
        ProdOperationTrace operationTrace = operationTraceService.getById(opreationId);
        if(null == operationTrace)
            return;
        operationTrace.setRemark(msg);
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

    }
}
