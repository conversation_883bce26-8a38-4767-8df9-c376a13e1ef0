package com.ruoyi.message.domian;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "MessageGroup对象", description = "")
public class MessageGroup extends MyBaseEntity {

    private String groupName;

    private String groupCode;

    private String content;

}
