package com.ruoyi.production.domain.dto.processTrace;

import com.ruoyi.production.domain.ProdOperationTrace;
import io.swagger.annotations.ApiModelProperty;

public class ProdOperationTraceDto extends ProdOperationTrace {


    @ApiModelProperty(value = "时间范围开始")
    private String timeStart;

    @ApiModelProperty(value = "时间范围结束")
    private String timeEnd;

    public String getTimeStart() {
        return timeStart;
    }

    public void setTimeStart(String timeStart) {
        this.timeStart = timeStart;
    }

    public String getTimeEnd() {
        return timeEnd;
    }

    public void setTimeEnd(String timeEnd) {
        this.timeEnd = timeEnd;
    }

}
