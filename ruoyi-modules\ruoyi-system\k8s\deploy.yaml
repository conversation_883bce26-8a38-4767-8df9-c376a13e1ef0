apiVersion: apps/v1
kind: Deployment
metadata:
  name: 695-system
  namespace: "695"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: 695-system
  template:
    metadata:
      labels:
        app: 695-system
    spec:
      containers:
        - name: 695-system
          image: ************/695/ruoyi-system:latest
          env:
          - name: NACOS_HOST
            value: "nacos-headless.nacos"
          - name: REDIS_HOST
            value: "svc-redis.dev"
          - name: REDIS_PWD
            value: "123456"
          - name: MYSQL_HOST
            value: "svc-mysql.dev"
          - name: MYSQL_USER
            value: "root"
          - name: MYSQL_PASS
            value: "123456"
          ports:
            - containerPort: 9221
      imagePullSecrets:
        - name: harbor-login
---

apiVersion: v1
kind: Service
metadata:
  name: svc-695-system
  namespace: "695"
spec:
  selector:
    app: 695-system
  type: NodePort
  ports:
    - port: 9221
      protocol: TCP
      targetPort: 9221
      nodePort: 30221
