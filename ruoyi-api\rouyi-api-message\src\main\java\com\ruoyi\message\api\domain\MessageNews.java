package com.ruoyi.message.api.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@Data
//@Builder
@ApiModel(value = "MessageNews对象", description = "")
public class MessageNews extends MyBaseEntity {

    private Long createrId;

    private Long receiverId;

    private String title;

    private String content;

    private String type;

    private String source;

    private String targetUrl;

    private Long lineId;

    private Long buttonGroupId;

    private Integer isDeal;

    private Integer isRead;

    private String operationId;

    @TableField(exist = false)
    private String messageRenderId;

}
