package com.ruoyi.production.utils;

import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.production.domain.BaseFillerType;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.TreeMap;

public class FillerTypeUtils {
    /**
     * 设置字典缓存
     *
     * @param key       参数键
     * @param dictDatas 字典数据列表
     */
    public static void setFillerTypeCache(String key, List<BaseFillerType> dictDatas) {
        SpringUtils.getBean(RedisService.class).setCacheObject(getCacheKey(key), dictDatas);
    }

    /**
     * 获取字典缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static List<BaseFillerType> getFillerTypeCache(String key) {
        Object cacheObj = SpringUtils.getBean(RedisService.class).getCacheObject(getCacheKey(key));
        if (StringUtils.isNotNull(cacheObj)) {
            return StringUtils.cast(cacheObj);
        }
        return null;
    }

    /**
     * 删除指定字典缓存
     *
     * @param key 字典键
     */
    public static void removeFillerTypeCache(String key) {
        SpringUtils.getBean(RedisService.class).deleteObject(getCacheKey(key));
    }

    /**
     * 清空字典缓存
     */
    public static void clearFillerTypeCache() {
        Collection<String> keys = SpringUtils.getBean(RedisService.class).keys(Constants.SYS_DICT_KEY + "*");
        SpringUtils.getBean(RedisService.class).deleteObject(keys);
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey) {
        return ProductionConstans.REDIS_FFIILER_TYPE + configKey;
    }

    public static void main(String[] args) {
        TreeMap<Long,Object> treeMap = new TreeMap<>(Comparator.reverseOrder());
        treeMap.put(1l,null);
        treeMap.put(3l,null);
        treeMap.put(2l,null);
        System.out.println(treeMap.keySet().toString());
    }

}
