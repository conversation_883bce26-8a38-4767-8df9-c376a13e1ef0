package com.ruoyi.production.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.BaseProcess;
import com.ruoyi.production.domain.vo.BaseProcessVo;
import com.ruoyi.production.mapper.BaseProcessMapper;
import com.ruoyi.production.service.IBaseProcessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Service
public class BaseProcessServiceImpl extends ServiceImpl<BaseProcessMapper, BaseProcess> implements IBaseProcessService {

    @Override
    public List<BaseProcessVo> listByCondition(BaseProcess baseProcess) {

        //QueryWrapper<BaseProcess> queryWrapper = new QueryWrapper();
        //if (StringUtils.isNotBlank(baseProcess.getProcessCode()))
        //    queryWrapper.eq("process_code", baseProcess.getProcessCode());
        //if (StringUtils.isNotBlank(baseProcess.getLineId() + ""))
        //    queryWrapper.eq("line_id", baseProcess.getLineId());
        //
        //queryWrapper.orderByAsc("order_num");
        return this.getBaseMapper().listByCondition(baseProcess);
    }

    @Override
    public List<BaseProcess> getProcessLinksByParentId(Long id) {
        return this.getBaseMapper().getProcessLinksByParentId(id);
    }

    @Override
    public List<BaseProcess> getMainProcessLinksByLineId(Long lineId) {
        QueryWrapper<BaseProcess> queryWrapper = new QueryWrapper();
        queryWrapper.eq("is_main", true);
        queryWrapper.eq("line_id",lineId);
        BaseProcess mainProcess = this.getBaseMapper().selectOne(queryWrapper);
        if (mainProcess == null)
            return null;

        return this.getProcessLinksByParentId(mainProcess.getId());
    }

    @Override
    public BaseProcess getEntityByProcessCode(Long lineId, String code) {
        if (StringUtils.isBlank(code))
            return null;
        QueryWrapper<BaseProcess> queryWrapper = new QueryWrapper();
        if (null != lineId) {
            queryWrapper.eq("line_id", lineId);
        }
        queryWrapper.eq("process_code", code);

        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public BaseProcess getNextNode(Long lineId, String code) {
        if (StringUtils.isBlank(code))
            return null;
        BaseProcess baseProcess = this.getEntityByProcessCode(lineId, code);
        if (null == baseProcess)
            return null;
        QueryWrapper<BaseProcess> queryWrapper = new QueryWrapper();
        queryWrapper.eq("parent_id", baseProcess.getId());
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public Boolean isBeforeInMach(Long lineId, String processCode) {
        String machCode = "MatInMachine";
        if (StringUtils.isBlank(processCode) || StringUtils.isBlank(lineId + ""))
            return null;
        QueryWrapper<BaseProcess> queryWrapper = new QueryWrapper();
        queryWrapper.eq("line_id", lineId);
        queryWrapper.eq("is_main", 1);
        List<BaseProcess> processList = this.baseMapper.selectList(queryWrapper);
        if (processList.size() < 0)
            return null;
        BaseProcess baseProcess = processList.get(0);

        Boolean flag = false;
        //List<BaseProcess> processLinks = this.getProcessLinksByParentId(Integer.parseInt(baseProcess.getId() + ""));
        //for (BaseProcess process : processLinks) {
        //    String code = process.getProcessCode();
        //    if (machCode.equals(code))
        //        flag = true;
        //    if (processCode.equals(code))
        //}

        return flag;
    }
}
