package com.ruoyi.production.domain.api;

import io.swagger.annotations.ApiModelProperty;

public class CallForcamPalnResultEntity {


    @ApiModelProperty(value = "产线号")
    String WORKPLACE_GROUP;
    @ApiModelProperty(value = "产线号_机床号_机器类型")
    String WORKPLACE_NAME;
    @ApiModelProperty(value = "物料号")
    String MATERIAL_NUMBER;
    @ApiModelProperty(value = "物料名称")
    String MATERIAL_DESC;
    @ApiModelProperty(value = "工单号-工序号（待确认）")
    String ORDER_NUMBER;
    @ApiModelProperty(value = "OPERATION_NUMBER")
    String OPERATION_NUMBER;
    @ApiModelProperty(value = "OPERATION_SPLIT")
    String OPERATION_SPLIT;
    @ApiModelProperty(value = "SCHEDULED_START_DATE")
    String SCHEDULED_START_DATE;
    @ApiModelProperty(value = "SCHEDULED_END_DATE")
    String SCHEDULED_END_DATE;
    @ApiModelProperty(value = "COMPONENT_NUMBER")
    String COMPONENT_NUMBER;
    @ApiModelProperty(value = "BATCH_NUMBER")
    String BATCH_NUMBER;
    @ApiModelProperty(value = "REQUIREMENT_QUANTITY")
    String REQUIREMENT_QUANTITY;
    @ApiModelProperty(value = "WORKPIECEID")
    String WORKPIECEID;
    @ApiModelProperty(value = "线边库位")
    String STORAGE_BIN;
    @ApiModelProperty(value = "产线号")
    String STORAGE_LOCATION;
    @ApiModelProperty(value = "TARGET_QUANTITY")
    String TARGET_QUANTITY;
    @ApiModelProperty(value = "POSITION_NUMBER")
    String POSITION_NUMBER;
    @ApiModelProperty(value = "WORKPLACEID")
    String WORKPLACEID;
    @ApiModelProperty(value = "富堪任务ID")
    String OPERATIONID;
    @ApiModelProperty(value = "物料ID")
    String COMPONENTID;

    public String getMATERIAL_DESC() {
        return MATERIAL_DESC;
    }

    public void setMATERIAL_DESC(String MATERIAL_DESC) {
        this.MATERIAL_DESC = MATERIAL_DESC;
    }

    public String getWORKPLACE_GROUP() {
        return WORKPLACE_GROUP;
    }

    public void setWORKPLACE_GROUP(String WORKPLACE_GROUP) {
        this.WORKPLACE_GROUP = WORKPLACE_GROUP;
    }

    public String getWORKPLACE_NAME() {
        return WORKPLACE_NAME;
    }

    public void setWORKPLACE_NAME(String WORKPLACE_NAME) {
        this.WORKPLACE_NAME = WORKPLACE_NAME;
    }

    public String getMATERIAL_NUMBER() {
        return MATERIAL_NUMBER;
    }

    public void setMATERIAL_NUMBER(String MATERIAL_NUMBER) {
        this.MATERIAL_NUMBER = MATERIAL_NUMBER;
    }

    public String getORDER_NUMBER() {
        return ORDER_NUMBER;
    }

    public void setORDER_NUMBER(String ORDER_NUMBER) {
        this.ORDER_NUMBER = ORDER_NUMBER;
    }

    public String getOPERATION_NUMBER() {
        return OPERATION_NUMBER;
    }

    public void setOPERATION_NUMBER(String OPERATION_NUMBER) {
        this.OPERATION_NUMBER = OPERATION_NUMBER;
    }

    public String getOPERATION_SPLIT() {
        return OPERATION_SPLIT;
    }

    public void setOPERATION_SPLIT(String OPERATION_SPLIT) {
        this.OPERATION_SPLIT = OPERATION_SPLIT;
    }

    public String getCOMPONENT_NUMBER() {
        return COMPONENT_NUMBER;
    }

    public void setCOMPONENT_NUMBER(String COMPONENT_NUMBER) {
        this.COMPONENT_NUMBER = COMPONENT_NUMBER;
    }

    public String getBATCH_NUMBER() {
        return BATCH_NUMBER;
    }

    public void setBATCH_NUMBER(String BATCH_NUMBER) {
        this.BATCH_NUMBER = BATCH_NUMBER;
    }

    public String getREQUIREMENT_QUANTITY() {
        return REQUIREMENT_QUANTITY;
    }

    public void setREQUIREMENT_QUANTITY(String REQUIREMENT_QUANTITY) {
        this.REQUIREMENT_QUANTITY = REQUIREMENT_QUANTITY;
    }

    public String getSCHEDULED_START_DATE() {
        return SCHEDULED_START_DATE;
    }

    public void setSCHEDULED_START_DATE(String SCHEDULED_START_DATE) {
        this.SCHEDULED_START_DATE = SCHEDULED_START_DATE;
    }

    public String getSCHEDULED_END_DATE() {
        return SCHEDULED_END_DATE;
    }

    public void setSCHEDULED_END_DATE(String SCHEDULED_END_DATE) {
        this.SCHEDULED_END_DATE = SCHEDULED_END_DATE;
    }

    public String getWORKPIECEID() {
        return WORKPIECEID;
    }

    public void setWORKPIECEID(String WORKPIECEID) {
        this.WORKPIECEID = WORKPIECEID;
    }

    public String getSTORAGE_BIN() {
        return STORAGE_BIN;
    }

    public void setSTORAGE_BIN(String STORAGE_BIN) {
        this.STORAGE_BIN = STORAGE_BIN;
    }

    public String getSTORAGE_LOCATION() {
        return STORAGE_LOCATION;
    }

    public void setSTORAGE_LOCATION(String STORAGE_LOCATION) {
        this.STORAGE_LOCATION = STORAGE_LOCATION;
    }

    public String getTARGET_QUANTITY() {
        return TARGET_QUANTITY;
    }

    public void setTARGET_QUANTITY(String TARGET_QUANTITY) {
        this.TARGET_QUANTITY = TARGET_QUANTITY;
    }

    public String getPOSITION_NUMBER() {
        return POSITION_NUMBER;
    }

    public void setPOSITION_NUMBER(String POSITION_NUMBER) {
        this.POSITION_NUMBER = POSITION_NUMBER;
    }

    public String getWORKPLACEID() {
        return WORKPLACEID;
    }

    public void setWORKPLACEID(String WORKPLACEID) {
        this.WORKPLACEID = WORKPLACEID;
    }

    public String getOPERATIONID() {
        return OPERATIONID;
    }

    public void setOPERATIONID(String OPERATIONID) {
        this.OPERATIONID = OPERATIONID;
    }

    public String getCOMPONENTID() {
        return COMPONENTID;
    }

    public void setCOMPONENTID(String COMPONENTID) {
        this.COMPONENTID = COMPONENTID;
    }
}
