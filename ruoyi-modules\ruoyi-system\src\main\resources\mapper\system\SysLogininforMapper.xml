<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysLogininforMapper">

	<resultMap type="SysLogininfor" id="SysLogininforResult">
		<id     property="infoId"        column="info_id"           />
		<result property="userName"      column="user_name"         />
		<result property="status"        column="status"            />
		<result property="ipaddr"        column="ipaddr"            />
		<result property="msg"           column="msg"               />
		<result property="accessTime"    column="access_time"       />
	</resultMap>

	<insert id="insertLogininfor" parameterType="SysLogininfor">
		insert into sys_logininfor (user_name, status, ipaddr, msg, access_time)
		values (#{userName}, #{status}, #{ipaddr}, #{msg}, sysdate())
	</insert>
	
	<select id="selectLogininforList" parameterType="SysLogininfor" resultMap="SysLogininforResult">
		select info_id, user_name, ipaddr, status, msg, access_time from sys_logininfor
		<where>
			<if test="ipaddr != null and ipaddr != ''">
				AND ipaddr like concat('%', #{ipaddr}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="userName != null and userName != ''">
				AND user_name like concat('%', #{userName}, '%')
			</if>
			<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				and date_format(access_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				and date_format(access_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
			</if>
		</where>
		order by info_id desc
	</select>

	<select id="pageList" resultMap="SysLogininforResult">
		select info_id, user_name, ipaddr, status, msg, access_time from sys_logininfor
		<where>
			<if test="params.ipaddr != null and params.ipaddr != ''">
				AND ipaddr like concat('%', #{params.ipaddr}, '%')
			</if>
			<if test="params.status != null and params.status != ''">
				AND status = #{params.status}
			</if>
			<if test="params.userName != null and params.userName != ''">
				AND user_name like concat('%', #{params.userName}, '%')
			</if>
<!--			<if test="params.beginTime != null and params.beginTime != ''">&lt;!&ndash; 开始时间检索 &ndash;&gt;-->
<!--				and date_format(access_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')-->
<!--			</if>-->
<!--			<if test="params.endTime != null and params.endTime != ''">&lt;!&ndash; 结束时间检索 &ndash;&gt;-->
<!--				and date_format(access_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')-->
<!--			</if>-->
		</where>
		order by info_id desc
	</select>
	
	<delete id="deleteLogininforByIds" parameterType="Long">
 		delete from sys_logininfor where info_id in
 		<foreach collection="array" item="infoId" open="(" separator="," close=")">
 			#{infoId}
        </foreach> 
 	</delete>
    
    <update id="cleanLogininfor">
        truncate table sys_logininfor
    </update>
    
</mapper> 