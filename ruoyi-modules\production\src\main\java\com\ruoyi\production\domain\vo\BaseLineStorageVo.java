package com.ruoyi.production.domain.vo;

import com.ruoyi.production.domain.BaseLineStorage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class BaseLineStorageVo extends BaseLineStorage {

    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "任务号")
    private String billCode;

    @ApiModelProperty(value = "工序号")
    private String schedulCode;

    @ApiModelProperty(value = "任务号+工序号")
    private String billSchedulCode;

    @ApiModelProperty(value = "物料号")
    private String matCode;

    @ApiModelProperty(value = "机床号")
    private String eqpCode;

    @ApiModelProperty(value = "工单号")
    private String ticketNumber;

    @ApiModelProperty(value = "物料类型：0：毛坯（待加工）；1：成品（待检验）")
    private String matType;

    @ApiModelProperty(value = "内容名称")
    private String fillerTypeName;

    @ApiModelProperty(value = "工单状态名称")
    private String matTypeName;

    @ApiModelProperty(value = "对应机床设备code")
    private String machCode;
    @ApiModelProperty(value = "对应机床设备name")
    private String machName;

    @ApiModelProperty(value = "工单状态")
    private Integer status;

    @ApiModelProperty(value = "工单关联托盘大小")
    private String bz1;
    @ApiModelProperty(value = "机床组")
    private String eqpGroup;
    @ApiModelProperty(value = "真实加工机床")
    private String realEqp;


}
