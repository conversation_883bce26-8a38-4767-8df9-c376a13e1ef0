package com.ruoyi.production.domain;


import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-05-30
 */
@ApiModel(value = "ProdOperationTraceDetail对象", description = "")
public class ProdOperationTraceDetail extends MyBaseEntity {

    @ApiModelProperty("父id")
    private Long pid;

    @ApiModelProperty("状态")
    private String status;

    private Long eqpId;

    public Long getEqpId() {
        return eqpId;
    }

    public void setEqpId(Long eqpId) {
        this.eqpId = eqpId;
    }

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
