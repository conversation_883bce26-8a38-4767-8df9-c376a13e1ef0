package com.ruoyi.production.core.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.message.api.RemoteEqpCollisionDataService;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.EquipmentSpindleCollisionData;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.utils.ProdDateUtil;
import com.ruoyi.production.utils.WebSocketServer;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public class DirectInterfaceConsumers {

    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;
    @Autowired
    private RemoteEqpCollisionDataService remoteEqpCollisionDataService;
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private WebSocketServer webSocketServer;

    @RabbitListener(queues = "normal.direct.queue")
    public void normal(String data, Message message, Channel channel) throws IOException {
        try {
            WebSocketVo webSocketVo = JSON.toJavaObject(JSON.parseObject(data), WebSocketVo.class);
            webSocketVo.setType("interface");

            webSocketServer.sendInfo(JSON.toJSONString(webSocketVo),webSocketVo.getLineId());

//            List<SysUser> userList = remoteUserService.listByCondition(sysUser, SecurityConstants.INNER).getData();
//            for (SysUser user : userList) {
//                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), user.getUserId() + "");
//            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("@@@@@@@@@@  通用消息监听 消费失败>>> " + data);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    @RabbitListener(queues = "pop.direct.queue")
    public void pop(String data, Message message, Channel channel) throws IOException {
        try {
            WebSocketVo webSocketVo = JSON.toJavaObject(JSON.parseObject(data), WebSocketVo.class);
            webSocketVo.setType("message");

            Long lineId = webSocketVo.getLineId();
            JSONObject jsonObject = remoteMessageNewsService.getPopNewsByLineId(lineId, SecurityConstants.INNER).getData();
            webSocketVo.setResponse(jsonObject);
            webSocketVo.setTime(System.currentTimeMillis());
            webSocketServer.sendInfo(JSON.toJSONString(webSocketVo),webSocketVo.getLineId());

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("@@@@@@@@@@  弹窗消息监听 消费失败>>> " + data);
            e.printStackTrace();
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);

        }
    }

    @RabbitListener(queues = "xyz.direct.queue")
    public void xyz(String data, Message message, Channel channel) throws IOException {
//        log.info("********** 收到消息 >>> 开始消费");
        try {
            WebSocketVo webSocketVo = JSON.toJavaObject(JSON.parseObject(data), WebSocketVo.class);
            webSocketVo.setType("interface");

            Set<Long> idSet = redisTemplate.opsForSet().members(ProductionConstans.REDIS_WS_COLLISION + webSocketVo.getLineId());
            if (null == idSet || idSet.size() < 1){
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            Long start = System.currentTimeMillis();

            HashMap<String, Object> param = new HashMap<>();
            param.put("startTime", new Date());
            param.put("endTime", ProdDateUtil.getMinuteToNow(-2));

            List<EquipmentSpindleCollisionData> dataList = remoteEqpCollisionDataService.getCollisionDataListByMap(param, SecurityConstants.INNER).getData();
            Map<String, Map<String, Map<String, String>>> map = new HashMap<>();
            for (EquipmentSpindleCollisionData collisionData : dataList) {
                String time = sdf.format(collisionData.getSyncTime());
                String machCode = collisionData.getMach();
                Map<String, Map<String, String>> machMap = map.get(machCode);
                if (null == machMap)
                    machMap = new LinkedHashMap<>();
                Map<String, String> timeMap = machMap.get(time);
                if (null == timeMap)
                    timeMap = new LinkedHashMap<>();
                timeMap.put(collisionData.getCoordinate(), collisionData.getValue());
                machMap.put(time, timeMap);
                map.put(machCode, machMap);
            }
            //格式化
            JSONArray jsonArray = new JSONArray();
            Set<String> mapSet = map.keySet();
            for (String machCode : mapSet) {
                BaseEquipment baseEquipment = equipmentService.getEquipmentByMqttAlias(machCode);
                if (null == baseEquipment)
                    continue;
                JSONObject machInfo = new JSONObject();
                machInfo.put("info", baseEquipment);
                JSONArray dataArray = new JSONArray();
                Map<String, Map<String, String>> machMap = map.get(machCode);
                Set<String> timeSet = machMap.keySet();
                timeSet.stream().forEach(time -> {
                    Map<String, String> timeMap = machMap.get(time);
                    timeMap.put("time", time);
                    dataArray.add(timeMap);
                });
                machInfo.put("data", dataArray);
                jsonArray.add(machInfo);
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("list", jsonArray);
            webSocketVo.setResponse(jsonObject);

//        webSocketVo.setResponse(1);
//        webSocketVo.setData(JSON.parseObject(map.toString()));
            Long endTime = System.currentTimeMillis();
//        log.info("********** 逻辑计算完毕>>> 用时" + (endTime - start));

            for (Long id : idSet) {
                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), id + "");
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//        log.info("********** websocket 发送完毕>>> 用时"+(new Date().getTime()-endTime));
        } catch (Exception e) {
            log.error("@@@@@@@@@@  主轴监控消息监听 消费失败>>> " + data);
            e.printStackTrace();
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    @RabbitListener(queues = "warn.direct.queue")
    public void warnMsg(String data, Message message, Channel channel) throws IOException {
        try {
            WebSocketVo webSocketVo = JSON.toJavaObject(JSON.parseObject(data), WebSocketVo.class);
            webSocketVo.setType("warn");

            webSocketVo.setTime(System.currentTimeMillis());
            SysUser sysUser = new SysUser();
            sysUser.setLineId(webSocketVo.getLineId());
            List<SysUser> userList = remoteUserService.listByCondition(sysUser, SecurityConstants.INNER).getData();
            webSocketServer.sendInfo(JSON.toJSONString(webSocketVo),webSocketVo.getLineId());
//            for (SysUser user : userList) {
//                webSocketVo.setUserId(user.getUserId());
//                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), user.getUserId() + "");
//            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("@@@@@@@@@@  弹窗消息监听 消费失败>>> " + data);
            e.printStackTrace();
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);

        }
    }

}
