package com.ruoyi.production.controller.api;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.production.controller.BaseModuleController;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.utils.ProdDateUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "开放给富堪的接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("api/digitalTwin/")
public class DigitalTwinController extends BaseModuleController {

    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private IProdWorkOrderService prodWorkOrderService;

    @PostMapping(value = "getWorkOrderListByLineId", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public R<?> getWorkOrderByLineId(@RequestBody ProdWorkOrder prodWorkOrder) throws ParseException {
        prodWorkOrder.setSchedulStartDate(sdf.parse(ProdDateUtil.getThisMonthStart()));
        prodWorkOrder.setSchedulEndDate(sdf.parse(ProdDateUtil.getThisMonthEnd()));
        List<ProdWorkOrderVo> prodWorkOrderVoList = prodWorkOrderService.getTotalList(prodWorkOrder);
        prodWorkOrderVoList.forEach(orderVo -> {
            orderVo.setStatusName(WorkOrderStatusEnum.getInfoByKey(orderVo.getStatus()));
            orderVo.setState(orderVo.getStatus() + "");
        });

//        this.modifyOrderStatus(prodWorkOrderVoList);
        return R.ok(prodWorkOrderVoList);
    }

//    @PostMapping(value = "getWorkOrderListByEqpCode", produces = {"application/json;charset=UTF-8"})
//    @ResponseBody
//    public R<?> getWorkOrderListByEqpCode(@RequestBody ProdWorkOrderDto prodWorkOrderDto) throws ParseException {
//        prodWorkOrderDto.setSchedulStartDate(sdf.parse(ProdDateUtil.getThisMonthStart()));
//        prodWorkOrderDto.setSchedulEndDate(sdf.parse(ProdDateUtil.getThisMonthEnd()));
//        List<ProdWorkOrderVo> prodWorkOrderVoList = prodWorkOrderService.listByCondition(prodWorkOrderDto);
//
//        this.modifyOrderStatus(prodWorkOrderVoList);
//        prodWorkOrderVoList.forEach(order -> {
//            String[] str = order.getTicketNumber().split(":");
//            if (str.length == 2) {
//                order.setOrderNo(str[0]);
//                order.setWorkOrderNo(str[1]);
//            }
//
//            Integer status = order.getStatus();
//            //数字孪生接口状态变更 对照新旧状态 添加
//
//            if (status == 7)
//                order.setStatusName("已装调");
//            else if (status == 3)
//                order.setStatusName("停止");
//            else
//                order.setStatusName(DigitalTWorkOrderStatusEnum.getInfoByKey(status));
//        });
//        return R.ok(prodWorkOrderVoList);
//    }
//
//    /**
//     * 数字孪生接口状态变更 对照新旧状态 添加
//     * @param prodWorkOrderVoList
//     */
//    private void modifyOrderStatus(List<ProdWorkOrderVo> prodWorkOrderVoList) {
//        //处理映射中文
//        /*
//        旧
//         WAIT(0, "待加工"),
//DOING(1, "加工中"),
//DONE(2, "待检测"),
//STOP(3, "停止"),
//CREATED(4, "已创建"),
//OUSTOCK(5, "已出库"),
//INSTOCK(6, "已入库");
//
//新
//    CREATED(0, "已创建"),
//    OUSTOCK(1, "已出库"),
//    ADJUSTED(2, "已装调"),
//    WAIT(3, "待加工"),
//    DOING(4, "加工中"),
//    DONE(5, "待检测"),
//    INSTOCK(6, "已入库");
//        * */
//        prodWorkOrderVoList.forEach(order -> {
//            Integer status = order.getStatus();
//            if (status == 0)
//                order.setStatus(4);
//            else if (status == 1)
//                order.setStatus(5);
//            else if (status == 2)
//                order.setStatus(7);
//            else if (status == 3)
//                order.setStatus(0);
//            else if (status == 4)
//                order.setStatus(1);
//            else if (status == 5)
//                order.setStatus(2);
//            else if (status == 6)
//                order.setStatus(6);
//            else
//                order.setStatus(3);
//        });
//    }


    @PostMapping(value = "getWorkOrderListByEqpCode", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public R<?> getWorkOrderListByEqpCode(@RequestBody ProdWorkOrderDto prodWorkOrderDto) throws ParseException {
        prodWorkOrderDto.setSchedulStartDate(sdf.parse(ProdDateUtil.getThisMonthStart()));
        prodWorkOrderDto.setSchedulEndDate(sdf.parse(ProdDateUtil.getThisMonthEnd()));
        List<ProdWorkOrderVo> prodWorkOrderVoList = prodWorkOrderService.listByCondition(prodWorkOrderDto);

        prodWorkOrderVoList.forEach(order -> {
            String[] str = order.getTicketNumber().split(":");
            if (str.length == 2) {
                order.setOrderNo(str[0]);
                order.setWorkOrderNo(str[1]);
            }

            order.setStatusName(WorkOrderStatusEnum.getInfoByKey(order.getStatus()));
            order.setState(order.getStatus() + "");

        });
        return R.ok(prodWorkOrderVoList);
    }


}
