package com.ruoyi.production.utils;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

@ServerEndpoint("/websocket/{sid}")
@Component
public class WebSocketServer {

    @Autowired
    private RemoteUserService remoteUserService;
    // 日志对象
    private static final Logger log = LoggerFactory.getLogger(WebSocketServer.class);

    // 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
    private static int onlineCount = 0;

    // concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
    private static CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<>();
    //private static ConcurrentHashMap<String,WebSocketServer> webSocketSet = new ConcurrentHashMap<>();

    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;

    // 接收sid
    private String sid = "";

    /*
     * 客户端创建连接时触发
     * */
    @OnOpen
    public void onOpen(Session session, @PathParam("sid") String sid) {
        this.session = session;
        webSocketSet.add(this); // 加入set中
        addOnlineCount(); // 在线数加1
        this.sid = sid;
        System.out.println("有新窗口开始监听:" + sid + ", 当前在线人数为" + getOnlineCount());
        try {
            sendMessage("连接成功");
        } catch (IOException e) {
            log.error("@@@@@@@@@@  websocket IO异常");
        }
    }

    /**
     * 客户端连接关闭时触发
     **/
    @OnClose
    public void onClose() {
        webSocketSet.remove(this); // 从set中删除
        subOnlineCount(); // 在线数减1
//        log.info("********** 有一连接关闭！当前在线人数为" + getOnlineCount());
    }

    /**
     * 接收到客户端消息时触发
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        if (null == message || StringUtils.isBlank(message) || "null".equals(message))
            return;
//        log.info("********** 收到来自窗口" + sid + "的信息:" + message);
        // 群发消息
        for (WebSocketServer item : webSocketSet) {
            try {
                item.sendMessage(message);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 连接发生异常时候触发
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("@@@@@@@@@@  发生错误" + session.getRequestURI());

        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送(向浏览器发消息)
     */
    public void sendMessage(String message) throws IOException {
//        if(null == message || StringUtils.isBlank(message))
//            return;
//        log.info("********** 服务器消息推送：" + message);
//        System.out.println(this.session.getRequestURI());
        if (!session.isOpen()) {
            return;
        }
        synchronized (this.session) {
            this.session.getBasicRemote().sendText(message);
        }
    }

    public void sendInfo(String message, @PathParam("lineId") Long lineId) throws IOException {
//        if(null == message || StringUtils.isBlank(message))
//            return;
//        log.info("********** 推送消息到窗口" + sid + "，推送内容:" + message);
        SysUser sysUser = new SysUser();
        sysUser.setLineId(lineId);
        List<SysUser> userList = remoteUserService.listByCondition(sysUser, SecurityConstants.INNER).getData();
        for (WebSocketServer item : webSocketSet) {
            for (SysUser user : userList) {
                try {
                    if (item.sid.equals(user.getUserId() + "")) {
                        //判断是否不接受消息角色
                        List<SysRole> noPopRole = user.getRoles().stream().filter(e -> e.getRoleKey().equals("noPop")).collect(Collectors.toList());
                        if (noPopRole.size() > 0) {
                            break;
                        } else {
                            item.sendMessage(message);
                            break;
                        }
                    }
                } catch (IOException e) {
                    continue;
                }
            }
        }
    }

    /**
     * 发送消息到所有客户端
     * 指定sid则向指定客户端发消息
     * 不指定sid则向所有客户端发送消息
     */
    public static void sendInfo(String message, @PathParam("sid") String sid) throws IOException {
//        if(null == message || StringUtils.isBlank(message))
//            return;
//        log.info("********** 推送消息到窗口" + sid + "，推送内容:" + message);
        for (WebSocketServer item : webSocketSet) {
            try {
                // 这里可以设定只推送给这个sid的，为null则全部推送
                if (sid == null) {
                    item.sendMessage(message);
                } else if (item.sid.equals(sid)) {
                    item.sendMessage(message);
                }
            } catch (IOException e) {
                continue;
            }
        }
    }

    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount++;
    }

    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount--;
    }

    public static CopyOnWriteArraySet<WebSocketServer> getWebSocketSet() {
        return webSocketSet;
    }



}