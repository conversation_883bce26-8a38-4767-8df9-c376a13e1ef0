package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.detection.DetectionReportVo;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface ProdWorkOrderMapper extends BaseMapper<ProdWorkOrder> {

    List<ProdWorkOrderVo> getTotalList(@Param("params")ProdWorkOrder prodWorkOrder);
    List<ProdWorkOrderVo> getProcessTraceList(@Param("params")ProdWorkOrder prodWorkOrder);

    List<ProdWorkOrderVo> listByCondition(@Param("params")ProdWorkOrder prodWorkOrder);

    IPage<ProdWorkOrderVo> listForPage(IPage<ProdOperationTraceVo> page, @Param("params") ProdWorkOrderDto orderDto);

    IPage<ProdWorkOrderVo> listForPageToDock(IPage<ProdOperationTraceVo> page, @Param("params") ProdWorkOrderDto orderDto);

    IPage<ProdWorkOrderVo> pageList(IPage<ProdOperationTraceVo> page, @Param("params") ProdWorkOrderDto orderDto);

    ProdWorkOrderVo getEntityByParam(@Param("params") ProdWorkOrderDto orderDto);

    List<DetectionReportVo> getDetectionReportData(@Param("params")ProdWorkOrder prodWorkOrder);

    IPage<DetectionReportVo> listForPageDetectionReport(IPage<DetectionReportVo> page, @Param("params") ProdWorkOrderDto prodWorkOrder);

    List<ProdWorkOrderVo> apiGetWorkOrderList(@Param("params")ProdWorkOrder prodWorkOrder);



}
