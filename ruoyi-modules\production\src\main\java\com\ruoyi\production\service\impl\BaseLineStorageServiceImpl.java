package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.production.core.observable.AutoProcessEvent;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.lineStorage.UpdateStorageByWODto;
import com.ruoyi.production.domain.vo.BaseLineStorageVo;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.mapper.BaseLineStorageMapper;
import com.ruoyi.production.service.IBaseLineStorageService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.utils.RabbitMsgUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.groupingBy;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Service
public class BaseLineStorageServiceImpl extends ServiceImpl<BaseLineStorageMapper, BaseLineStorage> implements IBaseLineStorageService {
    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;
    @Autowired
    private RabbitMsgUtils rabbitMsgUtils;

    @Override
    public List<BaseLineStorageVo> listByCondition(BaseLineStorage lineStorage) {
        return this.getBaseMapper().listByCondition(lineStorage);
    }

    @Override
    public BaseLineStorage getEntityByOperationId(BaseLineStorage lineStorage, Boolean exceptSelf) {
        QueryWrapper<BaseLineStorage> queryWrapper = new QueryWrapper<>();
        if (lineStorage.getOperationId() != null) {
            queryWrapper.eq("operation_id", lineStorage.getOperationId());
        }
        //过滤本身--线边库可修改为自己已设置的工单
        if (exceptSelf) {
            queryWrapper.ne("id", lineStorage.getId());
        }
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
//    @Transactional
    public Boolean updateLineStorageWithRealtion(BaseLineStorage newStorage) {
        BaseLineStorage oldStorage = this.baseMapper.selectById(newStorage.getId());
        String newOperationId = newStorage.getOperationId();
        String oldOperationId = oldStorage.getOperationId();
        ProdWorkOrder oldOrder = orderService.getEntityByOperationId(oldOperationId);
        ProdWorkOrder newOrder = orderService.getEntityByOperationId(newOperationId);
        //校验合法性
        if (StringUtils.isNotBlank(newStorage.getOperationId())) {
            //判断线边库中是否存在
            BaseLineStorage baseLineStorage = getEntityByOperationId(newStorage, true);
            if (null != baseLineStorage) {
                throw new ServiceException("该工单已在线边库中！库位号：" + baseLineStorage.getAlias());
            }
        }

        //原来无工单
        if (oldOperationId == null || StringUtils.isBlank(oldOperationId)) {
            //现在有工单，更新富堪货位信息
            if (StringUtils.isNotBlank(newOperationId) && null != newOrder) {
                newOrder.setStatus(WorkOrderStatusEnum.WAIT.getCode());
                orderService.updateWorkOrderWithStatus(newOrder);
//                applicationContext.publishEvent(new OrderWaitEvent(this, newOrder));
            }
        }
        //原来有工单
        if (StringUtils.isNotBlank(oldOperationId)) {
            if (oldOrder != null) {
                //2023年9月12日 添加判断，防止已下发工单被修改
                if (oldOrder.getDistributed() == 1 && oldOrder.getStatus() == 3) {
                    throw new ServiceException(oldStorage.getStorageName() + "库位工单已下发，禁止修改！");
                }
                //工单状态逻辑处理
                //2022-12-16添加细分逻辑
                if (oldOrder.getStatus() < WorkOrderStatusEnum.DOING.getCode()) {
                    //重置货位信息--todo 添加mes的
                    oldOrder.setStatus(WorkOrderStatusEnum.CREATED.getCode());
                    orderService.updateWorkOrderWithStatus(oldOrder);
                }
            }
            //现在有工单，重置富堪库位信息，更新富堪新货位信息
            if (StringUtils.isNotBlank(newOperationId)) {
                //工单状态逻辑处理
                if (newOrder != null && newOrder.getStatus() < WorkOrderStatusEnum.DOING.getCode()) {
                    newOrder.setStatus(WorkOrderStatusEnum.WAIT.getCode());
                    //工单下发字段重置 todo 和梁慧交互？
                    newOrder.setDistributed(0);
                    orderService.updateWorkOrderWithStatus(newOrder);
//                    applicationContext.publishEvent(new OrderWaitEvent(this, newOrder));
                }
            }
        }
        this.updateById(newStorage);
        //触发自动加工流程
        if (null != newOrder) {
            applicationContext.publishEvent(new AutoProcessEvent(this, newOrder.getLineId()));
        }
        return true;
    }

    @Override
    public void updateLineStorage(UpdateStorageByWODto entity) {
        BaseLineStorage to = entity.getTo();
        BaseLineStorage from = entity.getFrom();
        ProdWorkOrder order = entity.getWorkOrder();
        String operationId = order == null ? "" : order.getOperationId();

        BaseLineStorage lineStorage = new BaseLineStorage();
        if (null != from) {//出库
            //获取对应产线的线边库对象
            lineStorage.setId(from.getId());
            lineStorage.setEmpty(1);
            lineStorage.setLineId(from.getLineId());
            lineStorage.setOperationId("");
            lineStorage.setFillerType(0);
        } else if (null != to) {//入库
            lineStorage.setId(to.getId());
            lineStorage.setEmpty(0);
            lineStorage.setFillerType(entity.getFillerType());
            lineStorage.setOperationId(operationId);
            lineStorage.setLineId(to.getLineId());
        }
        this.updateById(lineStorage);
        //触发自动加工流程
        if (null != to && StringUtils.isNotBlank(operationId)) {
            applicationContext.publishEvent(new AutoProcessEvent(this, to.getLineId()));
        }
        //机床加工件剩余一件提醒 todo

//        this.saveOrUpdateWithMQ(lineStorage);
        //发送线边库更改消息
//        BaseLineStorage baseLineStorage = this.baseMapper.selectById(lineStorage.getId());
        //2不相同，设置缓存，生产消息
        rabbitMsgUtils.updateStorageInfo(lineStorage.getLineId());
//        SysPlatformApi api = apiService.getEntityByCode("dcs_getStorageInfo");
//        WebSocketVo webSocketVo = new WebSocketVo();
//        webSocketVo.setUrl(api.getUrl());
//        webSocketVo.setMethod(api.getApiMethod());
//        webSocketVo.setLineId(lineStorage.getLineId());
//        rabbitTemplate.convertAndSend("direct_interface_exchange", "normal", JSON.toJSONString(webSocketVo));
    }

    @Override
    public BaseLineStorage getEntityByAlias(Long lineId, String alisa) {
        QueryWrapper<BaseLineStorage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("line_id", lineId);
        queryWrapper.eq("alias", alisa);
        return this.getOne(queryWrapper);
    }

    @Override
    public Integer getRemainMatNums(Long eqpId) {
        return this.baseMapper.getRemainMatNums(eqpId);
    }

    @Override
    public List<BaseLineStorageVo> getReadyOrderList(BaseLineStorage vo) {
        return this.baseMapper.getReadyOrderList(vo);
    }

    @Override
    public void checkWorkOrderNums(Long lineId, ProdWorkOrder order) {
        BaseLineStorage vo = new BaseLineStorage();
        vo.setLineId(lineId);
        List<BaseLineStorageVo> storageVoList = getReadyOrderList(vo);
        Map<String, List<BaseLineStorageVo>> groupByBillSchedulCode = storageVoList.stream()
                .filter(e -> e.getBillSchedulCode().equals(order.getBillSchedulCode()))
                .collect(groupingBy(BaseLineStorageVo::getBillSchedulCode));
        for (String s : groupByBillSchedulCode.keySet()) {
            List<BaseLineStorageVo> tempList = groupByBillSchedulCode.get(s);
            if (tempList.size() < 3) {
                MessageNews messageNews = new MessageNews();
                messageNews.setContent("线边库<毛坯件>数量提示：任务号-" + order.getBillCode() + "，工序号-" + order.getSchedulCode() + "，剩余数量：" + tempList.size() + " 件");
                messageNews.setCreaterId(1L);
                messageNews.setLineId(lineId);
                messageNews.setButtonGroupId(2L);
                remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
                break;
            }
        }
    }

}
