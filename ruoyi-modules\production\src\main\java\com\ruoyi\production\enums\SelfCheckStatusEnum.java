package com.ruoyi.production.enums;

public enum SelfCheckStatusEnum {

    INCONSISTENT_OVER(0, "不符/超差"),
    INCONSISTENT_QUALIFIED(1, "不符/合格"),
    CONSISTENT_OVER(2, "一致/超差"),
    CONSISTENT_QUALIFIED(3, "一致/合格");

    private final Integer code;
    private final String info;

    SelfCheckStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(int code) {
        for (SelfCheckStatusEnum type : values()) {
            if (type.getCode() == code) {
                return type.getInfo();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
