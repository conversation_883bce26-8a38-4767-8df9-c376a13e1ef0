package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.ProdWorkorderOperationRealation;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-05-30
 */
public interface IProdWorkorderOperationRealationService extends IService<ProdWorkorderOperationRealation> {

    List<ProdWorkOrder> getWorkOrderListByOperationId(Long OperationId);
    void removeRealtionByWorkOrder(ProdWorkOrder prodWorkOrder);

    void removeRealtionByWorkOrderList(List<ProdWorkOrder> prodWorkOrderList);

}
