package com.ruoyi.production.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.domain.vo.detection.DetectionReportVo;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.utils.ProdDateUtil;
import com.ruoyi.production.utils.RabbitMsgUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

@RestController
@Api(value = "工单controller", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("workOrder")
public class ProdWorkOrderController extends BaseModuleController {

    @Autowired
    private IProdWorkOrderService workOrderService;
    @Autowired
    private RabbitMsgUtils rabbitMsgUtils;
//    @Autowired
//    private IBaseEquipmentService equipmentService;

    //    DateFormat forcamFormat = new SimpleDateFormat("yyyy年M月d日 zzz ah:mm:ss");
    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @PostMapping("syncList/{timeUnit}")
    @ApiOperation("获取工单列表")
//    @Transactional
    public AjaxResult getList(@PathVariable("timeUnit") @ApiParam("日：day；周：week") String timeUnit) throws ParseException {
        //参数实体
        ProdWorkOrder param = new ProdWorkOrder();
        param.setLineId(getLineId());

        JSONArray requestParam = new JSONArray();
        //拼接参数，调用查询
        //时间单位
        JSONObject unit = new JSONObject();
        unit.put("name", "scheduledTimeType");
        unit.put("value", "DAY");
        requestParam.add(unit);
        //开始时间
        JSONObject starttime = new JSONObject();
        starttime.put("name", "scheduledStartDate");
        starttime.put("value", ProdDateUtil.getTodayStart());
        param.setSchedulStartDate(sdf.parse(ProdDateUtil.getTodayStart().replace("T", " ")));
        if ("day".equals(timeUnit)) {
            starttime.put("value", ProdDateUtil.getTodayStart());
            param.setSchedulStartDate(sdf.parse(ProdDateUtil.getTodayStart().replace("T", " ")));
        }

        requestParam.add(starttime);
        //结束时间
        JSONObject endtime = new JSONObject();
        endtime.put("name", "scheduledEndDate");
        endtime.put("value", ProdDateUtil.getFetureDate(6));
        param.setSchedulEndDate(sdf.parse(ProdDateUtil.getFetureDate(6).replace("T", " ")));
        if ("day".equals(timeUnit)) {
            endtime.put("value", ProdDateUtil.getTodayEnd());
            param.setSchedulEndDate(sdf.parse(ProdDateUtil.getTodayEnd().replace("T", " ")));
        }
        requestParam.add(endtime);

        //从富勘同步工单
        //暂时处理双系统存在  判断5号线跳过
//        2022-12-21 富堪下线
//        if (getLineId() != 5)
//            this.saveOrUpdateOrderFromForcam(requestParam, 0, getLineId());
//        param.setLineId(getLineId());

        if ("week".equals(timeUnit)) {
            param.setSchedulStartDate(null);
            param.setSchedulEndDate(null);
        }
        List<ProdWorkOrderVo> workOrderList = workOrderService.getTotalList(param);
        //处理映射中文
        workOrderList.forEach(order -> {
            Integer status = order.getStatus();
            order.setStatusName(WorkOrderStatusEnum.getInfoByKey(status));
        });

        //返回存储的工单列表
        return AjaxResult.success(workOrderList);
    }

    @ApiOperation("查询详细列表")
    @PostMapping("detailList")
    public AjaxResult getDetailList(@ApiParam("查询参数实体") @RequestBody ProdWorkOrderDto param) throws ParseException {
        if (param.getDateType() != null && param.getDateType().equals("day")) {
            param.setSchedulStartDate(sdf.parse(ProdDateUtil.getTodayStart().replace("T", " ")));
            param.setSchedulEndDate(sdf.parse(ProdDateUtil.getTodayEnd().replace("T", " ")));
        }
        param.setLineId(getLineId());
        List<ProdWorkOrderVo> workOrderList = workOrderService.listByCondition(param);
        //处理映射中文
        workOrderList.forEach(order -> {
            Integer status = order.getStatus();
            order.setStatusName(WorkOrderStatusEnum.getInfoByKey(status));
        });
        return AjaxResult.success(workOrderList);
    }

    @ApiOperation("查询工单列表")
    @PostMapping("listByCondition")
    public AjaxResult getList(@RequestBody ProdWorkOrderDto param) {
        param.setLineId(getLineId());
        return AjaxResult.success(workOrderService.listByCondition(param));
    }

    @ApiOperation("根据状态获取任务号列表")
    @GetMapping("/getBillcodeList/{status}")
    public AjaxResult getBillcodeList(@PathVariable("status") Integer status) {
        ProdWorkOrder prodWorkOrder = new ProdWorkOrder();
        prodWorkOrder.setStatus(status);
        prodWorkOrder.setLineId(getLineId());
        return AjaxResult.success(workOrderService.getBillCodeList(prodWorkOrder));
    }

    @ApiOperation("在线监测--根据任务号获取所有任务号列表（完工状态的）")
    @GetMapping("/getDoneBillCodeList")
    public AjaxResult getDoneBillCodeList() {
        ProdWorkOrder prodWorkOrder = new ProdWorkOrder();
        prodWorkOrder.setLineId(getLineId());
        return AjaxResult.success(workOrderService.getDoneBillCodeList(prodWorkOrder));
    }

    @ApiOperation("根据任务号获取工序列表")
    @GetMapping("getSchedulCodeList")
    public AjaxResult getSchedulCodeList(@RequestParam("billcode") String billcode) {
        return AjaxResult.success(workOrderService.getSchedulCodeList(billcode));
    }

    @ApiOperation("根据任务号和工序号获取完工列表")
    @GetMapping("getDoneList")
    public AjaxResult getDoneList(@RequestParam(required = false, value = "billcode") String billcode,
                                  @RequestParam(required = false, value = "schedulCode") String schedulCode) {
        ProdWorkOrderDto dto = new ProdWorkOrderDto();
        dto.setBillCode(billcode);
        dto.setSchedulCode(schedulCode);
        dto.setStatus(WorkOrderStatusEnum.DONE.getCode());
        dto.setLineId(getLineId());
        return AjaxResult.success(workOrderService.getEntityListByParam(dto));
    }

    @ApiOperation("分页查询操作记录列表（预调至线边接驳台弹窗专用----过滤已加工和已操作的记录）")
    @PostMapping("listForPage")
    public AjaxResult listForPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdWorkOrderDto prodWorkOrderDto) {
        prodWorkOrderDto.setLineId(getLineId());
        return AjaxResult.success(workOrderService.listForPage(prodWorkOrderDto, pageNum, pageSize));
    }

    @ApiOperation("新---分页查询操作记录列表（预调至线边接驳台弹窗专用----过滤已加工和已操作的记录）机床组改版--过滤没设置的单据")
    @PostMapping("listForPageToDock")
    public AjaxResult listForPageToDock(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdWorkOrderDto prodWorkOrderDto) {
        prodWorkOrderDto.setLineId(getLineId());
        return AjaxResult.success(workOrderService.listForPageToDock(prodWorkOrderDto, pageNum, pageSize));
    }

    @ApiOperation("分页查询操作记录列表")
    @PostMapping("pageList")
    public AjaxResult pageList(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdWorkOrderDto prodWorkOrderDto) {
        prodWorkOrderDto.setLineId(getLineId());
        return AjaxResult.success(workOrderService.pageList(prodWorkOrderDto, pageNum, pageSize));
    }

    @ApiOperation("获取质量报表数据")
    @PostMapping("listForPageDetectionReport")
    public AjaxResult getDetectionReportData(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdWorkOrderDto prodWorkOrderDto) {
        prodWorkOrderDto.setLineId(getLineId());
        return AjaxResult.success(workOrderService.listForPageDetectionReport(prodWorkOrderDto, pageNum, pageSize));
    }

    @Log(title = "获取质量报表数据", businessType = BusinessType.EXPORT)
    @RequiresPermissions("production:detectionReport:export")
    @PostMapping("qualityReport/export")
    public void export(HttpServletResponse response,ProdWorkOrderDto prodWorkOrderDto)
    {
        prodWorkOrderDto.setLineId(getLineId());
        List<DetectionReportVo> list = workOrderService.getDetectionReportData(prodWorkOrderDto);
        ExcelUtil<DetectionReportVo> util = new ExcelUtil<>(DetectionReportVo.class);
        util.exportExcel(response, list, "质量报表");
    }

    @ApiOperation("根据任务号和工序号删除工单")
    @DeleteMapping("deleteAllWorkOrders")
    public AjaxResult deleteAllWorkOrders(@RequestBody ProdWorkOrderDto prodWorkOrderDto) {
        if (null == prodWorkOrderDto.getBillCode() || null == prodWorkOrderDto.getSchedulCode()) {
            return AjaxResult.error("缺少任务号或工序号，无法删除");
        }
        prodWorkOrderDto.setLineId(getLineId());
        return AjaxResult.success(workOrderService.deletedByBillSchedulCode(prodWorkOrderDto));
    }

    @PutMapping(value = "forceDoneWorkOrder/{opreationId}/{eqpId}")
    @Transactional
    public AjaxResult forceDoneWorkOrder(@PathVariable("opreationId") String opreationId,
                                         @PathVariable("eqpId") Long eqpId) {
        log.info("********** 强制完工 >>> " + opreationId);
        ProdWorkOrder order = workOrderService.getEntityByOperationId(opreationId);
        if (order == null) {
            return AjaxResult.error("工单未找到");
        }
        if(!order.getStatus().equals(WorkOrderStatusEnum.DOING.getCode())){
            return AjaxResult.error("工单状态非加工中，无法强制完工！");
        }
        workOrderService.forceDoneWorkOrder(order);
        rabbitMsgUtils.updateProcessTraceList(getLineId());
        return AjaxResult.success();
    }

    public static void main(String[] args) {
        String a = "0020202100";
        System.out.println(a.replaceFirst("^0*", ""));
    }


}
