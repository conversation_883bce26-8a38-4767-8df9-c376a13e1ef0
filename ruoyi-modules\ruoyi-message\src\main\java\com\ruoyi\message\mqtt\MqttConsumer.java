package com.ruoyi.message.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.message.domian.EquipmentSpindleCollisionData;
import com.ruoyi.message.service.IEquipmentSpindleCollisionDataService;
import com.ruoyi.production.api.RemoteEquipmentService;
import com.ruoyi.production.api.domain.BaseEquipment;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;
import java.util.concurrent.TimeUnit;

//@Component
public class MqttConsumer implements MqttCallbackExtended {

    private DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static Logger log = LoggerFactory.getLogger(MqttConsumer.class);

    MqttClient client;

    MqttConnectOptions options;

    @Value("${mqtt.broker.serverUri}")
    private String MqttUrl;

    @Value("${mqtt.topic}")
    protected String topic;

    @Value("${mqtt.qos}")
    protected int qos;

    @Value("${mqtt.broker.cleansession}")
    private boolean cleansession;

    @Value("${mqtt.broker.automaticreconnect}")
    private boolean automaticreconnect;

    @Autowired
    private RemoteEquipmentService remoteEquipmentService;

    @Autowired
    private IEquipmentSpindleCollisionDataService collisionDataService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 初始化mqtt消费者
     */
    @PostConstruct
    public void init() {
        try {
            client = new MqttClient(MqttUrl, java.util.UUID.randomUUID().toString(), new MemoryPersistence());
            // log.info("********** 初始化mqtt客户端" + client);

            // log.info("********** 配置mqtt客户端配置信息");
            options = new MqttConnectOptions();
            options.setCleanSession(cleansession);
            options.setConnectionTimeout(10);
            options.setKeepAliveInterval(20);
            options.setAutomaticReconnect(automaticreconnect);
            options.setMqttVersion(MqttConnectOptions.MQTT_VERSION_DEFAULT);

            client.setCallback(this);
            client.connect(options);
            client.subscribe(topic, qos);
            log.info("********** 初始化mqtt客户端！");
        } catch (MqttSecurityException e) {
            log.info("********** 初始化mqtt客户端异常：MqttSecurityException！\n" + e.getLocalizedMessage());
            throw new ServiceException(e.getLocalizedMessage());
        } catch (MqttException e) {
            log.info("********** 初始化mqtt客户端异常：MqttException！\n" + e.getLocalizedMessage());
            throw new ServiceException(e.getLocalizedMessage());
        }

    }


    /**
     * 断开链接，重连操作
     */
    @Override
    public void connectionLost(Throwable cause) {

        // log.info("********** MQTT>>" + cause + " 连接断开，1S之后尝试重连...");
        while (true) {
            try {
                if (!client.isConnected()) {
                    log.info("********** 重连mqtt客户端");
                    client.setCallback(this);
                    client.connect(options);
                }
                if (client.isConnected()) {
                    log.info("********** mqtt客户端已重新链接");
                    break;
                }
            } catch (MqttException e) {
                log.info("********** mqtt重连异常");
                throw new ServiceException(e.getLocalizedMessage());
            }
        }
    }

    /**
     * 连接成功+订阅消息
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("********** 开始订阅.............");
        try {
            client.subscribe(topic, qos);
            log.info("********** 订阅主题>>" + topic + ",qos>>" + qos);
        } catch (MqttException e) {
            log.info("********** 订阅主题：" + topic + ",qos:" + qos + "异常：" + e.getLocalizedMessage());
            throw new ServiceException(e.getLocalizedMessage());
        }
        log.info("********** 已经订阅.........");
    }

    /**
     * 收到新的消息推送
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
//        log.info("********** Consumer接收到的数据\n topic:" + topic + "\n message:" + message.toString());

        // subscribe后得到的消息会执行到这里面
//        String result = new String(message.getPayload(), "UTF-8");
        //log.info("********** 接收消息主题 >> " + topic + " 接收消息Qos >> " + message.getQos() + " 接收消息内容 >> " + result);

/*        //这里可以针对收到的消息做处理
        ElecMeasuringPointEntity elecMeasuringPointEntity = new ElecMeasuringPointEntity();
        elecMeasuringPointEntity.setUEnterpriseId(1111);
//插入方法
        PushCallback.elecMeasuringPointDao.insert(elecMeasuringPointEntity);*/

        try {
            String mach = topic.substring(4, 7);
            JSONObject jsonObject = JSON.parseObject(message.toString());
            String tKey = jsonObject.getString("TKey");
            Date syncTime = sdf.parse(jsonObject.getString("Time"));
            String value = jsonObject.getString("Tvalue");
            EquipmentSpindleCollisionData collisionData = EquipmentSpindleCollisionData.builder()
                    .syncTime(syncTime)
                    .mach(mach)
                    .coordinate(tKey)
                    .value(value)
                    .build();
//        System.out.println(JSON.toJSONString(data));
            collisionDataService.save(collisionData);

            String xyzKey = ProductionConstans.REDIS_COLLISION_XYZ + mach + ":" + syncTime.getTime();

            redisTemplate.opsForSet().add(xyzKey, tKey);
            redisTemplate.expire(xyzKey, 20, TimeUnit.SECONDS);

            Set<String> idSet = redisTemplate.opsForSet().members(xyzKey);
            if (idSet != null && idSet.size() < 3) {
                return;
            }
            BaseEquipment baseEquipment = remoteEquipmentService.getEquipmentByMqttAlias(mach, SecurityConstants.INNER).getData();
            if (null == baseEquipment) {
                return;
            }
            WebSocketVo webSocketVo = new WebSocketVo();
            webSocketVo.setUrl("dcs_getCollisionData");
            webSocketVo.setLineId(baseEquipment.getLineId());

            rabbitTemplate.convertAndSend("direct_interface_exchange", "xyz", JSON.toJSONString(webSocketVo));

//            collisionDataService.saveOrUpdateWithMQ(data);


        } catch (Exception e) {
            e.printStackTrace();
            //throw new ServiceException(e);
        }

    }

    /**
     * 消息处理结束
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        // log.info("********** 消息处理结束时调用");
    }
}