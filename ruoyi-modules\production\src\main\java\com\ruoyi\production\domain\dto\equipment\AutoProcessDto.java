package com.ruoyi.production.domain.dto.equipment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AutoProcessDto {
    @ApiModelProperty(value = "设备ID")
    @NotNull(message = "设备id不可为空")
    private Long eqpId;
    @ApiModelProperty(value = "自动加工")
    @NotNull(message = "设备是否自动加工值不可为空")
    private boolean autoProcess;
}