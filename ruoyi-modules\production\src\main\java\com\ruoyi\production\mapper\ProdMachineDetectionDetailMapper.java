package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.production.domain.ProdMachineDetectionDetail;
import com.ruoyi.production.domain.vo.detection.GetColumnVo;
import com.ruoyi.production.domain.vo.detection.ProdMachineDetectionDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
public interface ProdMachineDetectionDetailMapper extends BaseMapper<ProdMachineDetectionDetail> {

    List<ProdMachineDetectionDetailVo> listByCondition(@Param("params") ProdMachineDetectionDetail detail);

    IPage<ProdMachineDetectionDetailVo> listForPage(IPage<ProdMachineDetectionDetailVo> page, @Param("params") ProdMachineDetectionDetail detail);

    /**
     * 根据条形码查找所有的检测属性
     *
     * @param bscode
     * @return
     */
    List<GetColumnVo> getColumns(@Param("bscode") String bscode);

    List<ProdMachineDetectionDetailVo> getQualityReportData(@Param("params") Map map);

}
