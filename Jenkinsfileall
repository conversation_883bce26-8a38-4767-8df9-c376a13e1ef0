pipeline {
    //指定任务在哪个集群节点上执行
    agent any
    //声明环境变量，方便后面使用
    environment {
        harbor_user='admin'
        harbor_password='123456'
        harbor_addr='************'
        harbor_repo='5808'
        JOB_NAME='5808service'
    }

    tools {
        jdk 'jdk8'

    }
    stages {
        stage('拉取git仓库代码') {
            steps {
/*             checkout scmGit(branches: [[
            name: '*//*  *//*  *//*  *//* master']],
            extensions: [],
            userRemoteConfigs:
            [[credentialsId: '5b9c0df6-05e3-4715-af14-215cf7dbe863',
//             [[credentialsId: 'a907da43-ac4e-4bcc-8d49-09230e696771',
            url: 'http://*************:8090/root/159.git']]) */

                echo '跳过git拉取代码--使用jenkins自己拉的代码-SUCCESS'
            }
        }
        stage('通过jdk8构建项目') {
            steps {

                echo '==============================构建代码开始=============================='
//                 sh "mvn clean package"
//                sh "mvn clean "
//                sh "mvn install -Dmaven.test.skip=true "
                sh "mvn clean install -Dmaven.test.skip=true"
                //编译，构建本地镜像
//                 sh "mvn clean package dockerfile:build"
                // Run the maven build
                echo '==============================构建代码结束=============================='
            }
        }
        /*stage('通过sonarqube做代码质量检测') {
            steps {
                echo '代码质量检测-SUCCESS'
            }
        }
        stage('通过docker制作自定义镜像') {
            steps {
                sh "cd ${project_name}"
//                sh "cp ./ruoyi-admin/target/ruoyi-admin.jar ."
                sh "docker build -t  ${JOB_NAME}:latest ."
                sh "docker tag ${JOB_NAME}:latest ${harbor_addr}/${harbor_repo}/${JOB_NAME}:latest"
                echo 'docker制作自定义镜像-SUCCESS'
            }
        }
        stage('将自定义镜像推送到harbor仓库中') {
            steps {
                // 登录
                sh "docker login -u ${harbor_user} -p ${harbor_password} ${harbor_addr}"
                //上传镜像
                sh "docker push ${harbor_addr}/${harbor_repo}/${JOB_NAME}:latest"
                //delete old images
                sh "docker rmi -f ${JOB_NAME}"
                sh "docker rmi -f ${harbor_addr}/${harbor_repo}/${JOB_NAME}"
            }
        }


        stage('通过publish  over  ssh将sevice文件和deployment文件发送到K8S主节点上') {
            steps {
                sshPublisher(publishers: [sshPublisherDesc(configName: '************',
                        transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand: '', execTimeout: 120000, flatten: false,
                                makeEmptyDirs: false, noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: '',
                                remoteDirectorySDF: false, removePrefix: '', sourceFiles: 'k8s/deploy.yaml')],
                        usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
                echo '发送部署文件-SUCCESS'
            }
        }
        stage('在k8S上执行部署命令') {
            steps {
                sh "ssh root@************ kubectl delete -f /root/k8s/deploy.yaml"
                sh "ssh root@************ kubectl apply  -f /root/k8s/deploy.yaml"
//                     sh "ssh root@************  rm -f /root/k8s/deploy.yaml"
                echo 'k8S上执行部署命令-SUCCESS'

            }
        }*/

    }
}
