package com.ruoyi.production.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Slf4j
@Configuration
public class RabbitTemplateConfig implements RabbitTemplate.ReturnsCallback, RabbitTemplate.ConfirmCallback {

    @Autowired
    private RabbitTemplate rabbitTemplate;

//    @Autowired
//    private RemoteMessageMqRecordService remoteMessageMqRecordService;

    @PostConstruct
    public void init() {
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setConfirmCallback(this);
        rabbitTemplate.setReturnsCallback(this);
    }

    /**
     * 消息是否到达交换机的回调
     *
     * @param correlationData
     * @param ack
     * @param cause
     */
    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        if (ack) {
//            log.info("********** 消息已发送到交换器 cause:{} - {}", cause, correlationData);
        } else {
            log.error("@@@@@@@@@@ 消息未发送到交换器 cause:{} - {}", cause, correlationData);
        }
        //消息发送成功，更新数据库请求成功状态
//        MessageMqRecord mqRecord = new MessageMqRecord();
//        mqRecord.setId();
//        Map<String ,String > params = new HashMap<>();
//        params.put("msgCguid" , correlationData.getId());
//        params.put("reqstatus" ,ack ? "1":"0" );
//        mqMessageService.updateReqStatus(params);
//        System.out.println("confirm--:correlationData:"+correlationData+",ack:"+ack+",cause:"+cause);
    }

    /**
     * 消息被退回的回调，即消息为成功到达队列的回调
     * <p>
     * 相当于消息如果没有到达队列，则这个方法会被触发
     *
     * @param returnedMessage
     */
    @Override
    public void returnedMessage(ReturnedMessage returnedMessage) {
        log.error("@@@@@@@@@@ 消息未到达队列{}",returnedMessage);
    }

}
