package com.ruoyi.message.api.factory;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.MessageNews;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteMessageNewsFallbackFactory implements FallbackFactory<RemoteMessageNewsService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteMessageNewsFallbackFactory.class);

    @Override
    public RemoteMessageNewsService create(Throwable throwable) {
        log.error("@@@@@@@@@@  消息服务调用失败:{}", throwable.getMessage());
        return new RemoteMessageNewsService() {
            @Override
            public R add(MessageNews messageNews, String source) {
                return R.fail("添加 弹窗信息 失败:" + throwable.getMessage());
            }

            @Override
            public R<JSONObject> getPopNewsByLineId(Long id, String source) {
                return R.fail("获取 弹窗信息 失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> reset(Long id, String source) {
                return R.fail("重置信息 失败:" + throwable.getMessage());
            }
        };
    }
}
