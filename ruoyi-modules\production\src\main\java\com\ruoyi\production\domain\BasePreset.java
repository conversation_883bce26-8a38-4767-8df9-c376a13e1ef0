package com.ruoyi.production.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工作组（一个预调台+一个中转站）
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "BasePreset对象", description = "工作组（一个预调台+一个中转站）")
public class BasePreset extends MyBaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预调台编码")
    private String presetCode;

    @ApiModelProperty(value = "预调台名称")
    private String presetName;

    private String alias;

    private Long lineId;


}
