package com.ruoyi.production.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDto;
import com.ruoyi.production.domain.vo.iot.DelOrTopOpVo;
import com.ruoyi.production.service.IProdOperationTraceService;
import com.ruoyi.production.utils.ProdDateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


@RestController
@Api(value = "预调操作记录-控制器", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("operationTrace")
public class ProdOperationTraceController extends BaseModuleController {

    @Autowired
    private IProdOperationTraceService operationTraceService;

    @ApiOperation("查询操作记录列表")
    @PostMapping("listByCondition")
    public AjaxResult listByCondition(@RequestBody ProdOperationTrace operationTrace) {
        operationTrace.setLineId(getLineId());
        return AjaxResult.success(operationTraceService.listByCondition(operationTrace));
    }

    @ApiOperation("删除跟踪记录")
    @Log(title = "删除操作单记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult deleteById(@PathVariable("id") Long id) {
        String url = platformApiService.getEntityByCode("iot_delop").getUrl();
        DelOrTopOpVo delOpVo = DelOrTopOpVo.builder()
                .opid(id)
                .wbcid(getPresetInfo().getAlias())
                .wplid(getBaseLine().getAlias())
                .build();
        //发送请求
        String result = HttpRequest.post(url)
                .body(JSON.toJSONString(delOpVo))
                .timeout(3000)
                .execute().body();
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code")) && !"-50".equals(jsonObject.getString("code")))
            throw new ServiceException(jsonObject.toJSONString());
        return AjaxResult.success(operationTraceService.removeById(id));
    }

    @ApiOperation("操作单按钮")
    @PostMapping("/op/{op}")
    public AjaxResult op(@PathVariable("op") String op, @RequestBody ProdOperationTrace operationTrace) {
        DelOrTopOpVo delOpVo = DelOrTopOpVo.builder().build();
        String url = "";
        if ("top".equals(op)) {//置顶
            delOpVo.setOpid(operationTrace.getId());
            delOpVo.setWbcid(getPresetInfo().getAlias());
            delOpVo.setWplid(getBaseLine().getAlias());
            url = platformApiService.getEntityByCode("iot_topop").getUrl();
        }

        //发送请求
        String result = HttpRequest.post(url)
                .body(JSON.toJSONString(delOpVo))
                .timeout(3000)
                .execute().body();
        JSONObject jsonObject = JSON.parseObject(result);
        if (!"0".equals(jsonObject.getString("code")))
            throw new ServiceException(jsonObject.toJSONString());
        return AjaxResult.success();
    }


    @ApiOperation("分页查询操作记录列表")
    @PostMapping("listForPage")
    public AjaxResult listForPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdOperationTraceDto operationTraceDto) {

        //筛选范围添加
        operationTraceDto.setLineId(getLineId());
        operationTraceDto.setTimeStart(ProdDateUtil.getYesterdayStart());
        operationTraceDto.setTimeEnd(ProdDateUtil.getTodayEnd());
        return AjaxResult.success(operationTraceService.listForPage(operationTraceDto, pageNum, pageSize));
    }


}
