<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.BaseLineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.BaseLine">
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
        <result column="remark" property="remark" />
        <result column="line_name" property="lineName" />
        <result column="line_code" property="lineCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_by,
        create_time,
        update_by,
        update_time,
        id,
        deleted,
        remark, line_name, line_code
    </sql>

</mapper>
