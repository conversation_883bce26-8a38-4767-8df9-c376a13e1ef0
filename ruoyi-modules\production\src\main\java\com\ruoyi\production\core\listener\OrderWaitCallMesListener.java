package com.ruoyi.production.core.listener;

import com.ruoyi.production.core.observable.OrderWaitEvent;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.utils.CallMesUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class OrderWaitCallMesListener implements ApplicationListener<OrderWaitEvent> {

    @Autowired
    private CallMesUtil callMesUtil;

    @Override
    public void onApplicationEvent(OrderWaitEvent event) {
        ProdWorkOrder order = event.getWorkOrder();
        //通知mes物料齐套
        callMesUtil.requestMaterail(order);
    }
}
