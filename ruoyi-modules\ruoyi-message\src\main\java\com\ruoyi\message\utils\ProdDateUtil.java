package com.ruoyi.message.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class ProdDateUtil {
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private static final String TIME_START = "T00:00:00";
    private static final String TIME_END = "T23:59:59";

    public static String getThisMonthStart() {
        Calendar thisMonthFirstDateCal = Calendar.getInstance();
        thisMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
        String thisMonthFirstTime = sdf.format(thisMonthFirstDateCal.getTime()) + " 00:00:00";
        return thisMonthFirstTime;
    }

    public static String getThisMonthEnd() {
        Calendar thisMonthEndDateCal = Calendar.getInstance();
        thisMonthEndDateCal.set(Calendar.DAY_OF_MONTH, thisMonthEndDateCal.getActualMaximum(Calendar.DAY_OF_MONTH));
        String thisMonthEndTime = sdf.format(thisMonthEndDateCal.getTime()) + " 23:59:59";
        return thisMonthEndTime;
    }

    public static String getThisWeekStart() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_WEEK, 2);                //本周的第一天，写1获取到的是周日，2获取到的是周一
        return sdf.format(c.getTime()) + TIME_START;
    }

    public static String getThisWeekEnd() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_WEEK, 7);                //程序里一周的最后一天是周六，不符合中国人习惯，所以+1
        c.add(c.DATE, 1);
        return sdf.format(c.getTime()) + TIME_END;
    }

    public static String getFetureDate(int past) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + past);
        Date today = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String result = format.format(today) + TIME_END;
        return result;
    }

    public static String getTodayStart() {
        Date date = new Date();
        return sdf.format(date) + TIME_START;
    }

    public static String getTodayEnd() {
        Date date = new Date();
        return sdf.format(date) + TIME_END;
    }

    public static String getYesterdayStart() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, -1);
//        System.out.println(sdf.format(c.getTime()));
        return sdf.format(c.getTime()) + TIME_START;
    }


    //public ststic Date formatForcamDate(){
    //
    //}


    public static Date getMinuteToNow(Integer minute) {
        Calendar now = Calendar.getInstance();
//        now.setTime(date);
        now.add(Calendar.MINUTE, minute);
        Date newDate = now.getTime();
        return newDate;
    }


    public static void main(String[] args) throws ParseException {
        //DateFormat chinaFormat = new SimpleDateFormat("yyyy年MM月dd日 zzz ahh:mm:ss");
        // String date = "2022年05月01日 CST 下午02:06:00";
        //
        //DateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //
        // Date date1 = chinaFormat.parse(date);
        //System.out.println(sdf2.format(date1));
        // date1.setMonth(6);
        //System.out.println(sdf2.format(date1));
        //System.out.println(sdf2.format(date1));
//        System.out.println(ProdDateUtil.getYesterdayStart());
//        System.out.println(getThisWeekEnd());
//        System.out.println(getFetureDate(6));
        System.out.println(getThisMonthStart());
        System.out.println(getThisMonthEnd());
    }


}
