package com.ruoyi.message.test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController()
public class ObserverController {
    @Autowired
    private ApplicationContext applicationContext;

    @GetMapping("test")
    public String test() {
        EquipmentTestObservable order = new EquipmentTestObservable(123);
        applicationContext.publishEvent(order);
        return "success";
    }
}
