package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.BasePreset;

import java.util.List;

/**
 * <p>
 * 工作组（一个预调台+一个中转站） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
public interface IBasePresetService extends IService<BasePreset> {

    BasePreset getEntityByAlias(String alias);

    List<BasePreset> getLitByLineId(Long lineId);

}
