package com.ruoyi.production.enums;

/**
 * 货位类型
 *
 * <AUTHOR>
 */
public enum DigitalTWorkOrderStatusEnum {

    WAIT(0, "待加工"),
    DOING(1, "加工中"),
    DONE(2, "待检测"),
    STOP(3, "停止"),
    CREATED(4, "已创建"),
    OUSTOCK(5, "已出库"),
    INSTOCK(6, "已入库");


//    CREATED(0, "已创建"),
//    OUSTOCK(1, "已出库"),
//    LOAD(2, "已装调"),
//    WAIT(3, "待加工"),
//    DOING(4, "加工中"),
//    DONE(5, "待检测"),
//    INSTOCK(6, "已入库");

    private final Integer code;
    private final String info;

    DigitalTWorkOrderStatusEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(Integer code) {
        for (DigitalTWorkOrderStatusEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
