package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.vo.BaseLineStorageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface BaseLineStorageMapper extends BaseMapper<BaseLineStorage> {
    List<BaseLineStorageVo> listByCondition(@Param("params") BaseLineStorage lineStorage);
    Integer getRemainMatNums(Long eqpId);
    List<BaseLineStorageVo> getReadyOrderList(@Param("params") BaseLineStorage lineStorage);

}
