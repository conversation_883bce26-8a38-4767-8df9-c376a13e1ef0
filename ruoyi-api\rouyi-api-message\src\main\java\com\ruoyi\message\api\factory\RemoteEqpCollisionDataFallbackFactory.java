package com.ruoyi.message.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.message.api.RemoteEqpCollisionDataService;
import com.ruoyi.message.api.domain.EquipmentSpindleCollisionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteEqpCollisionDataFallbackFactory implements FallbackFactory<RemoteEqpCollisionDataService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteEqpCollisionDataFallbackFactory.class);

    @Override
    public RemoteEqpCollisionDataService create(Throwable throwable) {
        log.error("@@@@@@@@@@  消息服务调用失败:{}", throwable.getMessage());
        return new RemoteEqpCollisionDataService() {
            @Override
            public R<List<EquipmentSpindleCollisionData>> getCollisionDataListByMap(HashMap params, String source) {
                return R.fail("获取 EquipmentSpindleCollisionData 失败:" + throwable.getMessage());
            }
        };
    }
}
