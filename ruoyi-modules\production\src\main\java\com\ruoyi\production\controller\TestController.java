package com.ruoyi.production.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.webservice.SoapClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.annotation.ResubmitLock;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.production.domain.BaseLine;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.service.*;
import com.ruoyi.production.utils.CallMesUtil;
import com.ruoyi.production.utils.RabbitMsgUtils;
import com.ruoyi.production.utils.SmbUtil;
import com.ruoyi.production.utils.SyncEqpStatusUtil;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.StringReader;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@RestController
@RequestMapping("api/test")
@Api(value = "产线-控制器", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class TestController {
    @Autowired
    private RedisService redisService;
    @Autowired
    private CallMesUtil callMesUtil;
    @Autowired
    private ISysPlatformApiService platformApiService;
    @Autowired
    private IProdWorkOrderService prodWorkOrderService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private IBaseEquipmentService baseEquipmentService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private SyncEqpStatusUtil syncEqpStatusUtil;
    @Autowired
    private SmbUtil smbUtil;
    @Autowired
    private RemoteMessageNewsService messageNewsService;
    @Autowired
    private IBaseLineService lineService;
    @Autowired
    private ISysPlatformApiService apiService;
    @Autowired
    private RabbitMsgUtils rabbitMsgUtils;
    @Autowired
    private IBaseLineStorageService storageService;

    @ApiOperation("删除redis指定key")
    @GetMapping("deleteRedisKey")
    public AjaxResult deleteRedisKey(@RequestParam String key) {
        return AjaxResult.success(redisService.deleteObject(key));
    }

    @ApiOperation("根据产线获取用户列表")
    @GetMapping("getSysUser")
    public AjaxResult test3(@RequestParam Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setLineId(userId);
//        remoteUserService.listByCondition(sysUser,SecurityConstants.INNER);
        R<?> registerResult = remoteUserService.listByCondition(sysUser, SecurityConstants.INNER);
        return AjaxResult.success(registerResult);
    }

    @ApiOperation("测试redis锁")
    @GetMapping("testRedisLock")
    public AjaxResult testRedisLock() {
        boolean flag = redisService.tryLock("testRedis", "123456", 100, -1);
        return AjaxResult.success(flag);
    }

    @ApiOperation("测试redission锁")
    @PostMapping("testRedissionLock")
    @ResubmitLock
    public AjaxResult testRedissionLock(@RequestBody ProdWorkOrder order) {
        return AjaxResult.success(order);
    }

    @ApiOperation("测试redis锁")
    @GetMapping("testRedisUnLock")
    public AjaxResult testRedisUnLock() {
        redisService.unLock("testRedis", "123");
        return AjaxResult.success();
    }

    @ApiOperation("加锁")
    @GetMapping("test2")
    public AjaxResult test2(@RequestParam Long lineId, @RequestParam String cmd, @RequestParam Long value) {
//        prodRedisUtil.setAssociation(lineId, cmd, value);
        AtomicReference atomicReference = new AtomicReference();
        return AjaxResult.success();
    }

    @ApiOperation("解锁")
    @GetMapping("unlock")
    public AjaxResult test3(@RequestParam String prefix, @RequestParam Long value) {
//        prodRedisUtil.removeAssociation(prefix, value);
        return AjaxResult.success();
    }

    @ApiOperation("产线物料不足提示")
    @GetMapping("checkMachMatNums")
    public AjaxResult checkMachMatNums(@RequestParam Long lineId, @RequestParam String eqpCode) {
//        prodRedisUtil.removeAssociation(prefix, value);
        baseEquipmentService.checkMachMatNums(lineId, eqpCode);
        return AjaxResult.success();
    }

    @ApiOperation("mes通知开工")
    @PostMapping("mesStartOrder")
    public AjaxResult mesStartOrder(@RequestBody ProdWorkOrder order) {
        order = prodWorkOrderService.getEntityByOperationId(order.getOperationId());
        callMesUtil.startOrder(order);
        return AjaxResult.success();
    }

    @ApiOperation("mes测试报工")
    @PostMapping("finishOrder")
    public AjaxResult finishOrder(@RequestParam Long operationId) {
        String mes_report = platformApiService.getEntityByCode("mes_finishOrder").getUrl();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operationId", operationId);
        String result = HttpRequest.post(mes_report).timeout(3000)
                .body(jsonObject.toJSONString())
                .execute().body();
        return AjaxResult.success(result);
    }

    @ApiOperation("测试成品到预调台")
    @PostMapping("object2Parset")
    public AjaxResult test4() throws Exception {
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setLineId(6L);
        rabbitTemplate.convertAndSend("direct_interface_exchange", "pop", JSON.toJSONString(webSocketVo));
        return AjaxResult.success();
    }

    @ApiOperation("测试物品到预调台")
    @PostMapping("object2Parset2/{id}")
    public AjaxResult object2Parset(@PathVariable Long id) throws Exception {
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setLineId(id);
        messageNewsService.reset(id, SecurityConstants.INNER);
        rabbitTemplate.convertAndSend("direct_interface_exchange", "pop", JSON.toJSONString(webSocketVo));
        return AjaxResult.success();
    }

    @ApiOperation("同步托盘信息")
    @PostMapping("syncTrayInfo")
    public AjaxResult syncTrayInfo() throws Exception {
        List<BaseLine> lineList = lineService.list();
        for (BaseLine baseLine : lineList) {
            syncEqpStatusUtil.syncTrayInfo(baseLine.getId());
        }
        return AjaxResult.success();
    }

    @ApiOperation("同步产线设备状态信息")
    @PostMapping("syncEqpStatus")
    public AjaxResult syncEqpStatus() throws Exception {
        List<BaseLine> lineList = lineService.list();
        for (BaseLine baseLine : lineList) {
            syncEqpStatusUtil.syncEqpStatus(baseLine.getId());
        }
        return AjaxResult.success();
    }

    @ApiOperation("测试读取远程文件")
    @GetMapping("getSMBFile/{id}")
    public AjaxResult getSMBFile(@PathVariable String id) throws Exception {
        smbUtil.getFileAndSave(prodWorkOrderService.getEntityByOperationId(id));
        return AjaxResult.success();
    }

    @ApiOperation("本地测试解析文件")
    @GetMapping("getSMBFile2/{id}/{fileName}")
    public AjaxResult getSMBFile2(@PathVariable String id, @PathVariable String fileName) throws Exception {
//        smbUtil.getFileAndSave2(id,fileName);
        return AjaxResult.success();
    }

    @ApiOperation("本地测试解析文件")
    @GetMapping("getLineStorage/{lineId}")
    public AjaxResult getSMBFile2(@PathVariable long lineId) throws Exception {
        QueryWrapper<BaseLineStorage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("line_id",lineId);
        List<BaseLineStorage> storageList = storageService.list(queryWrapper);
//        smbUtil.getFileAndSave2(id,fileName);
        return AjaxResult.success(storageList);
    }

//    @ApiOperation("本地测试解析文件")
//    @GetMapping("updateOrder")
//    public AjaxResult updateOrder() throws Exception {
////        smbUtil.getFileAndSave2(id,fileName);
//        ProdWorkOrder prodWorkOrder = new ProdWorkOrder();
//        prodWorkOrder.setId(9990l);
//        prodWorkOrder.setBarcode("4456456465456");
//        prodWorkOrderService.updateOrderById(prodWorkOrder);
//        return AjaxResult.success();
//    }

    @ApiOperation("updateStorageInfo")
    @GetMapping("updateStorageInfo/{lineId}")
    public AjaxResult updateStorageInfo(@PathVariable("lineId") Long lineId) throws Exception {
        rabbitMsgUtils.updateStorageInfo(lineId);

////        smbUtil.getFileAndSave2(id,fileName);
//        SysPlatformApi api = apiService.getEntityByCode("dcs_getStorageInfo");
//        WebSocketVo webSocketVo = new WebSocketVo();
//        webSocketVo.setUrl(api.getUrl());
//        webSocketVo.setMethod(api.getApiMethod());
//        webSocketVo.setLineId(5L);
//        rabbitTemplate.convertAndSend("direct_interface_exchange", "normal", JSON.toJSONString(webSocketVo));
        return AjaxResult.success();
    }

    public static void main(String[] args) throws DocumentException {
//        String start = "INSERT INTO `base_line_storage` \n" +
//                "(`deleted`, `storage_name`, `storage_code`, `mat_id`, `wo_id`, `empty`, `line_id`, `line_code`, `operation_id`, `filler_type`, `alias`, `eqp_id`) \n" +
//                "VALUES " +
//                "('0', 'NAMEID', 'NAMEID', NULL, NULL, '1', '6', '6', '', '0', NULL, EQPID);";
//        int k = 1;
//        for (int i = 26; i < 35; i++) {
//            for (int j = 0; j < 4; j++) {
//                System.out.println(start.replace("EQPID", i + "").replace("NAMEID", k + ""));
//                k++;
//            }
//        }
//        CAXARequestFormDataDto dto = new CAXARequestFormDataDto();
//        dto.setFileName("g2-w2-PM.h");
//        dto.setDestFilePath("D:/CNC-ZICL/4");
//        dto.setSimpleFileName("gz-w2-PM.h");
//        dto.setMachineName("4");
//        dto.setMachineID("4");
//        String result = HttpRequest.post("http://10.72.1.110:6767/CAXAService1.asmx/RecvFile")
//                .contentType("application/x-www-form-urlencoded")
//                .body(JSON.toJSONString(dto))
//                .timeout(3000)
//                .execute().body();
//        System.out.println(result);
        //请求地址
/*        String soapUrl = "http://10.72.1.110:6767/CAXAService1.asmx?wsdl";
        HashMap<String, Object> map = new HashMap<>();
        map.put("FileName", "g2-w2-PM.h");
        map.put("DestFilePath", "D:/CNC-ZICL/4");
        map.put("SimpleFileName", "g2-w2-PM.h");
        map.put("MachineName", "4");
        map.put("MachineID", "4");
        SoapClient soapClient = SoapClient.create(soapUrl)
                //请求方法,命名空间
                .setMethod("RecvFile", "http://www.caxa.com/caxaservice/")
                .setParams(map);
        String sendSoap = soapClient.send();
        System.out.println(sendSoap);
        ThreadLocal threadLocal = new ThreadLocal();
        threadLocal.set(123);
        threadLocal.get();
        threadLocal.remove();*/
//        System.out.println(DocumentHelper.parseText(sendSoap));
//        Map<String, String> map1 = new HashMap<String, String>();
//        Map soapMap = XmlMap(sendSoap, map1);
//        System.out.println(JSONObject.toJSONString(soapMap));

        //转成xml对象
//        Document document = XmlUtil.parseXml(sendSoap);
//通过命名空间获取节点数据
//        NodeList nodeList = document.getElementsByTagNameNS(namespaceURL, "*");
//取出第一个节点数据
//        cn.hutool.json.JSONObject jsonObj = new JSONObject(nodeList.item(0).getTextContent().trim());
//        System.out.println(jsonObj);
//        Result result = new Result();
//        result.setCode(jsonObj.get("响应状态").toString());
//        result.setMsg(jsonObj.get("说明").toString());



    }

    public static Map<String, String> XmlMap(String xml, Map<String, String> map) {
        try {
            SAXReader reader = new SAXReader();
            org.dom4j.Document doc = reader.read(new StringReader(xml));
            Element root = doc.getRootElement();
            String path = "";
            if (map.containsKey(root.getName().trim())) {
                path = map.get(root.getName().trim());
                map.remove(root.getName().trim());
            }
            for (Iterator i = root.elementIterator(); i.hasNext(); ) {
                Element element = (Element) i.next();
                if (element.isTextOnly()) {
                    if (path.length() > 0) {
                        map.put(path + element.getName().trim(), element.getTextTrim());
                    } else {
                        map.put(element.getName().trim(), element.getTextTrim());
                    }
                } else {
                    map.put(element.getName().trim(), path + element.getName().trim() + ".");
                    XmlMap(element.asXML(), map);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }




}

