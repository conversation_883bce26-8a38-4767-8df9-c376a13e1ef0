package com.ruoyi.production.api.factory;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.production.api.RemoteEquipmentService;
import com.ruoyi.production.api.domain.BaseEquipment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteEquipmentFallbackFactory implements FallbackFactory<RemoteEquipmentService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteEquipmentFallbackFactory.class);

    @Override
    public RemoteEquipmentService create(Throwable throwable) {
        log.error("@@@@@@@@@@  用户服务调用失败:{}", throwable.getMessage());
        return new RemoteEquipmentService() {
            @Override
            public R<BaseEquipment> getEquipmentByMqttAlias(String mqttAlias, String source) {
                return R.fail("获取 BaseEquipment 失败:" + throwable.getMessage());
            }
        };
    }
}
