package com.ruoyi.production.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.BaseLine;
import com.ruoyi.production.service.IBaseLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("line")
@Api(value = "产线-控制器", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class BaseLineController extends BaseController {

    @Autowired
    IBaseLineService baseLineService;

    @ApiOperation("查询产线列表")
    @GetMapping("list")
    public AjaxResult getList() {
        return AjaxResult.success(baseLineService.list());
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取产线信息")
    @Cacheable(value = "production:line" ,key = "#p0")
    public AjaxResult get(@ApiParam(value = "产线id") @PathVariable Long id) {
        return AjaxResult.success(baseLineService.getById(id));
    }

    @PostMapping
    @ApiOperation("创建产线")
    public AjaxResult add(@RequestBody BaseLine baseLine) {
        return AjaxResult.success(baseLineService.save(baseLine));
    }

    @PutMapping("/{id}")
    @ApiOperation("修改产线")
    @CachePut(value = "production:line" ,key = "#p0")
    public AjaxResult updateAddress(@ApiParam(value = "产线id") @PathVariable Long id, @RequestBody BaseLine baseLine) {
        return AjaxResult.success(baseLineService.save(baseLine));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除产线")
    @CacheEvict(value="production:line",allEntries=true)
    public AjaxResult deleteAddress(@ApiParam(value = "产线id") @PathVariable Long id) {
        return AjaxResult.success(baseLineService.removeById(id));
    }

    @PostMapping("/batchSave")
    @ApiOperation("批量保存产线")
    public AjaxResult batchSave(@RequestBody List<BaseLine> baseLines) {
        return AjaxResult.success(baseLineService.saveBatch(baseLines));
    }

    @DeleteMapping("/batchDelete/{ids}")
    @ApiOperation("根据id批量删除产线信息")
    public AjaxResult batchDelete(@ApiParam(value = "产线id") @PathVariable String ids) {
        return AjaxResult.success(baseLineService.removeByIds(Arrays.asList(ids)));
    }

}
