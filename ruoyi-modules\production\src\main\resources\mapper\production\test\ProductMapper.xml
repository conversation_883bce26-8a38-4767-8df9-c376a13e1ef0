<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.test.ProductMapper">
    <resultMap type="com.ruoyi.production.domain.test.Product" id="ProductResult">
        <id property="id" column="id"/>
        <result property="price" column="price"/>
        <result property="stock" column="stock"/>
        <result property="lastUpdateTime" column="last_update_time"/>
    </resultMap>

    <select id="selectById" parameterType="com.ruoyi.production.domain.test.Product" resultMap="ProductResult">
        select id, price, stock, last_update_time
        from product
        where id = #{productId}
    </select>

    <update id="updateById" parameterType="com.ruoyi.production.domain.test.Product">
        update product
        set price            = #{price},
            stock            = #{stock},
            last_update_time = sysdate()
        where id = #{id}
    </update>
</mapper>