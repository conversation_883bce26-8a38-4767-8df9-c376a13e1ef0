package com.ruoyi.production.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Data
@ApiModel(value = "ProdWorkOrder对象", description = "")
public class ProdWorkOrder extends MyBaseEntity {

    private Long lineId;

    private Long eqpId;

    @ApiModelProperty(value = "产线号")
    private String lineCode;

    @ApiModelProperty(value = "机床号")
    private String eqpCode;

    @ApiModelProperty(value = "任务号")
    private String billCode;

    @ApiModelProperty(value = "工序号")
    private String schedulCode;

    @ApiModelProperty(value = "任务号+工序号")
    private String billSchedulCode;

    @ApiModelProperty(value = "ppWorkOrderCode")
    private String workOrderCode;

    @ApiModelProperty(value = "工序名")
    private String schedulName;

    @ApiModelProperty(value = "物料号")
    private String matCode;

    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "物料id")
    private Integer matId;

    @ApiModelProperty(value = "工单状态")
    private Integer status;

    @ApiModelProperty(value = "工单号")
    private String ticketNumber;

    @ApiModelProperty(value = "实际加工开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realStartTime;

    @ApiModelProperty(value = "实际加工结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndTime;

    @TableField("BZ1")
    @ApiModelProperty(value = "托盘大小类型：big、small")
    private String bz1;

    /*  以下全部是富堪接口字段  */
    private String operationSplit;

    @ApiModelProperty(value = "排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date schedulStartDate;

    @ApiModelProperty(value = "排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date schedulEndDate;

    @ApiModelProperty(value = "物料名称")
    private String materialDesc;

    @ApiModelProperty(value = "富堪工单唯一ID")
    private String operationId;

    @ApiModelProperty(value = "在线检测机器结果")
    private Integer detectionStatus;

    @ApiModelProperty(value = "自检结果")
    private Integer selfDetectionStatus;

    @ApiModelProperty(value = "专检结果")
    private Integer specialDetectionStatus;

    @ApiModelProperty(value = "带在线检测结果的二维码")
    private String barcode;

    @ApiModelProperty(value = "真实加工机床")
    private String realEqp;

    @ApiModelProperty(value = "已下发任务到梁慧控制系统")
    private Integer distributed;
}
