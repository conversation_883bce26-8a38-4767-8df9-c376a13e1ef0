package com.ruoyi.production.enums;

public enum LHOPStatusEnum {

    WAITING("waiting", "等待执行"),
    ACTING("acting", "动作中"),
    DONE("done", "完成"),
    ERROR("error", "错误");

    private final String code;
    private final String info;

    LHOPStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(String code) {
        for (LHOPStatusEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
