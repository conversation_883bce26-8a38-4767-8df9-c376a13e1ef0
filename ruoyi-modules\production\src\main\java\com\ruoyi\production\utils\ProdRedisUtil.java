package com.ruoyi.production.utils;

import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.service.IProdOperationTraceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class ProdRedisUtil {

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IProdOperationTraceService operationTraceService;

    public synchronized boolean setAssociation(String key, Object value) {

        if (null == redisTemplate.opsForValue().get(key) || redisTemplate.opsForValue().get(key).equals(""))
            redisTemplate.opsForValue().set(key, value, 1, TimeUnit.MINUTES);
//        Map<String, Long> map = redisService.getCacheMap(KEY);
//        if (null == map) {
//            map = new HashMap();
//        }
//        if (map.containsKey(key)) {
//            if (null != map.get(key)) {
//                return false;
//            }
//        }
//        map.put(key, value);
//        redisService.setCacheMap(KEY, map);
        return true;
    }

    public synchronized void removeAssociation(String prefix,Long id) {
        ProdOperationTrace operationTrace = operationTraceService.getById(id);
        String key = prefix + ":" + operationTrace.getLineId() + ":" + operationTrace.getCmd();
        redisTemplate.opsForValue().set(key, null);
//        Map<String, Long> map = redisService.getCacheMap(KEY);
//        Iterator<String> iterator = map.keySet().iterator();
//        while (iterator.hasNext()) {
//            String k = iterator.next();
//            Long value = map.get(k);
//            if (id.equals(value)) {
//                map.put(k, null);
//                redisService.setCacheMap(KEY, map);
//            }
//        }
    }

    public Object getAssociation(String prefix,Long lineId, String key) {
        key = prefix + ":" + lineId + ":" + key;
//        redisTemplate.opsForValue().get(key);
//        Map<String, Long> map = redisService.getCacheMap(KEY);
//        Iterator<String> iterator = map.keySet().iterator();
//        while (iterator.hasNext()) {
//            String k = iterator.next();
//            //Long value = map.get(k);
//            if (key.equals(k))
//                return map.get(k);
//        }
        return redisTemplate.opsForValue().get(key);
    }

    public String contractKey(String prefix, Long lineId, String key) {
        return prefix + ":" + lineId + ":" + key;
    }
}
