package com.ruoyi.production.controller;

import io.swagger.annotations.Api;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "工作组控制器", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("basePreset")
public class BasePresetController extends BaseModuleController {

}
