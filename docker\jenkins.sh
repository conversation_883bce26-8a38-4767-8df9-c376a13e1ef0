#!/bin/bash
echo "清理已有容器及镜像资源"
	for container in {"gateway","auth","modules-system"}
do
	image=docker_${container}
	if docker ps | grep ${container} ;then
	    docker stop ${container}
	fi
	if docker ps -a | grep ${container};then
	    docker rm ${container}
	fi
	if docker images | grep ${image};then
	    docker rmi ${image}
	fi
done
# 拷贝构建的jar包到宿主机
/home/<USER>/copy.sh
# 重新启动docker项目
/home/<USER>/deploy.sh modules
