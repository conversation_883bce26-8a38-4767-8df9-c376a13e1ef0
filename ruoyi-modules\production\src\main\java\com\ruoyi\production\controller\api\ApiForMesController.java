package com.ruoyi.production.controller.api;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.CheckedException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.ResubmitLock;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.controller.BaseModuleController;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.api.StartOrderEntity;
import com.ruoyi.production.domain.dto.MesWorkOrderDto;
import com.ruoyi.production.domain.vo.BaseLineStorageVo;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IBaseLineStorageService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.production.utils.CallMesUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@Api(value = "开放给mes的接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("api/mes/")
public class ApiForMesController extends BaseModuleController {

    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private IProdWorkOrderService prodWorkOrderService;
    @Autowired
    private ISysPlatformApiService platformApiService;
    @Autowired
    private IBaseLineStorageService storageService;
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private CallMesUtil callMesUtil;
    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;

    @PostMapping(value = "addWorkOrders", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public R<?> addWorkOrders(@RequestBody List<MesWorkOrderDto> mesWorkOrderDtoList) {
        log.info("********** 同步工单 >>> " + JSON.toJSONString(mesWorkOrderDtoList));
        List<ProdWorkOrder> insertList = new ArrayList<>();
        for (MesWorkOrderDto dto : mesWorkOrderDtoList) {

            ProdWorkOrder workOrder = prodWorkOrderService.getEntityByOperationId(dto.getId());
            //已存在工单，跳过
            if (workOrder != null)
                continue;
            workOrder = new ProdWorkOrder();
            workOrder.setBillCode(dto.getPpOrderCode());
            workOrder.setOperationId(dto.getId());
            //修改逻辑 2023年5月11日 16:39:53
            String code1 = dto.getOrderCode().split(":")[0];
            String code2 = dto.getOrderCode().split(":")[1];
            String ppOrderCode = dto.getPpOrderCode();

            workOrder.setTicketNumber(ppOrderCode + ":" + code2);
            workOrder.setWorkOrderCode(dto.getPpWorkOrderCode());
//            if (dto.getOrderCode().contains(":")) {
//                workOrder.setTicketNumber(dto.getOrderCode());
//            } else {
//                workOrder.setTicketNumber(dto.getOrderCode() + ":" + dto.getDeviceCode());
//            }
            // 2023年5月11日 16:45:11 结束
            workOrder.setSchedulCode(dto.getFlowId());
            workOrder.setSchedulName(dto.getRouteName());
            workOrder.setMatCode(dto.getMaterialCode());
            workOrder.setMatName(dto.getMaterialName());
            //给数字孪生添加用
            workOrder.setMaterialDesc(dto.getMaterialName());
            workOrder.setEqpCode(dto.getDeviceCode());
            workOrder.setLineCode(dto.getCenterCode());
            workOrder.setLineId(Long.parseLong(dto.getCenterCode()));
            workOrder.setSchedulStartDate(dto.getPlanStartTime());
            workOrder.setSchedulEndDate(dto.getPlanFinishTime());
            //添加顺序号
            String[] strings = workOrder.getTicketNumber().split("-");
            workOrder.setOperationSplit(strings.length > 0 ? strings[strings.length - 1] : null);

            workOrder.setBillSchedulCode(workOrder.getBillCode() + "_" + workOrder.getSchedulCode());
            workOrder.setStatus(0);
            insertList.add(workOrder);
        }
        boolean flag = prodWorkOrderService.saveBatch(insertList);
        if (flag && insertList.size() > 0) {
            ProdWorkOrder order = insertList.get(0);
            MessageNews messageNews = new MessageNews();
            messageNews.setContent("mes <新增> 工单提示：任务号-" + order.getBillCode() + "，工序号-" + order.getSchedulCode() + "，<新增>数量：" + insertList.size());
            messageNews.setCreaterId(1L);
            messageNews.setLineId(order.getLineId());
            messageNews.setButtonGroupId(2L);
            remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
        }
        return R.ok(flag);
    }

    @PostMapping(value = "startOrderOld", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @Log(title = "mes开工调用", businessType = BusinessType.OTHER)
    @ResubmitLock
    public R<?> startOrder(@Validated @RequestBody StartOrderEntity entity) {
//        log.info("********** mes开工调用 >>> 查找工单-ID：" + entity.getOperationId());
        //调用管控端接口开工，根据返回结果，调用mes报工接口设置开工，插入跟踪记录
        //根据operationId查找对应工单
        ProdWorkOrder workOrder = prodWorkOrderService.getEntityByOperationId(entity.getOperationId());
        if (null == workOrder) {
            throw new CheckedException("未查询到工单 " + entity.getOperationId());
        }
        log.info("********** mes开工调用 >>> 找到工单-工单号：" + workOrder.getTicketNumber() + ",工单状态 >>> " + workOrder.getStatus());
        //查询五分钟内是否二次 成功开工
//        String startOrderKey = ProductionConstans.PROD_START_ORDER + entity.getOperationId();
//        String o = redisTemplate.opsForValue().get(startOrderKey);
//        if (o != null) {
//            log.error("@@@@@@@@@@  此工单已在10分钟内二次开工 》》》 " + entity.getOperationId());
//            return R.fail("此工单已在10分钟内二次开工 》》》 " + entity.getOperationId());
//        }
        //校验是否已完成加工
/*        if (WorkOrderStatusEnum.DONE.getCode().equals(workOrder.getStatus())
                || WorkOrderStatusEnum.INSTOCK.getCode().equals(workOrder.getStatus())) {
            log.info("********** mes开工调用 >>> 工单状态无法开工：" + WorkOrderStatusEnum.getInfoByKey(workOrder.getStatus()));
            return R.fail(">>>管控系统中工单状态已完成，无法开工");
        }*/

        String apiCode = "iot_pushproc_" + workOrder.getLineId();
        //获取产线号
        String lineCode = workOrder.getLineCode();
        //apiParam.setApiCode(apiCode.replace("line", "line" + lineCode));
        //调用管控端参数查询
        //管控端地址查询
        String iotUrl = platformApiService.getEntityByCode(apiCode).getUrl();
        //参数
        BaseLineStorage storageParam = new BaseLineStorage();
        storageParam.setOperationId(workOrder.getOperationId());
        List<BaseLineStorageVo> storageList = storageService.listByCondition(storageParam);
        if (storageList.size() < 1) {
            log.error("@@@@@@@@@@  mes开工调用 >>> 系统中未在线边库查询到工单，无法开工：" + workOrder.getTicketNumber());
            return R.fail(">>>管控系统中未在线边库查询到工单，无法开工 " + entity.getOperationId());
        }

        JSONObject iotParam = new JSONObject();

        iotParam.put("procid", workOrder.getOperationId());
        iotParam.put("subtime", new Date());
        iotParam.put("starttime", null);
        iotParam.put("endtime", null);
        iotParam.put("objname", workOrder.getMatName());
        iotParam.put("part-type", "mat");
        iotParam.put("curpos", storageList.get(0).getAlias());
        iotParam.put("target", Integer.parseInt(workOrder.getEqpCode()) + "");
        iotParam.put("status", "waiting");

        String result = "";
        if (workOrder.getStatus().equals(WorkOrderStatusEnum.WAIT.getCode())) {
            synchronized (this) {
                workOrder = prodWorkOrderService.getEntityByOperationId(entity.getOperationId());
                if (workOrder.getStatus().equals(WorkOrderStatusEnum.WAIT.getCode())) {
                    result = HttpRequest.post(iotUrl).body(iotParam.toJSONString()).timeout(3000)
                            .execute().body();
                }
            }
        }

        JSONObject response = JSON.parseObject(result);
        if (StringUtils.isBlank(result)) {
            log.error("@@@@@@@@@@  mes开工调用 >>> 失败 : 工单状态无法开工：" + WorkOrderStatusEnum.getInfoByKey(workOrder.getStatus()));
            return R.ok(entity.getOperationId() + "工单状态非待加工");
        }
        if (response.getInteger("code") == 0) {
            //下单成功，更新机床为加工中
            BaseEquipment update = new BaseEquipment();
            update.setId(entity.getEqpId());
            update.setProcessing(1);
            equipmentService.updateById(update);
            log.info("********** 设置机床 加工中 id：" + update.getId());

            log.info("********** mes开工调用 >>> 成功 : " + result);

            return R.ok(result);
        } else {
            log.error("@@@@@@@@@@  mes开工调用 >>> 失败 :" + result);
            return R.fail(result);
        }
    }

    //    @PostMapping(value = "startOrderNew", produces = {"application/json;charset=UTF-8"})
//    @ResponseBody
//    @Log(title = "mes开工调用", businessType = BusinessType.OTHER)
//    @ResubmitLock
    @Log(title = "自动开工调用", businessType = BusinessType.OTHER)
    public R<?> startOrderNew(@Validated @RequestBody StartOrderEntity entity) {
        //根据operationId查找对应工单
        ProdWorkOrder workOrder = prodWorkOrderService.getEntityByOperationId(entity.getOperationId());
        if (null == workOrder) {
            throw new CheckedException("未查询到工单 " + entity.getOperationId());
        }
        log.info("********** 自动开工调用 >>> 找到工单-工单号：" + workOrder.getTicketNumber() + ",工单状态 >>> " + workOrder.getStatus());

        String apiCode = "iot_pushproc_" + workOrder.getLineId();
        //获取产线号
        String lineCode = workOrder.getLineCode();
        //apiParam.setApiCode(apiCode.replace("line", "line" + lineCode));
        //调用管控端参数查询
        //管控端地址查询
        String iotUrl = platformApiService.getEntityByCode(apiCode).getUrl();
        //参数
        BaseLineStorage storageParam = new BaseLineStorage();
        storageParam.setOperationId(workOrder.getOperationId());
        List<BaseLineStorageVo> storageList = storageService.listByCondition(storageParam);
        if (storageList.size() < 1) {
            log.error("@@@@@@@@@@  自动开工调用 >>> 系统中未在线边库查询到工单，无法开工：" + workOrder.getTicketNumber());
            return R.fail(">>>管控系统中未在线边库查询到工单，无法开工 " + entity.getOperationId());
        }

        JSONObject iotParam = new JSONObject();

        iotParam.put("procid", workOrder.getOperationId());
        iotParam.put("subtime", new Date());
        iotParam.put("starttime", null);
        iotParam.put("endtime", null);
        iotParam.put("objname", workOrder.getMatName());
        iotParam.put("part-type", "mat");
        iotParam.put("curpos", storageList.get(0).getAlias());
        iotParam.put("target", entity.getRealEqp());
        iotParam.put("status", "waiting");

        String result = "";
        if (workOrder.getStatus().equals(WorkOrderStatusEnum.WAIT.getCode())) {
            synchronized (this) {
                workOrder = prodWorkOrderService.getEntityByOperationId(entity.getOperationId());
                if (workOrder.getStatus().equals(WorkOrderStatusEnum.WAIT.getCode())) {
                    try {
                        result = HttpRequest.post(iotUrl).body(iotParam.toJSONString()).timeout(3000)
                                .execute().body();
                    } catch (Exception e) {
                        //下单失败通知
                        MessageNews messageNews = new MessageNews();
                        messageNews.setContent("向控制系统下发工单失败！ >>>" +
                                "下发机床号：" + entity.getRealEqp() + "，失败原因：" + e.getMessage());
                        messageNews.setCreaterId(1L);
                        messageNews.setLineId(workOrder.getLineId());
                        messageNews.setButtonGroupId(2L);
                        remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
                        log.error("@@@@@@@@@@  自动开工调用 >>> 失败 :" + e.getMessage());
                    }
                }
            }
        }

        JSONObject response = JSON.parseObject(result);
        if (StringUtils.isBlank(result)) {
            log.error("@@@@@@@@@@  自动开工调用 >>> 失败 : 工单状态无法开工：" + WorkOrderStatusEnum.getInfoByKey(workOrder.getStatus()));
            return R.ok(entity.getOperationId() + "工单状态非待加工");
        }
        if (response.getInteger("code") == 0) {
            //下单成功，更新机床为加工中
            BaseEquipment equipment = equipmentService.getById(entity.getEqpId());
            BaseEquipment update = new BaseEquipment();
            update.setId(equipment.getId());
            update.setProcessing(1);
            update.setOperationId(entity.getOperationId());
            equipmentService.updateById(update);
            log.info("********** 设置机床加工中：" + equipment.getEqpCode());
            //设置实际开工机床 2023年3月20日 10:41:06 添加
            ProdWorkOrder updateOrder = new ProdWorkOrder();
            updateOrder.setId(workOrder.getId());
            updateOrder.setRealEqp(entity.getRealEqp());
            updateOrder.setDistributed(1);
            prodWorkOrderService.updateById(updateOrder);
            log.info("********** 自动开工调用 >>> 成功 : " + result);
            //下单成功，通知mes工单已开工
            workOrder.setRealEqp(entity.getRealEqp());
            callMesUtil.startOrder(workOrder);
            //下单成功通知
            MessageNews messageNews = new MessageNews();
            messageNews.setContent("向控制系统下发工单成功 >>>" +
                    "下发机床号：" + updateOrder.getRealEqp() + "，下发工单号：" + workOrder.getTicketNumber() +
                    "，库位：" + storageList.get(0).getAlias());
            messageNews.setCreaterId(1L);
            messageNews.setLineId(workOrder.getLineId());
            messageNews.setButtonGroupId(2L);
            remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
            return R.ok(result);
        } else {
            //下单失败通知
            MessageNews messageNews = new MessageNews();
            messageNews.setContent("向控制系统下发工单失败！ >>>" +
                    "下发机床号：" + entity.getRealEqp() + "，控制系统返回失败原因：" + result);
            messageNews.setCreaterId(1L);
            messageNews.setLineId(workOrder.getLineId());
            messageNews.setButtonGroupId(2L);
            remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
            log.error("@@@@@@@@@@  自动开工调用 >>> 失败 :" + result);
            return R.fail(result);
        }
    }

    @PostMapping(value = "updateWorkOrders", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public R<?> updateWorkOrders(@RequestBody List<MesWorkOrderDto> mesWorkOrderDtoList) {
        log.info("********** 更新工单 >>> " + JSON.toJSONString(mesWorkOrderDtoList));
        List<ProdWorkOrder> updateList = new ArrayList<>();
        for (MesWorkOrderDto dto : mesWorkOrderDtoList) {
            ProdWorkOrder workOrder = prodWorkOrderService.getEntityByOperationId(dto.getId());

            //不存在工单，工单状态已开工，跳过
            if (workOrder == null) {
                log.info(dto.getId() + " 工单不存在");
                continue;
            }
            if (workOrder.getStatus() > 2) {
                log.info(dto.getId() + " 工单状态不允许更新，状态为：" + workOrder.getStatus());
                continue;
            }
            ProdWorkOrder updateOrder = new ProdWorkOrder();
            updateOrder.setId(workOrder.getId());
//            workOrder.setBillCode(dto.getPpOrderCode());
//            workOrder.setOperationId(dto.getId());
//            if (dto.getOrderCode().contains(":")) {
//                workOrder.setTicketNumber(dto.getOrderCode());
//            } else {
//                workOrder.setTicketNumber(dto.getOrderCode() + ":" + dto.getDeviceCode());
//            }
//            workOrder.setSchedulCode(dto.getFlowId());
            updateOrder.setMatCode(dto.getMaterialCode());
            updateOrder.setMatName(dto.getMaterialName());
            updateOrder.setSchedulName(dto.getRouteName());
            updateOrder.setEqpCode(dto.getDeviceCode());
            updateOrder.setLineCode(dto.getCenterCode());
            updateOrder.setLineId(Long.parseLong(dto.getCenterCode()));
            updateOrder.setSchedulStartDate(dto.getPlanStartTime());
            updateOrder.setSchedulEndDate(dto.getPlanFinishTime());
            updateOrder.setStatus(0);

//            workOrder.setBillSchedulCode(workOrder.getBillCode() + "_" + workOrder.getSchedulCode());
            updateList.add(updateOrder);
        }
        boolean flag = prodWorkOrderService.updateBatchById(updateList);
        if (flag && updateList.size() > 0) {
            ProdWorkOrder order = updateList.get(0);
            MessageNews messageNews = new MessageNews();
            messageNews.setContent("mes <更新> 工单提示：<更新>数量：" + updateList.size());
            messageNews.setCreaterId(1L);
            messageNews.setLineId(order.getLineId());
            messageNews.setButtonGroupId(2L);
            remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
        }
        return R.ok(flag);
    }

    @PostMapping(value = "deleteWorkOrders", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public R<?> deleteWorkOrders(@RequestBody List<MesWorkOrderDto> mesWorkOrderDtoList) {
        log.info("********** 删除工单 >>> " + JSON.toJSONString(mesWorkOrderDtoList));
        List<Long> deleteIds = new ArrayList<>();
        for (MesWorkOrderDto dto : mesWorkOrderDtoList) {

            ProdWorkOrder workOrder = prodWorkOrderService.getEntityByOperationId(dto.getId());

            if (workOrder.getStatus() != 0) {
                log.info(dto.getId() + " 工单状态不允许删除 状态为：" + workOrder.getStatus());
                continue;
            }

            deleteIds.add(workOrder.getId());
//            prodWorkOrderService.removeByIds(deleteIds);
        }
        return R.ok();
    }

    @PutMapping(value = "forceDoneWorkOrder/{opreationId}", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public R<?> forceDoneWorkOrder(@PathVariable("opreationId") String opreationId) {
        log.info("********** 强制完工 >>> " + opreationId);
        ProdWorkOrder order = prodWorkOrderService.getEntityByOperationId(opreationId);
        if (order == null) {
            return R.fail("工单未找到");
        }
        order.setStatus(WorkOrderStatusEnum.DONE.getCode());
        prodWorkOrderService.updateWorkOrderWithStatus(order);
        return R.ok();
    }

    public static void main(String[] args) {
        String[] strings = "".split("-");
        System.out.println(strings.length > 0 ? strings[strings.length - 1] : null);
    }

}
