package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.production.domain.BaseProcess;
import com.ruoyi.production.domain.vo.BaseProcessVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface BaseProcessMapper extends BaseMapper<BaseProcess> {
    List<BaseProcess> getProcessLinksByParentId(@Param("parentId") Long id);
    List<BaseProcessVo> listByCondition(@Param("params") BaseProcess baseProcess);
}
