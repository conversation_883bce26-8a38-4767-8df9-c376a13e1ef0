package com.ruoyi.production.domain.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class StartOrderEntity {

    @NotBlank(message = "operationId不可为空")
    @ApiModelProperty(value = "operationId")
    private String operationId;
    @ApiModelProperty(value = "开工设备id")
    private Long eqpId;
    @ApiModelProperty(value = "开工设备code")
    private String realEqp;
}
