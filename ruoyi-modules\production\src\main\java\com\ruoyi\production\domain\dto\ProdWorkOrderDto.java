package com.ruoyi.production.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.production.domain.ProdWorkOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "工单参数", description = "")
public class ProdWorkOrderDto extends ProdWorkOrder {

    @ApiModelProperty(value = "工单ID")
    private String operationId;
    @ApiModelProperty(value = "托盘位置")
    private String trayPosition;
    @ApiModelProperty(value = "托盘物品类型")
    private String procType;
    @ApiModelProperty(value = "今日和往日区分标识(1:往日)")
    private Integer dataScrop;
    @ApiModelProperty(value = "时间范围")
    private String dateType;
    @ApiModelProperty(value = "状态范围")
    private List<String> statusList;
    @ApiModelProperty(value = "工单状态不等于此状态")
    private List<Integer> notInStatusList;

    @ApiModelProperty(value = "针对实际加工时间进行范围查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndDateBegin;

    @ApiModelProperty(value = "针对实际加工时间进行范围查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndDateStop;

}
