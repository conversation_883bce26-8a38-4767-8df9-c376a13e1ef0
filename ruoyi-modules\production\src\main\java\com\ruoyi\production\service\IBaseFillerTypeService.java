package com.ruoyi.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.BaseFillerType;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-13
 */
public interface IBaseFillerTypeService extends IService<BaseFillerType> {

    List<BaseFillerType> listByCondition(BaseFillerType BaseFillerType);
    List<BaseFillerType> getListByLineId(Long LineId);
    BaseFillerType getByFillerType(Long lineId,Integer fillerType);
    BaseFillerType getByAlias(Long lineId,String alisa);



}
