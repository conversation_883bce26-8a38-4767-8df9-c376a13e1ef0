<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.BaseLineStorageMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.BaseLineStorage">
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
        <result column="storage_name" property="storageName"/>
        <result column="storage_code" property="storageCode"/>
        <result column="mat_id" property="matId"/>
        <result column="wo_id" property="woId"/>
        <result column="empty" property="empty"/>
        <result column="line_id" property="lineId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_by
        ,
        create_time,
        update_by,
        update_time,
        id,
        deleted,
        remark, storage_name, storage_code, mat_id, wo_id, empty, line_id
    </sql>

    <select id="listByCondition" resultType="com.ruoyi.production.domain.vo.BaseLineStorageVo">
        select
        storage.*,
        wo.eqp_code,
        wo.status as mat_type,
        wo.bill_code,
        wo.mat_code,
        wo.mat_name,
        wo.schedul_code,
        wo.ticket_number,
        wo.bz1,
        wo.real_eqp,
        equipment.eqp_code as mach_code,
        equipment.eqp_name as mach_name,
        b.eqpGroup
        from base_line_storage storage
        left join (select * from prod_work_order) wo on wo.operation_id = storage.operation_id
        left join
        (select operation_id, mat_type
        from prod_process_trace pt
        where pt.id in (select MAX(id) from prod_process_trace group by operation_id)) a
        on a.operation_id = storage.operation_id
        left join base_equipment equipment on equipment.id=storage.eqp_id
        left join (select GROUP_CONCAT(eqp_code) as eqpGroup,bill_schedul_code from prod_order_mach_relation where enable=1 group by
        bill_schedul_code ) b
        on b.bill_schedul_code= wo.bill_schedul_code
        <where>
            <if test="params.lineId != null and params.lineId != ''">
                and storage.line_id = #{params.lineId}
            </if>
            <if test="params.storageCode != null and params.storageCode!= ''">
                and storage.storage_code = #{params.storageCode}
            </if>
            <if test="params.operationId != null and params.operationId!= ''">
                and storage.operation_id = #{params.operationId}
            </if>
            <if test="params.empty != null">
                and storage.empty = #{params.empty}
            </if>
        </where>
        order by storage.id asc
    </select>

    <select id="getRemainMatNums" resultType="java.lang.Integer">
        select count(*)
        from base_equipment eqp
        left join prod_work_order wo on eqp.eqp_code = wo.eqp_code
        left join base_line_storage ls on wo.operation_id = ls.operation_id
        <where>
            wo.status = 3
            <if test="eqpId != null ">
                and eqp.id = #{eqpId}
            </if>
        </where>
    </select>

    <select id="getReadyOrderList" resultType="com.ruoyi.production.domain.vo.BaseLineStorageVo">
        select s.operation_id,s.storage_name,s.alias,s.empty,omr.eqp_code as machCode,pwo.status,pwo.bill_schedul_code
        from base_line_storage s
                 inner JOIN prod_work_order pwo on pwo.operation_id=s.operation_id
                 left join prod_order_mach_relation omr on omr.bill_schedul_code= pwo.bill_schedul_code
        <where>
        pwo.status = 3 and pwo.distributed = 0 and omr.enable=1
        <if test="params.lineId != null and params.lineId != ''">
            and omr.line_id = #{params.lineId}
        </if>
        </where>
    </select>

</mapper>
