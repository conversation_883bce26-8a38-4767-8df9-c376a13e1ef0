package com.ruoyi.production.enums;

public enum OperationStatusEnum {

    WAIT("waiting", "等待执行"),
    DOING("acting", "执行中"),
    DONE("done", "执行完成"),
    ERROR("error", "错误");

    private final String code;
    private final String info;

    OperationStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(String code) {
        for (OperationStatusEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
