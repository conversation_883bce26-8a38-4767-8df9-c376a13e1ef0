<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.message.mapper.MessageButtonMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.message.domian.MessageButton">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="button_text" property="buttonText"/>
        <result column="button_url" property="buttonUrl"/>
        <result column="button_type" property="buttonType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        remark,
        deleted,
        create_by, create_time, update_by, update_time, button_text, button_url, button_type
    </sql>

    <select id="getButtonsByGroupId" resultType="com.ruoyi.message.domian.vo.MessageGroupButtonVo">
        select button.button_text,
        button.button_type,
        mbg.group_id,
        mbg.button_id,
        mbg.child_group_id
        from message_button_group mbg
        left join message_button button on mbg.button_id = button.id
        <where>
            <if test="id != null">
                and mbg.group_id = #{id}
            </if>
        </where>
    </select>
</mapper>
