package com.ruoyi.common.log.aspect;

import com.ruoyi.common.core.exception.CheckedException;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.domain.SysUser;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

@Aspect
@Component
public class PreOperationAspect {
    private static final Logger log = LoggerFactory.getLogger(PreOperationAspect.class);

    @Autowired
    protected TokenService tokenService;

    /**
     * 构建
     */
    public PreOperationAspect() {
    }

    /**
     * 定义AOP签名 (切入所有使用注解的方法)
     */
    public static final String POINTCUT_SIGN = " @annotation(com.ruoyi.common.security.annotation.RequiresLogin) || "
            + "@annotation(com.ruoyi.common.security.annotation.RequiresPermissions) || "
            + "@annotation(com.ruoyi.common.security.annotation.RequiresRoles)";

    /**
     * 声明AOP签名
     */
    @Pointcut("@annotation(com.ruoyi.common.log.annotation.PreOperation)")
    public void pointcut() {
    }

    /**
     * 环绕切入
     *
     * @param joinPoint 切面对象
     * @return 底层方法执行后的返回值
     * @throws Throwable 底层方法抛出的异常
     */
    @Before("pointcut()")
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        // 注解鉴权
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        SysUser sysUser = tokenService.getLoginUser(ServletUtils.getRequest()).getSysUser();
        // 请求的地址
        String requestIp = IpUtils.getIpAddr(ServletUtils.getRequest());
        String ips = sysUser.getOpreationAllowIps();
        List ipList = ips == null ? null : Arrays.asList(ips.split(","));
        if (null == ipList || !ipList.contains(requestIp))
            throw new CheckedException("当前IP没有执行操作权限，请联系管理员。");

//        checkMethodAnnotation(signature.getMethod());
            try {
                // 执行原有逻辑
                Object obj = joinPoint.proceed();
                return obj;
            } catch (Throwable e) {
                throw e;
            }
    }

    /**
     * 对一个Method对象进行注解检查
     */
    public void checkMethodAnnotation(Method method) {

        // 校验 @RequiresPermissions 注解
        RequiresPermissions requiresPermissions = method.getAnnotation(RequiresPermissions.class);
        if (requiresPermissions != null) {
            AuthUtil.checkPermi(requiresPermissions);
        }
    }
}
