package com.ruoyi.production.enums;

public enum LHMatTypeEnum {

    MAT("mat", "待加工"),
    POD("pod", "待检验");

    private final String code;
    private final String info;

    LHMatTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(String code) {
        for (LHMatTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
