package com.ruoyi.message.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.message.service.IMessageButtonGroupService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("buttonGroup")
public class MessageButtonGroupController extends BaseModuleController {

    @Autowired
    private IMessageButtonGroupService buttonGroupService;

    @ApiOperation("根据组id查询按钮")
    @GetMapping("getButtonsByGroupId/{id}")
    @Cacheable(value = "message:buttongroup", key = "#id")
    public AjaxResult getButtonsByGroupId(@PathVariable("id") Long id) {

        return AjaxResult.success();
    }

}
