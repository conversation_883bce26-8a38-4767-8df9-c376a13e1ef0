package com.ruoyi.production.core.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.message.api.RemoteEqpCollisionDataService;
import com.ruoyi.message.api.domain.EquipmentSpindleCollisionData;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.utils.ProdDateUtil;
import com.ruoyi.production.utils.WebSocketServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 碰撞缓冲消息消费者
 */

//@Component
public class FanoutEquipmentDataListener {

    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private RedisService redisService;
    @Autowired
    private RemoteEqpCollisionDataService remoteEqpCollisionDataService;
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private RedisTemplate redisTemplate;

    Logger log = LoggerFactory.getLogger(FanoutEquipmentDataListener.class);

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${spring.cloud.client.ip-address}:${server.port}-eqpdata", durable = "false", autoDelete = "true"),
            exchange = @Exchange(value = "websocket_eqpdata_fanout_exchange", type = ExchangeTypes.FANOUT)))
    public void handleMessage(String data) {
//        log.info("********** 收到消息 >>> 开始消费");
        WebSocketVo webSocketVo = JSON.toJavaObject(JSON.parseObject(data), WebSocketVo.class);
        webSocketVo.setType("interface");

        Set idSet = redisTemplate.opsForSet().members(ProductionConstans.REDIS_WS_COLLISION + webSocketVo.getLineId());
        if (null == idSet || idSet.size() < 1)
            return;

        Long start = System.currentTimeMillis();

        HashMap param = new HashMap();
        param.put("startTime", new Date());
        param.put("endTime", ProdDateUtil.getMinuteToNow(-2));

        List<EquipmentSpindleCollisionData> dataList = remoteEqpCollisionDataService.getCollisionDataListByMap(param, SecurityConstants.INNER).getData();
        Map<String, Map<String, Map<String, String>>> map = new HashMap();
        for (EquipmentSpindleCollisionData collisionData : dataList) {
            String time = sdf.format(collisionData.getSyncTime());
            String machCode = collisionData.getMach();
            Map<String, Map<String, String>> machMap = map.get(machCode);
            if (null == machMap)
                machMap = new LinkedHashMap<>();
            Map<String, String> timeMap = machMap.get(time);
            if (null == timeMap)
                timeMap = new LinkedHashMap<>();
            timeMap.put(collisionData.getCoordinate(), collisionData.getValue());
            machMap.put(time, timeMap);
            map.put(machCode, machMap);
        }
        //格式化
        JSONArray jsonArray = new JSONArray();
        Set<String> mapSet = map.keySet();
        for (String machCode : mapSet) {
            BaseEquipment baseEquipment = equipmentService.getEquipmentByMqttAlias(machCode);
            if (null == baseEquipment)
                continue;
            JSONObject machInfo = new JSONObject();
            machInfo.put("info", baseEquipment);
            JSONArray dataArray = new JSONArray();
            Map<String, Map<String, String>> machMap = map.get(machCode);
            Set<String> timeSet = machMap.keySet();
            timeSet.stream().forEach(time -> {
                Map<String, String> timeMap = machMap.get(time);
                timeMap.put("time", time);
                dataArray.add(timeMap);
            });
            machInfo.put("data", dataArray);
            jsonArray.add(machInfo);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list",jsonArray);
        webSocketVo.setResponse(jsonObject);

//        webSocketVo.setResponse(1);
//        webSocketVo.setData(JSON.parseObject(map.toString()));
        Long endTime = System.currentTimeMillis();
//        log.info("********** 逻辑计算完毕>>> 用时" + (endTime - start));

        idSet.stream().forEach(id -> {
            try {
                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), id + "");
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
//        log.info("********** websocket 发送完毕>>> 用时"+(new Date().getTime()-endTime));

    }

    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis());
    }
}
