<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.BasePresetMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.BasePreset">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="preset_code" property="presetCode"/>
        <result column="preset_name" property="presetName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        create_time,
        deleted,
        create_by, update_by, update_time, remark, preset_code, preset_name
    </sql>
</mapper>
