package com.ruoyi.production.core.listener;

import com.ruoyi.production.core.observable.OrderDoneEvent;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.utils.CallMesUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class OrderDoneCallMesListener implements ApplicationListener<OrderDoneEvent> {

    @Autowired
    private CallMesUtil callMesUtil;

    @Override
    public void onApplicationEvent(OrderDoneEvent event) {
        ProdWorkOrder order = event.getWorkOrder();
        callMesUtil.reportCompleted(order);
    }
}
