package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.production.domain.ProdProcessTrace;
import com.ruoyi.production.domain.vo.ProdProcessTraceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface ProdProcessTraceMapper extends BaseMapper<ProdProcessTrace> {
    List<ProdProcessTraceVo> listByCondition(@Param("params") ProdProcessTrace processTrace);
}
