package com.ruoyi.production.domain.dto.operation;

import com.ruoyi.production.domain.BaseLineStorage;
import io.swagger.annotations.ApiModelProperty;

public class Line2RoomDto extends BaseLineStorage {

    @ApiModelProperty(value = "托盘位置号")
    private Integer trayPosition;

    @ApiModelProperty(value = "物料名称")
    private String matName;

    public String getMatName() {
        return matName;
    }

    public void setMatName(String matName) {
        this.matName = matName;
    }

    public Integer getTrayPosition() {
        return trayPosition;
    }

    public void setTrayPosition(Integer trayPosition) {
        this.trayPosition = trayPosition;
    }
}
