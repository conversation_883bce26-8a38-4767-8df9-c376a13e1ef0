package com.ruoyi.production.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Data
@ApiModel(value = "BaseLineStorage对象", description = "")
public class BaseLineStorage extends MyBaseEntity {

    @ApiModelProperty(value = "线边库名称")
    private String storageName;

    @ApiModelProperty(value = "线边库代码")
    private String storageCode;

    @ApiModelProperty(value = "填充类型")
    private Integer fillerType;

    @ApiModelProperty(value = "富堪工单唯一id")
    private String operationId;

    @ApiModelProperty(value = "线边库别名")
    private String alias;

    private Long matId;

    private Long woId;

    private Integer empty;

    private Long lineId;

    private Long lineCode;

    @ApiModelProperty(value = "对应机床设备id")
    private Long eqpId;

}
