package com.ruoyi.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.production.domain.ProdMachineDetection;
import com.ruoyi.production.domain.dto.ProdMachineDetectionDto;
import com.ruoyi.production.domain.vo.detection.ProdMachineDetectionVo;
import com.ruoyi.production.domain.vo.detection.SpecialBarcodeVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
public interface ProdMachineDetectionMapper extends BaseMapper<ProdMachineDetection> {

    ProdMachineDetectionVo selectById(@Param("id") Long id);

    IPage<ProdMachineDetectionVo> listForPage(IPage<ProdMachineDetectionVo> page, @Param("params") ProdMachineDetectionDto params);

    @Select("select detection.id as detectionId,o.barcode " +
            "from prod_work_order o " +
            "left join prod_machine_detection detection on o.operation_id=detection.operation_id " +
            "where o.barcode like concat('%', #{barcode}, '%')")
    List<SpecialBarcodeVo> getSpecialBarcodeVos(@Param("barcode") String barcode);

    @Select("select detection.id from prod_work_order o " +
            "left join prod_machine_detection detection on o.operation_id=detection.operation_id " +
            "where o.barcode = #{barcode}")
    Long getDetectionIdByBarcode(@Param("barcode") String barcode);

}
