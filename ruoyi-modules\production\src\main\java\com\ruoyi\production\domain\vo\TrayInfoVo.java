package com.ruoyi.production.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "托盘信息参数", description = "")
public class TrayInfoVo {

    //接口测试
/*        TrayInfoVo trayInfoVo = new TrayInfoVo();
        trayInfoVo.setFillerType(StorageFillerTypeEnum.PART.getCode());
        trayInfoVo.setBillCode("2022-05");
        trayInfoVo.setSchedulCode("66");
        trayInfoVo.setMatName("阀体");
        trayInfoVo.setTicketNumber("100063176-01_20_01");
        trayInfoVo.setWorkOrderStatus(WorkOrderStatusEnum.WAIT.getCode());
        list.add(trayInfoVo);

        TrayInfoVo v1 = new TrayInfoVo();
        v1.setFillerType(2);
        list.add(v1);*/

    @ApiModelProperty(value = "设备id")
    private Long eqpId;

    @ApiModelProperty(value = "设备code")
    private String eqpCode;

    @ApiModelProperty(value = "托架id")
    private String rackId;

    @ApiModelProperty(value = "托盘信号点")
    private String tag;

    @ApiModelProperty(value = "工单号")
    private String ticketNumber;

    @ApiModelProperty(value = "托盘号")
    private Integer trayPosition;

    @ApiModelProperty(value = "任务号")
    private String billCode;

    @ApiModelProperty(value = "工序号")
    private String schedulCode;

    @ApiModelProperty(value = "物料号")
    private String matCode;

    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "物料id")
    private Integer matId;

    @ApiModelProperty(value = "富堪工单唯一ID")
    private String operationId;

    @ApiModelProperty(value = "工单状态")
    private Integer workOrderStatus;

    @ApiModelProperty(value = "工单状态")
    private String workOrderStatusName;

    @ApiModelProperty(value = "内容名称")
    private String fillerTypeName;

    @ApiModelProperty(value = "填充类型")
    private Integer fillerType;

    @ApiModelProperty(value = "是否修改")
    private Boolean updated;

    @ApiModelProperty(value = "选中的boxId")
    private Long boxId;

    @ApiModelProperty(value = "PLC信号点")
    private Boolean signal;

    @ApiModelProperty(value = "设备组")
    private String eqpGroup;

    @ApiModelProperty(value = "真实加工机床")
    private String realEqp;

//    private ActingOpreationInfoVo actingVo;

}
