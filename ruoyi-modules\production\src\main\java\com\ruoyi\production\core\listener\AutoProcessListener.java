package com.ruoyi.production.core.listener;

import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.controller.api.ApiForMesController;
import com.ruoyi.production.core.annotation.CheckIOTAutoProcess;
import com.ruoyi.production.core.observable.AutoProcessEvent;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.api.StartOrderEntity;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.BaseLineStorageVo;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IBaseLineStorageService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.utils.SyncEqpStatusUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AutoProcessListener implements ApplicationListener<AutoProcessEvent> {
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private ApiForMesController mesController;
    @Autowired
    private IBaseLineStorageService storageService;
    @Autowired
    private SyncEqpStatusUtil syncEqpStatusUtil;
    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private RedisService redisService;

    @Override
    @CheckIOTAutoProcess
    public synchronized void onApplicationEvent(AutoProcessEvent event) {
        try {
            //立刻同步产线设备状态，准备工单信息
            syncEqpStatusUtil.syncEqpStatus(event.getLineId());
            Thread.sleep(500);

            //获取产线所有机床  //循环遍历是否有可以加工的机床
            BaseEquipment params = new BaseEquipment();
            params.setLineId(event.getLineId());
            params.setType(EqpTypeEnum.MACHINE.getCode());
            params.setAutoProcess(1);//开启自动加工
            params.setProcessing(0);//非加工中
            params.setStatus(1);//在线
            List<BaseEquipment> equipmentList = equipmentService.listByCondition(params);
            if (equipmentList.size() == 0) {
                log.warn(">>> 没有满足加工条件的机床");
                return;
            }

            BaseLineStorage vo = new BaseLineStorage();
            vo.setLineId(event.getLineId());
            List<BaseLineStorageVo> readyOrderList = new ArrayList<>();
            List<BaseLineStorageVo> tempReadyOrderList = storageService.getReadyOrderList(vo);
            //2024-6-27 添加 过滤与plc信号不一致的线边库
            //start
            String key = ProductionConstans.PROD_EQP_STAT + event.getLineId() + ":" + 99;
            if (redisService.hasKey(key)) {
                Map<String, Object> signalMap = redisService.getCacheMap(key);
                for (BaseLineStorageVo baseLineStorageVo : tempReadyOrderList) {
                    if (signalMap.containsKey(baseLineStorageVo.getAlias())) {
                        Boolean boxHas = baseLineStorageVo.getEmpty() == 1 ? false : true;
                        Boolean plcHas = (Boolean) signalMap.get(baseLineStorageVo.getAlias());
                        if (boxHas != plcHas) {
                            log.warn(">>> PLC有无料信号与库位有无料信号不一致，排除此库位！ 库位信息：" + baseLineStorageVo.getStorageName() + " ** PLC信号：" + signalMap.get(baseLineStorageVo.getAlias()));
                        } else {
                            //信号一致，加入队列
                            readyOrderList.add(baseLineStorageVo);
                        }
                    } else {
                        //调试阶段 没有PLC信号的不判断，默认加入队列
                        readyOrderList.add(baseLineStorageVo);
                    }
                }
            }else{
                readyOrderList = tempReadyOrderList;
            }
            // end


            //过滤未设置机床组的订单
            readyOrderList = readyOrderList.stream().filter(e -> e.getMachCode() != null).collect(Collectors.toList());
            if (readyOrderList.size() == 0) {
                log.warn(">>> 线边库中暂无满足加工条件的工单");
                return;
            }
            List<BaseLineStorageVo> tempReadList = null;
            StartOrderEntity startOrderEntity = new StartOrderEntity();
            List<Integer> notInStatusList = new ArrayList<>();
            notInStatusList.add(WorkOrderStatusEnum.DONE.getCode());
            notInStatusList.add(WorkOrderStatusEnum.INSTOCK.getCode());
            ProdWorkOrderDto orderDto = new ProdWorkOrderDto();
            orderDto.setNotInStatusList(notInStatusList);
            //循环遍历线边库是否存在 机床的可加工工单
            for (BaseEquipment baseEquipment : equipmentList) {
                //2023-04-18 07:55:12  添加，处理还有下发加工件没加工完的情况下，再次下发工单
                orderDto.setRealEqp(baseEquipment.getEqpCode());
                orderDto.setDistributed(1);
                List<ProdWorkOrder> distributedList = orderService.getEntityListByParam(orderDto);
                if (distributedList.size() > 0) {
                    log.info(baseEquipment.getEqpCode() + "号机床已存在下发的、未加工完毕的工单，待加工完后继续下发！");
                    continue;
                }
                // 二次校验结束

                tempReadList = readyOrderList.stream()
                        .filter(e -> e.getMachCode().equals(baseEquipment.getEqpCode()))
//                    .filter(e -> e.getStatus().equals(WorkOrderStatusEnum.WAIT.getCode()))
                        .collect(Collectors.toList());
                //该机床待加工件数 > 0
                if (tempReadList.size() > 0) {
                    startOrderEntity.setEqpId(baseEquipment.getId());
                    startOrderEntity.setRealEqp(baseEquipment.getEqpCode());
                    startOrderEntity.setOperationId(tempReadList.get(0).getOperationId());
                    R r = mesController.startOrderNew(startOrderEntity);
                    if (r.getCode() == R.SUCCESS) {
                    }
                    //待加工列表过滤掉此工单
                    readyOrderList = readyOrderList.stream().filter(e -> !e.getOperationId().equals(startOrderEntity.getOperationId())).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
