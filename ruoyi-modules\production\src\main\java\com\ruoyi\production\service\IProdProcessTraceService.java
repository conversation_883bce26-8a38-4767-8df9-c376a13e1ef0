package com.ruoyi.production.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdProcessTrace;
import com.ruoyi.production.domain.vo.ProdProcessTraceVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface IProdProcessTraceService extends IService<ProdProcessTrace> {
    /**
     * 根据条件查询信息
     *
     * @param processTrace
     * @return
     */
    List<ProdProcessTraceVo> listByCondition(ProdProcessTrace processTrace);

    /**
     * 根据条件查询信息
     *
     * @param processTrace
     * @return
     */
    List<ProdProcessTraceVo> getProcessList(ProdProcessTrace processTrace);
}
