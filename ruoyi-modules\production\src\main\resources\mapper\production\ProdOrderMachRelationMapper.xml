<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProdOrderMachRelationMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.ProdOrderMachRelation">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="schedul_code" property="schedulCode"/>
        <result column="bill_code" property="billCode"/>
        <result column="bill_schedul_code" property="billSchedulCode"/>
        <result column="eqp_id" property="eqpId"/>
        <result column="eqp_code" property="eqpCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        remark,
        deleted,
        create_by, create_time, update_by, update_time, schedul_code, bill_code, bill_schedul_code, eqp_id, eqp_code
    </sql>

    <select id="getOrderMachConfig" resultType="com.ruoyi.production.domain.vo.OrderMachConfigVo">
        select omr.id,eqp.id as eqpId, eqp.eqp_code, eqp.eqp_name, eqp.status as eqpStatus, omr.enable
        from base_equipment eqp
        left join prod_order_mach_relation omr on omr.eqp_id = eqp.id
        <where>
            eqp.type=0
            <if test="params.billCode != null">
                and omr.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and omr.schedul_code = #{params.schedulCode}
            </if>
        </where>
        union
        select null as id,eqp.id as eqpId, eqp.eqp_code, eqp.eqp_name, eqp.status as eqpStatus, 0 as enable
        from base_equipment eqp
        <where>
            eqp.type=0
            <if test="params.lineId != null">
                and eqp.line_id = #{params.lineId}
            </if>
        </where>
    </select>
</mapper>
