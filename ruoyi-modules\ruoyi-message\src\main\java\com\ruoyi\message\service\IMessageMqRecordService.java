package com.ruoyi.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.message.api.domain.MessageMqRecord;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-30
 */
public interface IMessageMqRecordService extends IService<MessageMqRecord> {

    List<MessageMqRecord> listByCondition(MessageMqRecord mqRecord);

    int updateByMsgId(MessageMqRecord mqRecord);

    MessageMqRecord getByMsgId(String msgId);

}
