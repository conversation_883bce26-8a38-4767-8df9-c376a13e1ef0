package com.ruoyi.common.security.aspect;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.RedissionLock;
import com.ruoyi.common.security.utils.BeanMapUtilByReflect;
import com.ruoyi.common.security.utils.RedissonLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁/防重复提交 aop
 */
@Aspect
@Component
@Slf4j
public class RedissonLockAop {

    public RedissonLockAop() {
    }

    /**
     * 切点，拦截被 @ResubmitLock 修饰的方法
     */
    @Pointcut("@annotation(com.ruoyi.common.security.annotation.RedissionLock)")
    public void pointcut() {
    }

//    @Before("pointcut()")
//    public void before(JoinPoint joinPoint) {
//        Object[] args = joinPoint.getArgs();    //获取方法入参
//        System.out.println("原方法的入参是："+args[0]);
//        System.out.println("原方法执行前会先执行我！！");
//    }

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        //当前线程名
        String threadName = Thread.currentThread().getName();
        log.info("线程{}------进入分布式锁aop------", threadName);
        //获取参数列表
        Object[] objs = pjp.getArgs();
        //因为只有一个JSON参数，直接取第一个
        Map param = BeanMapUtilByReflect.beanToMap(objs[0]);
        //获取该注解的实例对象
        RedissionLock annotation = ((MethodSignature) pjp.getSignature()).
                getMethod().getAnnotation(RedissionLock.class);
        //生成分布式锁key的键名，以逗号分隔
        String keyParts = annotation.keyParts();
        StringBuffer keyBuffer = new StringBuffer();
        if (StringUtils.isEmpty(keyParts)) {
            log.info("线程{} keyParts设置为空，不加锁", threadName);
            return pjp.proceed();
        } else {
            //生成分布式锁key
            String[] keyPartArray = keyParts.split(",");
            for (String keyPart : keyPartArray) {
                keyBuffer.append(param.get(keyPart));
            }
            String key = keyBuffer.toString();
            log.info("线程{} 要加锁的key={}", threadName, key);
            //获取锁
            if (RedissonLockUtils.tryLock(key, 3000, 5000, TimeUnit.MILLISECONDS)) {
                try {
                    log.info("线程{} 获取锁成功", threadName);
                    return  pjp.proceed();
                } finally {
                    RedissonLockUtils.unlock(key);
                    log.info("线程{} 释放锁", threadName);
                }
            } else {
                log.info("线程{} 获取锁失败", threadName);
                return new AjaxResult(-9, "请求超时");
            }
        }

    }
}
