package com.ruoyi.production.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "BaseFillerType对象", description = "设备类别")
public class BaseFillerType extends MyBaseEntity {

    private String fillerName;

    private Integer fillerType;

    private Long parentId;

    private Long lineId;

    private String alias;

}
