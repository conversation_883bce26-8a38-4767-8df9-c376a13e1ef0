package com.ruoyi.message.controller;


import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.message.api.domain.MessageMqRecord;
import com.ruoyi.message.service.IMessageMqRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-30
 */
@RestController
@RequestMapping("mq")
public class MessageMqRecordController extends BaseModuleController {

    @Autowired
    private IMessageMqRecordService mqRecordService;


    //    --------------------------------------------  inner interface -----------------------------------------

    @InnerAuth
    @ApiOperation("添加新信息")
    @PostMapping("add")
    public R<Boolean> add(@RequestBody MessageMqRecord mqRecord) {
        return R.ok(mqRecordService.save(mqRecord));
    }

    @InnerAuth
    @ApiOperation("查询信息")
    @PostMapping("list")
    public R<List<MessageMqRecord>> list(@RequestBody MessageMqRecord mqRecord) {
        List<MessageMqRecord> mqRecordList = mqRecordService.listByCondition(mqRecord);
        return R.ok(mqRecordList);
    }

    @InnerAuth
    @ApiOperation("更新")
    @PostMapping("update")
    public R<Boolean> update(@RequestBody MessageMqRecord mqRecord) {
        return R.ok(mqRecordService.updateById(mqRecord));
    }

    @InnerAuth
    @ApiOperation("根据消息id更新")
    @PostMapping("updateByMesgId")
    public R<Integer> updateByMesgId(@RequestBody MessageMqRecord mqRecord) {
        return R.ok(mqRecordService.updateByMsgId(mqRecord));
    }

    @InnerAuth
    @ApiOperation("根据消息id更新")
    @GetMapping("getByMesgId/{msgId}")
    public R<MessageMqRecord> getByMesgId(@PathVariable("msgId") String msgId) {
        return R.ok(mqRecordService.getByMsgId(msgId));
    }

}
