package com.ruoyi.message.domian;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "MessageButtonGroup对象", description = "")
public class MessageButtonGroup {

    private Integer id;

    private Integer groupId;

    private Integer buttonId;

    private Integer childGroupId;

}
