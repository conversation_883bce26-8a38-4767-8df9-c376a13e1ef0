package com.ruoyi.production.enums;

public enum EqpStatusEnum {

    OFFLINE(0, "离线"),
    ONLINE(1, "在线");

    private final Integer code;
    private final String info;

    EqpStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(int code) {
        for (EqpStatusEnum type : values()) {
            if (type.getCode() == code) {
                return type.getInfo();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
