package com.ruoyi.production.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.production.domain.BaseFillerType;
import com.ruoyi.production.domain.BasePreset;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.domain.vo.TrayInfoVo;
import com.ruoyi.production.domain.vo.iot.ModifyTrayVo;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.enums.LHStorageFillerTypeEnum;
import com.ruoyi.production.service.IBaseFillerTypeService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.utils.SyncEqpStatusUtil;
import com.ruoyi.production.utils.TrayInfoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("trayInfo")
@Api(value = "托盘信息接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class TrayInfoController extends BaseModuleController {

    @Autowired
    private IProdWorkOrderService prodWorkOrderService;
    @Autowired
    private IBaseFillerTypeService fillerTypeService;
    @Autowired
    private TrayInfoUtils trayInfoUtils;
    @Autowired
    private SyncEqpStatusUtil syncEqpStatusUtil;

    @ApiOperation("预调室托盘修改")
    @PutMapping("roomUpdate")
    @Log(title = "预调室托盘修改", businessType = BusinessType.UPDATE)
    public AjaxResult roomUpdate(@RequestBody TrayInfoVo trayInfoVo) {
        ModifyTrayVo modifyTrayVo = ModifyTrayVo.builder()
                .rackid(trayInfoVo.getRackId() + "." + trayInfoVo.getTag())
                .obj(trayInfoUtils.transferIotObjMod(getLineId(), trayInfoVo))
                .build();
        String url = platformApiService.getEntityByCode("iot_roomRackMdoify_" + getLineId()).getUrl();
        //发送请求
        String result = HttpRequest.post(url)
                .body(JSON.toJSONString(modifyTrayVo))
                .timeout(3000)
                .execute().body();
        return AjaxResult.success(result);
    }

    @ApiOperation("获取产线托架信息")
    @GetMapping("/line")
    public AjaxResult line() throws InterruptedException {

        syncEqpStatusUtil.syncEqpStatus(getLineId());
        syncEqpStatusUtil.syncTrayInfo(getLineId());

        List<List<TrayInfoVo>> list = new ArrayList<>();
        String key = ProductionConstans.tray_info_line + getLineId();
        //信号处理
        Map<String, Object> signalMap = redisService.getCacheMap(ProductionConstans.PROD_EQP_STAT + getLineId() + ":" + EqpTypeEnum.SHELVES.getCode());

        String trayInfo = redisService.getCacheObject(key);
        if (StringUtils.isBlank(trayInfo)) {
            return AjaxResult.success();
        }

        JSONObject source = JSON.parseObject(trayInfo);
        Set<String> trayInfos = source.keySet();
        List<String> dockList = trayInfos.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        for (String rackKey : dockList) {
            JSONObject dockJson = source.getJSONObject(rackKey);

            if (dockJson == null || StringUtils.isBlank(dockJson.toJSONString())) {
                list.add(null);
                continue;
            }
            //获取信号信息
            JSONObject signalInfo = (JSONObject) signalMap.get(rackKey);

            dockJson.put("rackid", rackKey.replace("dock.", ""));
            List<TrayInfoVo> trayInfoVoList = this.transferTrayInfo(dockJson);
            //遍历添加信号信息
            Set<String> signalKeySets = signalInfo.keySet();
            for (TrayInfoVo trayInfoVo : trayInfoVoList) {
                trayInfoVo.setSignal(signalKeySets.contains(trayInfoVo.getTag()) ? signalInfo.getBoolean(trayInfoVo.getTag()) : false);
            }
            list.add(trayInfoVoList);
        }

        return AjaxResult.success(list);
    }

    @ApiOperation("获取预调间托架信息")
    @GetMapping("/room")
    public AjaxResult room() throws InterruptedException {
        syncEqpStatusUtil.syncEqpStatus(11L);
        syncEqpStatusUtil.syncTrayInfo(getLineId());

        BasePreset basePreset = getPresetInfo();
        String key = ProductionConstans.tray_info_room + basePreset.getId();
        String trayInfo = redisService.getCacheObject(key);
        Map<String, Object> signalMap = redisService.getCacheMap(ProductionConstans.PROD_EQP_STAT + "11:" + EqpTypeEnum.BENCH1.getCode());
        JSONObject signalInfo = (JSONObject) signalMap.get(getPresetInfo().getAlias());
        JSONArray trayInfos = JSON.parseArray(trayInfo);
        if (StringUtils.isBlank(trayInfo) || trayInfos.size() < 1) {
            return AjaxResult.success(new ArrayList<>());
        }
        JSONObject dockJson = new JSONObject();
        for (Object tranInfo : trayInfos) {
            JSONObject jsonObject = (JSONObject) tranInfo;
            Set<String> stringSet = jsonObject.keySet();
            if (stringSet.size() < 2) {
                continue;
            }
            for (String dockKeySet : stringSet) {
                //遍历托架对象
                dockJson = jsonObject.getJSONObject(dockKeySet);
                dockJson.put("rackid", dockKeySet);
                break;
            }
            break;
        }

        List<TrayInfoVo> trayInfoVoList = this.transferTrayInfo(dockJson);
        //2022年12月26日 13:52:26 添加plc信号点

        if (null == signalInfo) {
            signalInfo = new JSONObject();
        }
        Set<String> signalKeySets = signalInfo.keySet();
        for (TrayInfoVo trayInfoVo : trayInfoVoList) {
            trayInfoVo.setSignal(signalKeySets.contains(trayInfoVo.getTag()) ? signalInfo.getBoolean(trayInfoVo.getTag()) : false);
        }
        return AjaxResult.success(trayInfoVoList);

    }

    private List<TrayInfoVo> transferTrayInfo(JSONObject dockJson) {

        List<TrayInfoVo> trayInfoVoList = new ArrayList<>();
        //托架id
        String rackid = dockJson.getString("rackid");
        dockJson.remove("rackid");
        //处理元素信息
        Set<String> dockSets = dockJson.keySet();
        for (String dockSet : dockSets) {
            //内容为空
            if (null == dockJson.getString(dockSet)) {
                trayInfoVoList.add(TrayInfoUtils.getEmptyTrayInfo(rackid, dockSet));
                continue;
            }
            JSONObject innerJson = dockJson.getJSONObject(dockSet);
            String objtype = innerJson.getString("objtype");
            JSONObject objJson = innerJson.getJSONObject(objtype);
            //是物料的处理
            if (LHStorageFillerTypeEnum.PART.getInfo().equals(objtype)) {
                String opreationId = objJson.getString("procid");
                ProdWorkOrderDto param = new ProdWorkOrderDto();
                param.setOperationId(opreationId);
                ProdWorkOrderVo workOrder = prodWorkOrderService.getEntityByParam(param);
                if (null == workOrder)
                    throw new ServiceException("托盘关联工单id不存在：" + opreationId);
                trayInfoVoList.add(TrayInfoUtils.getPartTrayInfo(rackid, dockSet, workOrder));
            } else {
                //托盘处理
                TrayInfoVo trayInfoVo = new TrayInfoVo();
                trayInfoVo.setRackId(rackid);
                trayInfoVo.setTag(dockSet);
//                BaseFillerType baseFillerType = fillerTypeService.getByAlias(getLineId(),"tray_small");
                BaseFillerType baseFillerType = fillerTypeService.getByAlias(getLineId(), objJson.getString("trayid"));
                trayInfoVo.setFillerType(baseFillerType.getFillerType());
                trayInfoVo.setFillerTypeName(baseFillerType.getFillerName());

                trayInfoVoList.add(trayInfoVo);
//                trayInfoVoList.add(TrayInfoUtils.getTrayInfo(rackid, dockSet, objJson.getString("trayid")));
            }
        }

        trayInfoVoList.sort(new Comparator<TrayInfoVo>() {
            @Override
            public int compare(TrayInfoVo o1, TrayInfoVo o2) {
                String tag1 = o1.getTag();
                String tag2 = o2.getTag();
                Integer positon1 = Integer.valueOf(tag1.substring(tag1.length() - 1, tag1.length()));
                Integer positon2 = Integer.valueOf(tag2.substring(tag2.length() - 1, tag2.length()));
                char t1 = tag1.toCharArray()[0];
                char t2 = tag2.toCharArray()[0];
                if (positon1 < positon2)
                    return -1;
                if (positon1 > positon2)
                    return 1;
                if (positon1.equals(positon2)) {
                    if (Integer.valueOf(t1) > Integer.valueOf(t2))
                        return -1;
                    if (Integer.valueOf(t1) < Integer.valueOf(t2))
                        return 1;
                }
                return 0;
            }
        });
        return trayInfoVoList;
    }

    public static void main(String[] args) {
        int a = 10;
        Integer b = 10;
        System.out.println(a == b);
        System.out.println(b.equals(a));
    }

}
