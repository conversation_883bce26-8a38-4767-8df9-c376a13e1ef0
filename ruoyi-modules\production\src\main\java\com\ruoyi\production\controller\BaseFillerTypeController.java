package com.ruoyi.production.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.BaseFillerType;
import com.ruoyi.production.service.IBaseFillerTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("fillerType")
@Api(value = "设备类型控制器", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class BaseFillerTypeController extends BaseModuleController {

    @Autowired
    private IBaseFillerTypeService fillerTypeService;

    @ApiOperation("获取产线设备类型")
    @GetMapping("getByLineId")
    public AjaxResult getByLineId() {
        return AjaxResult.success(fillerTypeService.getListByLineId(getLineId()));
    }

    @ApiOperation("根据参数获取设备类型")
    @PostMapping("list")
    public AjaxResult list(BaseFillerType baseFillerType) {
        baseFillerType.setLineId(getLineId());
        return AjaxResult.success(fillerTypeService.listByCondition(baseFillerType));
    }

}
