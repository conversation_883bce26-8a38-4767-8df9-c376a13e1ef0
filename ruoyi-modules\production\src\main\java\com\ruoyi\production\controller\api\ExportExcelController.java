package com.ruoyi.production.controller.api;

import com.ruoyi.production.controller.BaseModuleController;
import com.ruoyi.production.domain.dto.detection.QualityReportDataDto;
import com.ruoyi.production.service.IProdMachineDetectionDetailService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@Api(value = "开放给管控端的接口")
@RequestMapping("api/export")
public class ExportExcelController extends BaseModuleController {
    @Autowired
    private IProdMachineDetectionDetailService detailService;

    @GetMapping("/exportQualityReport")
    public void exportQualityReport(HttpServletResponse response,QualityReportDataDto dto) {
        detailService.exportQualityReportExcel(response, dto);
    }
}
