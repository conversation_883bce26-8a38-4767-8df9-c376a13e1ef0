package com.ruoyi.production.core.observable;

import com.ruoyi.production.domain.ProdWorkOrder;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class OrderRestoreEvent extends ApplicationEvent {
    private ProdWorkOrder workOrder;
    public OrderRestoreEvent(Object source, ProdWorkOrder workOrder) {
        super(source);
        this.workOrder = workOrder;
    }

}
