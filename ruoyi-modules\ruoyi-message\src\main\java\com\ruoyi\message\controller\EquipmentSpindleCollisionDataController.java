package com.ruoyi.message.controller;


import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.message.domian.EquipmentSpindleCollisionData;
import com.ruoyi.message.service.IEquipmentSpindleCollisionDataService;
import io.swagger.annotations.ApiOperation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("collisionData")
public class EquipmentSpindleCollisionDataController extends BaseModuleController {

    @Autowired
    private RedisService redisService;
    @Autowired
    private IEquipmentSpindleCollisionDataService collisionDataService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @ApiOperation("add用户id缓存")
    @PostMapping("addUserIdCache")
    public AjaxResult addUserIdCache() {

        redisTemplate.opsForSet().add(ProductionConstans.REDIS_WS_COLLISION + getLineId(),getSysUser().getUserId());

        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setUrl("dcs_getCollisionData");
        webSocketVo.setLineId(getLineId());

        rabbitTemplate.convertAndSend("direct_interface_exchange", "xyz", JSON.toJSONString(webSocketVo));

//        Set idSet = redisService.getCacheSet(ProductionConstans.REDIS_WS_COLLISION + getLineId());
//        if (null == idSet)
//            idSet = new HashSet();

//        idSet.add(getSysUser().getUserId());
//        redisTemplate.opsForSet()
//        redisService.setCacheSet(ProductionConstans.REDIS_WS_COLLISION + getLineId(), idSet);

        return AjaxResult.success();
    }

    @ApiOperation("移除用户id缓存")
    @PostMapping("removeUserIdCache")
    public AjaxResult removeUserIdCache() {

        redisTemplate.opsForSet().remove(ProductionConstans.REDIS_WS_COLLISION + getLineId(),getSysUser().getUserId());

        return AjaxResult.success();
    }

    @InnerAuth
    @PostMapping("getCollisionDataListByMap")
    public R<List<EquipmentSpindleCollisionData>> getCollisionDataListByMap(@RequestBody Map params) {
        return R.ok(collisionDataService.listByCondition(params));
    }

}
