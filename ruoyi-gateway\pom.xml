<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi</artifactId>
        <version>3.5.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-gateway</artifactId>

    <description>
        ruoyi-gateway网关模块
    </description>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        </dependency>

        <!-- SpringCloud Gateway -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>


        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel Gateway -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-sentinel-gateway</artifactId>
        </dependency>

        <!-- Sentinel Datasource Nacos -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- SpringCloud Loadbalancer -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>

        <!--验证码 -->
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
        </dependency>

        <!-- RuoYi Common Redis-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common-redis</artifactId>
        </dependency>

        <!-- Swagger -->
        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger-ui</artifactId>-->
        <!--            <version>${swagger.fox.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger2</artifactId>-->
        <!--            <version>${swagger.fox.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-micro-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.3.6</version>
                <configuration>
                    <repository>${project.artifactId}</repository>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
<!--            Docker构建配置信息 begin 岳琪版本-->
<!--                        <plugin>
            &lt;!&ndash;                <groupId>com.spotify</groupId>&ndash;&gt;
                            <groupId>io.fabric8</groupId>
                            <artifactId>docker-maven-plugin</artifactId>
                            <version>0.33.0</version>
                            <configuration>
                                &lt;!&ndash;这一部分是为了实现对远程docker容器的控制&ndash;&gt;
                                &lt;!&ndash;docker主机地址,用于完成docker各项功能,注意是tcp不是http!&ndash;&gt;
                                <dockerHost>tcp://*************:2375</dockerHost>
                                <images>
                                    <image>
                                        <name>${docker.registry}/${project.artifactId}:${project.modelVersion}</name>
                                        <registry>${docker.registry}</registry>
                                        <build>
                                            &lt;!&ndash; 指定dockerfile文件的位置&ndash;&gt;
                                            <dockerFile>${project.basedir}/src/main/docker/Dockerfile</dockerFile>
            &lt;!&ndash;                                <buildOptions>&ndash;&gt;
            &lt;!&ndash;                                    &lt;!&ndash; 网络的配置，与宿主主机共端口号&ndash;&gt;&ndash;&gt;
            &lt;!&ndash;                                    <network>host</network>&ndash;&gt;
            &lt;!&ndash;                                </buildOptions>&ndash;&gt;
                                        </build>
                                    </image>
                                </images>
                                <buildArgs>
                                    &lt;!&ndash; dockerfile参数，指定jar路径 &ndash;&gt;
                                    <JAR_FILE>${project.build.directory}/${project.build.finalName}.jar</JAR_FILE>
                                </buildArgs>
                            </configuration>
                        </plugin>-->

<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>dockerfile-maven-plugin</artifactId>-->
<!--                <version>1.3.6</version>-->
<!--&lt;!&ndash;                <version>1.3.7</version>&ndash;&gt;-->
<!--                <configuration>-->
<!--&lt;!&ndash;                    <dockerDirectory>src/main/docker</dockerDirectory>&ndash;&gt;-->
<!--                    <repository>${project.artifactId}</repository>-->
<!--                    <buildArgs>-->
<!--                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>-->
<!--                    </buildArgs>-->
<!--&lt;!&ndash;                    <imageName>${docker.registry}/${project.artifactId}:latest</imageName>-->

<!--                    <registryUrl>http://docker.casicloud.com:5000/v2</registryUrl>-->
<!--                    <resources>-->
<!--                        <resource>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>${project.build.finalName}.jar</include>-->
<!--                        </resource>-->
<!--                    </resources>&ndash;&gt;-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>


</project>
        <!--            Docker构建配置信息 end-->
        <!--            <plugin>-->
        <!--                <groupId>com.spotify</groupId>-->
        <!--                <artifactId>dockerfile-maven-plugin</artifactId>-->
        <!--                <version>1.4.13</version>-->
        <!--                <executions>-->
        <!--                    &lt;!&ndash;运行mvn package时，会自动执行build目标，构建Docker镜像;-->
        <!--                    运行mvn deploy命令时，会自动执行push目标，将Docker镜像push到Docker仓库&ndash;&gt;-->
        <!--                    <execution>-->
        <!--                        <id>default</id>-->
        <!--                        <goals>-->
        <!--                            <goal>build</goal>-->
        <!--                            <goal>push</goal>-->
        <!--                        </goals>-->
        <!--                    </execution>-->
        <!--                </executions>-->

        <!--                <configuration>-->
        <!--                    <dockerfile>${project.basedir}/src/main/docker/Dockerfile</dockerfile>-->
        <!--&lt;!&ndash;                    <imageName>${docker.registry}/${project.artifactId}:${project.modelVersion}</imageName>&ndash;&gt;-->
        <!--&lt;!&ndash;                    <dockerDirectory>${project.basedir}/src/main/docker</dockerDirectory>&ndash;&gt;-->
        <!--&lt;!&ndash;                    <registryUrl>http://${docker.registry}/v2</registryUrl>&ndash;&gt;-->
        <!--                    <repository>${docker.registry}/${project.artifactId}</repository>-->
        <!--                    <tag>${project.version}</tag>-->
        <!--                    &lt;!&ndash;在settings.xml中配置server&ndash;&gt;-->
        <!--&lt;!&ndash;                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>&ndash;&gt;-->
        <!--                    &lt;!&ndash;可以指定一个或多个变量，传递给Dockerfile，在Dockerfile中通过ARG指令进行引用&ndash;&gt;-->
        <!--                    <buildArgs>-->
        <!--                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>-->
        <!--                    </buildArgs>-->
        <!--                </configuration>-->
        <!--            </plugin>-->