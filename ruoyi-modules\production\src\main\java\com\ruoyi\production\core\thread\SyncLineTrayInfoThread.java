package com.ruoyi.production.core.thread;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.production.domain.BaseLine;
import com.ruoyi.production.service.IBaseLineService;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.production.utils.RabbitMsgUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Callable;

public class SyncLineTrayInfoThread implements Callable {

    private Long lineId;
    private static final Logger logger = LoggerFactory.getLogger(SyncLineTrayInfoThread.class);

    private static ISysPlatformApiService apiService;
    private static RedisService redisService;
    private static IBaseLineService baseLineService;
//    private static RabbitTemplate rabbitTemplate;
    private static RabbitMsgUtils rabbitMsgUtils;

    public SyncLineTrayInfoThread(Long lineId) {
        this.lineId = lineId;
    }

    static{
        apiService = SpringUtils.getBean(ISysPlatformApiService.class);
        redisService = SpringUtils.getBean(RedisService.class);
        baseLineService = SpringUtils.getBean(IBaseLineService.class);
//        rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
        rabbitMsgUtils = SpringUtils.getBean(RabbitMsgUtils.class);
    }

    @Override
    public Object call() {
        try {
            //todo 查询产线对应托盘信息
            BaseLine baseLine = baseLineService.getById(lineId);
            String apiCode = "iot_rack_" + lineId;
            if (null == apiService.getEntityByCode(apiCode)){
                return "无产线接口地址";
            }
            String url = apiService.getEntityByCode(apiCode).getUrl();
            JSONObject requestParam = new JSONObject();
            requestParam.put("wplid", baseLine.getAlias());
//            Thread.sleep(1000);
            String result = HttpRequest.post(url).timeout(3000)
                    .execute().body();
//            String result = "{\"dock.1\":{\"s1\":{\"part\":{\"no\":\"100012603-01_60_26\",\"mat\":\"壳体\",\"machid\":\"103\",\"procid\":\"37CC135DBEE34AA0963D6C917C1678EC\",\"status\":\"mat\"},\"objtype\":\"part\"},\"s2\":null,\"b1\":null},\"dock.2\":{\"s1\":null,\"s2\":null,\"b1\":null}}";
            String key = ProductionConstans.tray_info_line + lineId;

            //1相同，跳过
            if (result.equals(redisService.getCacheObject(key))){
                return "********** 产线id:" + lineId + " 线边托盘信息无变化";
            } else {
                //2不相同，设置缓存，生产消息
//                SysPlatformApi api = apiService.getEntityByCode("dcs_getLineTrayInfo");
//                WebSocketVo webSocketVo = new WebSocketVo();
//                webSocketVo.setUrl(api.getUrl());
//                webSocketVo.setMethod(api.getApiMethod());
//                webSocketVo.setLineId(lineId);
                redisService.setCacheObject(key, result);
//                rabbitTemplate.convertAndSend("direct_interface_exchange","normal", JSON.toJSONString(webSocketVo));
                rabbitMsgUtils.updateLineTrayInfo(lineId);
//                RabbitMsgUtils.SendMessage(webSocketVo, ProductionConstans.mq_websocket_fanout_exchange);
            }
        } catch (Exception e) {
//            e.printStackTrace();
            logger.error("@@@@@@@@@@ 产线id:" + lineId + " 线边托盘信息更新失败>>>" + e.getMessage());
            return "@@@@@@@@@@ 产线id:" + lineId + " 线边托盘信息更新失败>>>" + e.getMessage();
        }
        return "**********产线id:" + lineId + " 线边托盘信息更新成功";
    }
}
