package com.ruoyi.common.core.web.mapper;


import com.ruoyi.common.core.web.domain.MyBaseEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * 数据层 基类通用数据处理
 *
 * @param <D> Dto
 * <AUTHOR>
 */
public interface BaseMapper<D extends MyBaseEntity> extends com.baomidou.mybatisplus.core.mapper.BaseMapper<D> {

    /**
     * 自定义批量插入
     */
    int insertBatch(@Param("collection") Collection<D> list);

    /**
     * 自定义批量更新，条件为主键
     */
    int updateBatch(@Param("collection") Collection<D> list);
}
