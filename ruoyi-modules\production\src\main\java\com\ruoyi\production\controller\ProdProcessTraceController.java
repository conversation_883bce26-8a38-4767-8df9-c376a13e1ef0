package com.ruoyi.production.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.ProdProcessTrace;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.vo.ProdProcessTraceVo;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.service.IProdProcessTraceService;
import com.ruoyi.production.service.IProdWorkOrderService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("processTrace")
public class ProdProcessTraceController extends BaseModuleController {

    @Autowired
    private IProdProcessTraceService traceService;
    @Autowired
    private IProdWorkOrderService orderService;

    @ApiOperation("查询车间加工流程跟踪列表")
    @PostMapping("list")
//    @Cacheable(value = "prod:processTraceList")
    public AjaxResult getList(@RequestBody ProdProcessTrace processTrace) {
        //获取产线id
        processTrace.setLineId(getLineId());
        //获取全部加工节点流程信息
        List<ProdProcessTraceVo> processTraceList = traceService.getProcessList(processTrace);
        //计算机床加工时间列表
        ProdWorkOrder workOrderParam = new ProdWorkOrder();
        workOrderParam.setLineId(getLineId());
        List<ProdWorkOrderVo> workOrderVos = orderService.getProcessTraceList(workOrderParam);
        for (ProdProcessTraceVo prodProcessTraceVo : processTraceList) {
            String eqpCode = prodProcessTraceVo.getEqpCode();
            if (StringUtils.isBlank(eqpCode))
                continue;
//            workOrderParam.setLineId(prodProcessTraceVo.getLineId());
//            workOrderParam.setRealEqp(prodProcessTraceVo.getEqpCode());

            //过滤剩余数量为0的数据
            List<ProdWorkOrderVo> tempVos = workOrderVos.stream()
                    .filter(e -> e.getRealEqp() != null)
                    .filter(e -> e.getRealEqp().equals(prodProcessTraceVo.getEqpCode()))
                    .filter(orderVo -> orderVo.getRemainNum() > 0)
                    .collect(Collectors.toList());
            prodProcessTraceVo.setWorkOrderVos(tempVos);
        }
        return AjaxResult.success(processTraceList);
    }

}
