package com.ruoyi.production.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@Data
@ApiModel(value = "ProdWorderMachRelation对象", description = "")
public class ProdOrderMachRelation extends MyBaseEntity {

    @ApiModelProperty(value = "配置机床是否启用")
    private Integer enable;

    private String schedulCode;

    private String billCode;

    private String billSchedulCode;
    @ApiModelProperty(value = "机床id")
    private Long eqpId;
    @ApiModelProperty(value = "机床代码")
    private String eqpCode;

    private Long lineId;

}
