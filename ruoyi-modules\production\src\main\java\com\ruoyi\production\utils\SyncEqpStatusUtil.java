package com.ruoyi.production.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.core.thread.SyncLineEqpInfoThread;
import com.ruoyi.production.core.thread.SyncLineTrayInfoThread;
import com.ruoyi.production.core.thread.SyncRoomTrayInfoThread;
import com.ruoyi.production.domain.BaseLine;
import com.ruoyi.production.domain.SysPlatformApi;
import com.ruoyi.production.enums.EqpStatusEnum;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IBaseLineService;
import com.ruoyi.production.service.IBasePresetService;
import com.ruoyi.production.service.ISysPlatformApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SyncEqpStatusUtil {

    @Autowired
    private ISysPlatformApiService apiService;
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private IBasePresetService presetService;
    @Autowired
    protected IBaseLineService lineService;
    @Resource
    private ThreadPoolTaskExecutor myTaskExecutor;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RabbitMsgUtils rabbitMsgUtils;

    private static final int lockTime = 1000;

    ThreadLocal<BaseLine> threadLocal = new ThreadLocal<>();

    /**
     * 同步托盘信息
     */
//    @Scheduled(cron = "0/3 * * * * ? ")
    //fixedDelay-- 当前任务执行完毕两秒后再执行
//    @Scheduled(fixedDelay = 2000)
    public void syncTrayInfo(Long lineId) {
//        if (!redisService.tryLock(ProductionConstans.REDIS_JOB_TRAYINFO, "mach", 2, -1))
//            return;
//        //todo 查询所有产线
        try {
            //同步产线托盘信息
            List<Future> futureList = new ArrayList<>();
            SyncLineTrayInfoThread lineTrayInfoThread = new SyncLineTrayInfoThread(lineId);
            Future future = myTaskExecutor.submit(lineTrayInfoThread);
            futureList.add(future);
            Thread t = new Thread();

            //同步预调台托盘信息
            SyncRoomTrayInfoThread roomTrayInfoThread = new SyncRoomTrayInfoThread(lineId);
            future = myTaskExecutor.submit(roomTrayInfoThread);
            futureList.add(future);

//            List<BasePreset> presetList = presetService.getLitByLineId(lineId);
//            for (BasePreset basePreset : presetList) {
//                SyncRoomTrayInfoThread roomTrayInfoThread = new SyncRoomTrayInfoThread(basePreset.getId());
//                future = myTaskExecutor.submit(roomTrayInfoThread);
//                futureList.add(future);
//            }

//            同步状态plc信息
//            SyncLineEqpInfoThread lineEqpInfoThread = new SyncLineEqpInfoThread(lineId);
//            future = myTaskExecutor.submit(lineEqpInfoThread);
//            futureList.add(future);

            //打印托盘信息同步结果
//            for (Future f : futureList) {
//                log.info(f.get().toString());
//            }

        } catch (Exception e) {
            log.error("@@@@@@@@@@  托盘信息同步执行异常>>>" + e.getMessage());
            e.printStackTrace();
        }

    }

    /**
     * 定时同步各产线自动流程开关状态
     */
    @Scheduled(fixedDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void syncFlowStatus() {
        List<BaseLine> lineList = lineService.getDistinctLineList();
        for (BaseLine baseLine : lineList) {
            String lineCode = baseLine.getLineCode();
            if (lineCode.equals("0"))
                continue;
            SysPlatformApi api = apiService.getEntityByCode("iot_flowStatus_" + lineCode);
            if (null == api) {
                log.warn(">>>>>  未查询到控制系统流程状态查询接口url lineCode:" + lineCode);
            }
            String response = HttpRequest.post(api.getUrl())
                    .timeout(3000)
                    .execute().body();
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject.containsKey("data")) {
                redisService.setCacheObject("liaohui:flowStatus:" + lineCode, jsonObject.getBoolean("data"));
            }
        }
    }

    /**
     * 定时同步各产线设备状态
     */
    @Scheduled(fixedDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void syncAllEqpStatus() {
//        System.out.println("开始同步设备状态" + new Date());
        List<BaseLine> lineList = lineService.getDistinctLineList();
        for (BaseLine baseLine : lineList) {
            if (baseLine.getLineCode().equals("0"))
                continue;
//            this.syncEqpStatusMain(baseLine.getId());
            SyncLineEqpInfoThread lineEqpInfoThread = new SyncLineEqpInfoThread(baseLine.getId());
            Future future = myTaskExecutor.submit(lineEqpInfoThread);
        }
    }

    /**
     * 同步机床等状态
     */
    //30秒执行一次
//    @Scheduled(cron = "0/30 * * * * ? ")
//    @Scheduled(fixedDelay = 30, timeUnit = TimeUnit.MINUTES)
    public void syncEqpStatus(Long lineId) {
        try {
            //同步产线托盘信息
            List<Future> futureList = new ArrayList<>();
            SyncLineEqpInfoThread lineEqpInfoThread = new SyncLineEqpInfoThread(lineId);
            Future future = myTaskExecutor.submit(lineEqpInfoThread);
            futureList.add(future);
            //打印托盘信息同步结果
//            for (Future f : futureList) {
//                log.info(f.get().toString());
//            }

        } catch (Exception e) {
            log.error("@@@@@@@@@@  产线plc状态同步执行异常>>>" + e.getMessage());
            e.printStackTrace();
        }

    }

    public void syncEqpStatusMain(Long lineId) {
        List<BaseEquipment> equipments;
        List<BaseEquipment> updateList = new ArrayList<>();
        Map<Integer, Map<String, Object>> map = new HashMap<>();
        //循环遍历机床信息
        BaseEquipment eqpParam = new BaseEquipment();
        eqpParam.setLineId(lineId);
        equipments = equipmentService.listByCondition(eqpParam);
        if (equipments == null || equipments.size() == 0) {
            log.error(">>>产线：" + lineId + " 无设备");
            return;
        }
        SysPlatformApi api = apiService.getEntityByCode("iot_lineStatus_" + lineId);
        if (null == api) {
            log.error(">>>产线：" + lineId + " 无同步设备状态接口地址");
            return;
        }

        String result = HttpRequest.post(api.getUrl())
                .timeout(3000)
                .execute().body();
//        String result = "";
        JSONObject response = JSON.parseObject(result);
//            logger.info("********** 同步PLC点位接口返回：" + result);

        for (BaseEquipment equipment : equipments) {
            BaseEquipment updateEqp = new BaseEquipment();
            updateEqp.setId(equipment.getId());
            if (response.containsKey(equipment.getAlias())) {
                Map<String, Object> sonMap;
                if (map.containsKey(equipment.getType())) {
                    sonMap = map.get(equipment.getType());
                } else {
                    sonMap = new HashMap<>();
                }
                if (response.get(equipment.getAlias()) instanceof Boolean) {
                    if (response.getBoolean(equipment.getAlias())) {
                        updateEqp.setStatus(1);
                    } else {
                        updateEqp.setStatus(0);
                        //2023年3月17日 15:36:59 添加，机床离线，设置自动加工关闭
                        updateEqp.setAutoProcess(0);
                    }
                    //判断状态是否有变化才
                    if (!updateEqp.getStatus().equals(equipment.getStatus())) {
                        updateList.add(updateEqp);
                    }
                }
                sonMap.put(equipment.getAlias(), response.get(equipment.getAlias()));
                map.put(equipment.getType(), sonMap);
            }
        }
        //有变化才保存
        if (updateList.size() > 0) {
            equipmentService.updateBatchById(updateList);
            for (Map.Entry<Integer, Map<String, Object>> entry : map.entrySet()) {
                redisService.setCacheMap(ProductionConstans.PROD_EQP_STAT + lineId + ":" + entry.getKey(), entry.getValue());
            }
            rabbitMsgUtils.updateProcessTraceList(lineId);
            log.info("********* 状态同步 》》》 状态发生变化");
        }
    }

    /**
     * 同步线Agv状态
     */
    //@Scheduled(cron = "0/5 * * * * ? ")
    //@Scheduled(cron = "0 */1 * * * ?")
    public void syncAgvStatus() {
        String agvQueryStatus = apiService.getEntityByCode("agv_query_status").getUrl();
        JSONObject param = new JSONObject();
        param.put("reqCode", UUID.randomUUID().toString().replace("-", ""));
        param.put("mapshortName", "XY");
        String result = HttpRequest.post(agvQueryStatus)
                .timeout(3000)
                .body(param.toJSONString())
                .execute().body();
        JSONObject agvResponse = JSON.parseObject(result);
        JSONArray agvArray = agvResponse.getJSONArray("data");

        //获取所有agv数据
        BaseEquipment eqpParam = new BaseEquipment();
        eqpParam.setType(EqpTypeEnum.AGV.getCode());
        List<BaseEquipment> equipments = equipmentService.listByCondition(eqpParam);

        for (int i = 0; i < agvArray.size(); i++) {
            JSONObject agvEntity = agvArray.getJSONObject(i);
            String robotCode = agvEntity.getString("robotCode");
            Integer status = agvEntity.getInteger("status");
            equipments.forEach(agv -> {
                if (robotCode.equals(agv.getEqpCode())) {
                    if (status > 0) {
                        agv.setStatus(EqpStatusEnum.ONLINE.getCode());
                        equipmentService.updateById(agv);
                    }
                }
            });
        }
        log.info("********** 更新Agv状态成功");
    }

}
