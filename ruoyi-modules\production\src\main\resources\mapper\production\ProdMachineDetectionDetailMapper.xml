<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProdMachineDetectionDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.ProdMachineDetectionDetail">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="pid" property="pid"/>
        <result column="name" property="name"/>
        <result column="target_value" property="targetValue"/>
        <result column="upper_tolerance" property="upperTolerance"/>
        <result column="lower_tolerance" property="lowerTolerance"/>
        <result column="real_value" property="realValue"/>
        <result column="program_result" property="programResult"/>
        <result column="verify_value" property="verifyValue"/>
        <result column="verify_result" property="verifyResult"/>
        <result column="status_value" property="statusValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        remark,
        deleted,
        create_by, create_time, update_by, update_time, status, pid, name, target_value, upper_tolerance, lower_tolerance, real_value, program_result, verify_value, verify_result,status_value
    </sql>

    <select id="listByCondition" parameterType="map"
            resultType="com.ruoyi.production.domain.vo.detection.ProdMachineDetectionDetailVo">
        select
        id,
        remark,
        create_by, create_time, update_by, update_time, status, pid, name, target_value, upper_tolerance,
        lower_tolerance, real_value, program_result, verify_value, verify_result,status_value
        FROM
        prod_machine_detection_detail detail
        <where>
            detail.report_data = 0
            <if test="params.pid != null">
                and detail.pid = #{params.pid}
            </if>
        </where>
    </select>

    <select id="listForPage" resultType="com.ruoyi.production.domain.vo.detection.ProdMachineDetectionDetailVo">
        select
        *
        FROM
        prod_machine_detection_detail detail
        <where>
            detail.report_data = 0
            <if test="params.pid != null">
                and detail.pid = #{params.pid}
            </if>
            <if test="params.name != null">
                and detail.name = #{params.name}
            </if>
        </where>
    </select>

    <select id="getColumns" resultType="com.ruoyi.production.domain.vo.detection.GetColumnVo">
        select distinct detail.name,detail.report_data
        from prod_machine_detection_detail detail
                 left join prod_machine_detection detection on detail.pid = detection.id
                 left join prod_work_order o on o.operation_id = detection.operation_id
        where o.bill_schedul_code = #{bscode}
        order by detail.name
    </select>

    <select id="getQualityReportData"
            resultType="com.ruoyi.production.domain.vo.detection.ProdMachineDetectionDetailVo">
        select o.real_start_time,o.real_end_time,o.mat_name,o.real_eqp,o.eqp_code,o.operation_split,detail.name,detail.target_value,detail.real_value,
        detail.verify_result,detail.status,detail.id,detection.id as pid,detail.special_value,detail.special_result,detail.report_data
        from prod_work_order o
        left join prod_machine_detection detection on detection.operation_id = o.operation_id
        left join prod_machine_detection_detail detail on detail.pid = detection.id
            <where>
                o.status >= 5
                <if test="params.bscode != null">
                    and o.bill_schedul_code = #{params.bscode}
                </if>
                <if test="params.columnList != null and params.columnList.size()>0">
                    AND detail.name IN
                    <foreach item="item" index="index" collection="params.columnList" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </where>
        order by o.operation_split,detail.name
    </select>
</mapper>
