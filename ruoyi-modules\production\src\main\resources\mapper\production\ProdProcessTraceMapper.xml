<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProdProcessTraceMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.ProdProcessTrace">
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
        <result column="line_id" property="lineId"/>
        <result column="process_id" property="processId"/>
        <result column="operation_id" property="operationId"/>
        <result column="status" property="status"/>
        <result column="stop_reason" property="stopReason"/>
        <result column="current_position" property="currentPosition"/>
        <result column="trace_type" property="traceType"/>
        <result column="mat_type" property="matType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_by
                ,
        create_time,
        update_by,
        update_time,
        id,
        deleted,
        remark, line_id, process_id, operation_id, status, stop_reason, current_position, trace_type, mat_type,operation_id
    </sql>

    <select id="listByCondition" parameterType="map" resultType="com.ruoyi.production.domain.vo.ProdProcessTraceVo">
        select
        wo.ticket_number,
        wo.real_start_time,
        wo.real_end_time,
        bp.process_name,
        bp.process_code,
        ptt.status,
        eqp.status as eqp_status,
        eqp.eqp_name,
        eqp.eqp_code,
        eqp.id as eqpId,
        eqp.auto_process,
        eqp.processing,
        wo.mat_name,
        wo.schedul_code,
        wo.bill_code,
        wo.operation_id,
        wo.status as orderStatus
        from base_equipment eqp
        left join (select status,eqp_code,operation_id,process_id from prod_process_trace pt where pt.id in (select MAX(id) from prod_process_trace group by eqp_code))ptt on ptt.eqp_code=eqp.eqp_code
        left join (select status,ticket_number,real_start_time,real_end_time,mat_name,schedul_code,bill_code,operation_id from prod_work_order where deleted = 0) wo on wo.operation_id = ptt.operation_id
        LEFT JOIN base_process bp ON bp.id = ptt.process_id
        <where>
            eqp.type=0
            <if test="params.lineId != null">
                and eqp.line_id = #{params.lineId}
            </if>
        </where>
        order by eqp.id asc
    </select>
</mapper>
