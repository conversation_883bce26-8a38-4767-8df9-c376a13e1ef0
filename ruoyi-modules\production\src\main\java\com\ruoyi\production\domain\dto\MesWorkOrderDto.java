package com.ruoyi.production.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MesWorkOrderDto {
    String id;
    String orderCode;
    String ppOrderCode;
    String ppWorkOrderCode;
    String flowId;
    String routeName;
    String centerCode;
    String deviceCode;
    String materialCode;
    String materialName;
    Integer doQty;

    @ApiModelProperty(value = "排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date planStartTime;

    @ApiModelProperty(value = "排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date planFinishTime;
}
