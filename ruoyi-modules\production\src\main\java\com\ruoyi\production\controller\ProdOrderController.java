package com.ruoyi.production.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.dto.ordermachconfig.OrderMachConfigDto;
import com.ruoyi.production.service.IProdOrderMachRelationService;
import com.ruoyi.production.service.IProdWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("order")
@Api(value = "订单controller", produces = MediaType.APPLICATION_JSON_VALUE)
public class ProdOrderController extends BaseModuleController {

    @Autowired
    private IProdWorkOrderService workOrderService;

    @Autowired
    private IProdOrderMachRelationService worderMachRelationService;

    @ApiOperation("根据参数获取订单机床映射配置")
    @GetMapping("getOrderMachConfig")
    public AjaxResult getOrderMachConfig(OrderMachConfigDto dto) {
        //获取产线id
        dto.setLineId(getLineId());
        return AjaxResult.success(worderMachRelationService.getOrderMachConfig(dto));
    }

    @ApiOperation("保存订单机床映射配置")
    @PostMapping("saveOrderMachConfig")
    public AjaxResult saveOrderMachConfig(@RequestBody OrderMachConfigDto dto) {
        dto.setLineId(getLineId());
        worderMachRelationService.saveOrderMachConfig(dto);
        return AjaxResult.success();
    }

}
