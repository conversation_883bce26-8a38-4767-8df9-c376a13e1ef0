package com.ruoyi.production.domain.dto.operation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "调度出、入库参数模型", description = "调度出、入库参数模型")
@Data
public class InOutStockDto {
    @ApiModelProperty(value = "任务号")
    private String billcode;
    @ApiModelProperty(value = "工序号")
    private String schedulCode;
    @ApiModelProperty(value = "入库数量")
    private Integer instockNum;
    @ApiModelProperty(value = "出库数量")
    private Integer outstockNum;
    @ApiModelProperty(value = "立库号")
    private String positionCode;

    @ApiModelProperty(value = "任务号")
    private String reqid;
    @ApiModelProperty(value = "工序号")
    private String process;
}
