package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.domain.ProdMachineDetection;
import com.ruoyi.production.domain.ProdMachineDetectionDetail;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.detection.AddDetailColumnDto;
import com.ruoyi.production.domain.dto.detection.DeleteDetailColumnDto;
import com.ruoyi.production.domain.dto.detection.QualityReportDataDto;
import com.ruoyi.production.domain.vo.detection.*;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.mapper.ProdMachineDetectionDetailMapper;
import com.ruoyi.production.mapper.ProdMachineDetectionMapper;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IProdMachineDetectionDetailService;
import com.ruoyi.production.service.IProdWorkOrderService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Service
public class ProdMachineDetectionDetailServiceImpl extends ServiceImpl<ProdMachineDetectionDetailMapper, ProdMachineDetectionDetail> implements IProdMachineDetectionDetailService {

    @Autowired
    private ProdMachineDetectionMapper detectionMapper;
    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private IBaseEquipmentService equipmentService;

    private final DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public List<ProdMachineDetectionDetailVo> listByCondition(ProdMachineDetectionDetail detail) {
        return this.baseMapper.listByCondition(detail);
    }

    @Override
    public IPage<ProdMachineDetectionDetailVo> listForPage(ProdMachineDetectionDetail detail, Integer current, Integer pageSiz) {
        Page<ProdMachineDetectionDetailVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);
        return this.getBaseMapper().listForPage(page, detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDetectionDetail(List<ProdMachineDetectionDetail> detailList) {
        for (ProdMachineDetectionDetail detail : detailList) {
            //自检
            if (StringUtils.isBlank(detail.getVerifyValue())) {
                detail.setCompareResult(null);
                detail.setVerifyResult(null);
                detail.setUpdateBy(null);
                detail.setVerifyTime(null);
            }
            //专检
            if (StringUtils.isBlank(detail.getSpecialValue())) {
                detail.setSpecialResult(-1);
            }
        }
        this.updateBatchById(detailList);
        //查询所有记录
        QueryWrapper<ProdMachineDetectionDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid", detailList.get(0).getPid());
        detailList = this.list(queryWrapper);
        ProdMachineDetection detection = detectionMapper.selectById(detailList.get(0).getPid());
        if (detection == null) {
            return true;
        }
        //添加自检时间
        detection.setVerifyTime(new Date());
        //生成主表自检结果
        int vr0 = 0;
        int vr1 = 0;
        //生成主表自检结果
        int sv0 = 0;
        int sv1 = 0;
        for (ProdMachineDetectionDetail detail : detailList) {
            Integer vr = detail.getVerifyResult();
            //生成主表自检结果
            if (vr != null && vr == 0) {
                vr0++;
            } else if (vr != null && vr == 1) {
                vr1++;
            }
            //生成主表自检结果
            Integer sv = detail.getSpecialResult();
            if (sv != null && sv == 0) {
                sv0++;
            } else if (sv != null && sv == 1) {
                sv1++;
            }
        }
        //生成主表自检结果
        if (vr0 > 0) {
            detection.setVerifyStatus(0);
        } else if (vr1 == detailList.size()) {
            detection.setVerifyStatus(1);
        } else {
            detection.setVerifyStatus(2);
            detection.setVerifyTime(null);
        }

//        if (vr0 > 0) {
//            detection.setVerifyStatus(0);
//        } else if (vr1 > 0) {
//            detection.setVerifyStatus(1);
//        } else if (vr0 == 0 && vr1 == 0) {
//            detection.setVerifyStatus(2);
//            detection.setVerifyTime(null);
//        }
        //生成主表专检结果

        if (sv0 > 0) {
            detection.setSpecialStatus(0);
        } else if (sv1 == detailList.size()) {
            detection.setSpecialStatus(1);
        } else {
            detection.setSpecialStatus(2);
        }

//        if (sv0 > 0) {
//            detection.setSpecialStatus(0);
//        } else if (sv1 > 0) {
//            detection.setSpecialStatus(1);
//        } else if (sv0 == 0 && sv1 == 0) {
//            detection.setSpecialStatus(2);
//        }

        detectionMapper.updateById(detection);
        //将自检结果更新至工单表
        ProdWorkOrder order = orderService.getEntityByOperationId(detection.getOperationId());
        String barCode = order.getTicketNumber().replace("-", "").replace("_", "").replace(":", "");
        barCode = barCode + detection.getStatus();
        ProdWorkOrder updateOrder = new ProdWorkOrder();
        updateOrder.setId(order.getId());
        updateOrder.setBarcode(barCode);
        updateOrder.setDetectionStatus(detection.getStatus());
        updateOrder.setSelfDetectionStatus(detection.getVerifyStatus());
        //处理专检结果回写订单表
        if (detection.getSpecialStatus() != 2) {
            updateOrder.setSpecialDetectionStatus(detection.getSpecialStatus());
        }
        orderService.updateById(updateOrder);
        return true;
    }

    @Override
    public List<GetColumnVo> getColumns(String barcode) {
        //根据条形码查找工单
        QueryWrapper<ProdWorkOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("barcode", barcode);
        ProdWorkOrder order = orderService.getOne(orderQueryWrapper);
        if (order == null) {
            throw new ServiceException("未查询到工单信息");
        }
        return this.baseMapper.getColumns(order.getBillSchedulCode());
    }

    @Override
    public List<GetColumnVo> getColumnsByParam(QualityReportDataDto dto) {
        //根据条形码查找工单
        QueryWrapper<ProdWorkOrder> orderQueryWrapper = new QueryWrapper<>();
        if (null != dto.getBarcode())
            orderQueryWrapper.eq("barcode", dto.getBarcode());
        if (null != dto.getBillCode())
            orderQueryWrapper.eq("bill_code", dto.getBillCode());
        if (null != dto.getSchedulCode())
            orderQueryWrapper.eq("schedul_code", dto.getSchedulCode());
        List<ProdWorkOrder> orderList = orderService.list(orderQueryWrapper);
        if (orderList.size() == 0) {
            throw new ServiceException("未查询到工单信息");
        }
        ProdWorkOrder order = orderList.get(0);
        return this.baseMapper.getColumns(order.getBillSchedulCode());
    }

    @Override
    public QualityReportDataVo getQualityReportData(QualityReportDataDto dto) {
        QualityReportDataVo reportDataVo = new QualityReportDataVo();
        QualityReportDataOrderVo orderVo = new QualityReportDataOrderVo();
        //根据条形码查找工单
        QueryWrapper<ProdWorkOrder> orderQueryWrapper = new QueryWrapper<>();
        if (null != dto.getBarcode())
            orderQueryWrapper.eq("barcode", dto.getBarcode());
        if (null != dto.getBillCode())
            orderQueryWrapper.eq("bill_code", dto.getBillCode());
        if (null != dto.getSchedulCode())
            orderQueryWrapper.eq("schedul_code", dto.getSchedulCode());
        List<ProdWorkOrder> orderList = orderService.list(orderQueryWrapper);

        if (orderList.size() == 0) {
            throw new ServiceException("未查询到工单信息 -> " + dto.getBarcode());
        }
        if (dto.getColumns() != null && dto.getColumns().size() == 0) {
            throw new ServiceException("未选择检测内容");
        }
        ProdWorkOrder order = orderList.get(0);
        orderVo.setMatName(order.getMatName());

        orderVo.setSchedulCode(order.getSchedulCode());
        orderVo.setBillCode(order.getBillCode());
        orderVo.setMatCode(order.getMatCode());
        orderVo.setSchedulName(order.getSchedulName());
        //查询任务号+工序号下所有工单
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("bscode", order.getBillSchedulCode());
//        if (null != dto.getColumns() && dto.getColumns().size() > 0) {
//            hashMap.put("columnList", dto.getColumns());
//        }
        List<ProdMachineDetectionDetailVo> detectionDetailVos = this.baseMapper.getQualityReportData(hashMap);
        //获取所有的顺序号set集合
        TreeSet<Integer> splitSet = detectionDetailVos.stream().map(e -> Integer.parseInt(e.getOperationSplit())).collect(Collectors.toCollection(TreeSet::new));
        //构建初始化键值对 开始
        QualityReportColumnMetaDataVo nullMetaDataVo = new QualityReportColumnMetaDataVo();
        HashMap<String, QualityReportColumnDataVo> initMap = new HashMap<>();
        for (String column : dto.getColumns()) {
            TreeMap<Integer, Object> initColumnData = new TreeMap<>();
            QualityReportColumnDataVo initColumnDataVo = new QualityReportColumnDataVo();
            for (Integer split : splitSet) {
                initColumnData.put(split, nullMetaDataVo);
            }
            initColumnDataVo.setColumnData(initColumnData);
            initColumnDataVo.setName(column);
            initMap.put(column, initColumnDataVo);
        }
        //构建初始化键值对 完毕
        String eqpCode = order.getEqpCode();
        BaseEquipment equipment = equipmentService.getEntityByCode(order.getLineId(), EqpTypeEnum.MACHINE.getCode(), eqpCode);
        //加工型号
        orderVo.setEqpSystem(equipment.getEqpName());
        //设置完工数量
        orderVo.setQuanity(splitSet.size());
//        //设置物料编码
//        orderVo.setMatCode(detectionDetailVos.get(0).getMatCode());
//        //设置物料工序名 todo

        //将所有数据根据检测项分组
        Date realStart = null;
        Date realEnd = null;
        int pageSize = 10;

        for (ProdMachineDetectionDetailVo detectionDetailVo : detectionDetailVos) {
            //实际加工时间范围
            if (detectionDetailVo.getRealStartTime() != null) {
                if (realStart != null && detectionDetailVo.getRealStartTime().getTime() < realStart.getTime()) {
                    realStart = detectionDetailVo.getRealStartTime();
                } else if (realStart == null) {
                    realStart = detectionDetailVo.getRealStartTime();
                }
            }

            if (detectionDetailVo.getRealEndTime() != null) {
                if (realEnd != null && detectionDetailVo.getRealEndTime().getTime() > realEnd.getTime()) {
                    realEnd = detectionDetailVo.getRealEndTime();
                } else if (realEnd == null) {
                    realEnd = detectionDetailVo.getRealEndTime();
                }
            }
            //实际加工时间范围 结束

            if (detectionDetailVo.getPid() == null) {
                continue;
            }
            if (!dto.getColumns().contains(detectionDetailVo.getName())) {
                continue;
            }

            QualityReportColumnDataVo columnDataVo = initMap.get(detectionDetailVo.getName());
            TreeMap<Integer, Object> columnData = columnDataVo.getColumnData();
//            if (tempMap.containsKey(detectionDetailVo.getName())) {
//                columnDataVo = tempMap.get(detectionDetailVo.getName());
//                columnData = columnDataVo.getColumnData();
//            } else {
//                columnDataVo = new QualityReportColumnDataVo();
//                columnData = new TreeMap<>();
//            }
            columnDataVo.setName(detectionDetailVo.getName());
            columnDataVo.setTargetValue(detectionDetailVo.getTargetValue());
            columnDataVo.setReportData(detectionDetailVo.getReportData());

            QualityReportColumnMetaDataVo metaDataVo = new QualityReportColumnMetaDataVo();
            metaDataVo.setRealValue(detectionDetailVo.getRealValue());
            metaDataVo.setPid(detectionDetailVo.getPid());
//            metaDataVo.setVerifyResult(detectionDetailVo.getVerifyResult());
            metaDataVo.setId(detectionDetailVo.getId());
            metaDataVo.setSpecialResult(detectionDetailVo.getSpecialResult());
            metaDataVo.setSpecialValue(detectionDetailVo.getSpecialValue());
            metaDataVo.setStatus(detectionDetailVo.getStatus());
            metaDataVo.setVerifyValue(detectionDetailVo.getVerifyValue());
            metaDataVo.setVerifyResult(detectionDetailVo.getVerifyResult());
            columnData.put(Integer.parseInt(detectionDetailVo.getOperationSplit()), metaDataVo);

            columnDataVo.setColumnData(columnData);
            initMap.put(detectionDetailVo.getName(), columnDataVo);
        }
        orderVo.setRealStartTime(realStart);
        orderVo.setRealEndTime(realEnd);
        reportDataVo.setOrderVo(orderVo);

        //数据根据每页10个分组
        List<QualityReportColumnDataVo> reportColumnDataVoList = new ArrayList<>();
        for (String s : initMap.keySet()) {
            QualityReportColumnDataVo columnDataVo = initMap.get(s);
            TreeMap<Integer, Object> columnData = columnDataVo.getColumnData();
            columnDataVo.setColumnData(columnData);
            reportColumnDataVoList.add(columnDataVo);
        }

        TreeMap<String, List<QualityReportColumnDataVo>> splitMap = new TreeMap<>();

        for (QualityReportColumnDataVo columnDataVo : reportColumnDataVoList) {
            TreeMap<Integer, Object> treeMap = columnDataVo.getColumnData();

            QualityReportColumnDataVo singleData = null;

            List<QualityReportColumnDataVo> singleList = new ArrayList<>();
            int i = 0;
            for (Integer integer : treeMap.keySet()) {
                if (i == 0 || i % pageSize == 0) {
                    if (i != 0)
                        singleList.add(singleData);
                    singleData = new QualityReportColumnDataVo();
                    singleData.setName(columnDataVo.getName());
                    singleData.setTargetValue(columnDataVo.getTargetValue());
                    singleData.setReportData(columnDataVo.getReportData());
                }
                if (singleData.getColumnData() == null) {
                    TreeMap<Integer, Object> objectTreeMap = new TreeMap<>();
                    singleData.setColumnData(objectTreeMap);
                }
                singleData.getColumnData().put(integer, treeMap.get(integer));
                i++;
            }
            singleList.add(singleData);
            splitMap.put(columnDataVo.getName(), singleList);
        }
        //按分页方式组装数据
        int pageNum = splitMap.get(splitMap.firstKey()).size();
        List<List<QualityReportColumnDataVo>> pageList = new ArrayList<>(pageNum);
        for (int i = 0; i < pageNum; i++) {
            List<QualityReportColumnDataVo> temp = new ArrayList<>();
            for (String s : splitMap.keySet()) {
                temp.add(splitMap.get(s).get(i));
            }
            pageList.add(temp);
        }
        reportDataVo.setPageList(pageList);
        return reportDataVo;
    }

    @Override
    public void exportQualityReportExcel(HttpServletResponse response, QualityReportDataDto dto) {
        XSSFWorkbook wb = new XSSFWorkbook();
        try {
            //获取基本数据
            QualityReportDataVo reportDataVo = this.getQualityReportData(dto);
            QualityReportDataOrderVo orderVo = reportDataVo.getOrderVo();
            List<List<QualityReportColumnDataVo>> pageList = reportDataVo.getPageList();

            XSSFRow row = null;
            XSSFCell cell = null;
            // 建立新的sheet对象（excel的表单） 并设置sheet名字
            XSSFSheet sheet = wb.createSheet("统计报表");
            sheet.setDefaultRowHeightInPoints(20);
            sheet.setDefaultColumnWidth(10);
            int pageNum = 0;
            int excelRowNums = 25;
            for (List<QualityReportColumnDataVo> singlePage : pageList) {
                generateExcel(wb, sheet, pageNum, excelRowNums, pageList.size(), orderVo, singlePage);
                pageNum++;
            }
            // ------------------处理数据end--------------------------------------
            // 输出Excel文件
//            response.reset();
//            OutputStream output = response.getOutputStream();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            wb.write(response.getOutputStream());

//            response.setHeader("Content-disposition", "attachment;filename=" + new String("工序数据记录表".getBytes("gb2312"), "ISO8859-1") + ".xlsx"); //filename =  文件名
//            response.setContentType("application/msexcel");
//            response.setHeader("content-Type", "application/vnd.ms-excel");
//            wb.write(output);
//            output.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    @Override
    public void addDetailColumn(AddDetailColumnDto dto) {
        //查询添加列是否已存在
        List<String> columns = this.getColumns(dto.getBarcode()).stream().map(GetColumnVo::getName).collect(Collectors.toList());
        if (columns != null && columns.size() > 0 && columns.contains(dto.getName()))
            throw new ServiceException("列名" + dto.getName() + "已存在！");
        //根据条形码查找工单
        QueryWrapper<ProdWorkOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("barcode", dto.getBarcode());
        ProdWorkOrder order = orderService.getOne(orderQueryWrapper);
        if (order == null) {
            throw new ServiceException("未查询到条形码工单信息 -> " + dto.getBarcode());
        }
        //查询任务号+工序号下所有工单
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("bscode", order.getBillSchedulCode());
        List<ProdMachineDetectionDetailVo> detectionDetailVos = this.baseMapper.getQualityReportData(hashMap);
        List<Long> pidList = detectionDetailVos.stream().filter(e -> e.getPid() != null).map(ProdMachineDetectionDetailVo::getPid).distinct().sorted().collect(Collectors.toList());
        List<ProdMachineDetectionDetail> saveList = new ArrayList<>();
        for (Long pid : pidList) {
            ProdMachineDetectionDetail detail = new ProdMachineDetectionDetail();
            detail.setPid(pid);
            detail.setName(dto.getName());
            detail.setTargetValue(dto.getTargetValue());
            detail.setReportData(1);
            saveList.add(detail);
        }
        this.saveBatch(saveList);
    }

    @Override
    public int deleteDetailColumn(DeleteDetailColumnDto dto) {
        //根据条形码查找工单
        QueryWrapper<ProdWorkOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("barcode", dto.getBarcode());
        ProdWorkOrder order = orderService.getOne(orderQueryWrapper);
        if (order == null) {
            throw new ServiceException("未查询到条形码工单信息 -> " + dto.getBarcode());
        }
        //查询任务号+工序号下所有工单
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("bscode", order.getBillSchedulCode());
        List<ProdMachineDetectionDetailVo> detectionDetailVos = this.baseMapper.getQualityReportData(hashMap);
        List<Long> pidList = detectionDetailVos.stream().filter(e -> e.getPid() != null).map(ProdMachineDetectionDetailVo::getPid).distinct().sorted().collect(Collectors.toList());
        if (pidList.size() == 0)
            return 0;

        QueryWrapper<ProdMachineDetectionDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("pid", pidList);
        queryWrapper.eq("report_data", 1);
        queryWrapper.eq("name", dto.getName());
        return this.baseMapper.delete(queryWrapper);
    }

    private void generateExcel(XSSFWorkbook wb, XSSFSheet sheet, int pageNum, int excelRowNums, int totalPage, QualityReportDataOrderVo orderVo,
                               List<QualityReportColumnDataVo> singlePage) {
        int rowNum = pageNum * excelRowNums;
        XSSFRow row = null;
        XSSFCell cell = null;
        // 建立新的sheet对象（excel的表单） 并设置sheet名字
        //----------------标题样式--------------------------------
        XSSFCellStyle titleStyle = wb.createCellStyle(); //标题样式
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font ztFont = wb.createFont();
        ztFont.setItalic(false); // 设置字体为斜体字
        //ztFont.setColor(Font.COLOR_NORMAL);            // 将字体设置为“红色”
        ztFont.setFontHeightInPoints((short) 16); // 将字体大小设置为18px
        ztFont.setFontName("宋体"); // 将“宋体”字体应用到当前单元格上
        ztFont.setBold(true); //加粗
        //ztFont.setUnderline(Font.U_DOUBLE);// 添加（Font.U_SINGLE单条下划线/Font.U_DOUBLE双条下划线）
        //ztFont.setStrikeout(true);// 是否添加删除线
        titleStyle.setFont(ztFont);
        //------------------------------------------------------------
        //----------------二级标题格样式----------------------------------
        XSSFCellStyle titleStyle2 = wb.createCellStyle(); //表格样式
        titleStyle2.setAlignment(HorizontalAlignment.CENTER);
        titleStyle2.setVerticalAlignment(VerticalAlignment.CENTER);

        titleStyle2.setBorderBottom(BorderStyle.THIN);//下边框
        titleStyle2.setBorderLeft(BorderStyle.THIN);//左边框
        titleStyle2.setBorderRight(BorderStyle.THIN);//右边框
        titleStyle2.setBorderTop(BorderStyle.THIN);//上边框

        Font ztFont2 = wb.createFont();
        ztFont2.setItalic(false); // 设置字体为斜体字
        ztFont2.setColor(Font.COLOR_NORMAL); // 将字体设置为“红色”
        ztFont2.setFontHeightInPoints((short) 10); // 将字体大小设置为18px
        ztFont2.setFontName("宋体"); // 字体应用到当前单元格上
        ztFont2.setBold(true); //加粗
        //ztFont.setUnderline(Font.U_DOUBLE);// 添加（Font.U_SINGLE单条下划线/Font.U_DOUBLE双条下划线）
        //ztFont.setStrikeout(true);// 是否添加删除线
        titleStyle2.setFont(ztFont2);
        titleStyle2.setWrapText(true);//设置自动换行
        //----------------------------------------------------------
        //----------------单元格样式-----------------------------------
        XSSFCellStyle cellStyle = wb.createCellStyle(); //表格样式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        cellStyle.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyle.setBorderRight(BorderStyle.THIN);//右边框
        cellStyle.setBorderTop(BorderStyle.THIN);//上边框

        Font cellFont = wb.createFont();
        cellFont.setItalic(false); // 设置字体为斜体字
        cellFont.setColor(Font.COLOR_NORMAL); // 将字体设置为“红色”
        cellFont.setFontHeightInPoints((short) 9); // 将字体大小设置为18px
        cellFont.setFontName("宋体"); // 字体应用到当前单元格上
        cellStyle.setFont(cellFont);
        cellStyle.setWrapText(true);//设置自动换行
        //----------------------------------------------------------
        //----------------加工时间 单元格样式-----------------------------------
        XSSFCellStyle cellStyle2 = wb.createCellStyle(); //表格样式
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);

        cellStyle2.setBorderBottom(BorderStyle.THIN);//下边框
        cellStyle2.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyle2.setBorderRight(BorderStyle.THIN);//右边框
        cellStyle2.setBorderTop(BorderStyle.THIN);//上边框

        Font cellFont2 = wb.createFont();
        cellFont2.setItalic(false); // 设置字体为斜体字
        cellFont2.setColor(Font.COLOR_NORMAL); // 将字体设置为“红色”
        cellFont2.setFontHeightInPoints((short) 8); // 将字体大小设置为18px
        cellFont2.setFontName("宋体"); // 字体应用到当前单元格上
        cellStyle2.setFont(cellFont2);
        cellStyle2.setWrapText(true);//设置自动换行

        // ----------------------创建工单数据 0-1 行 ----------------------------
        // 在sheet里创建第一行，参数为行索引(excel的行)，可以是0～65535之间的任何一个
        row = sheet.createRow(rowNum);
        // 创建单元格（excel的单元格，参数为列索引，可以是0～255之间的任何一个
        cell = row.createCell(0);
        // 设置单元格内容
        String titleName = "工序数据记录表";
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum + 1, 0, 1));
        cell.setCellValue(titleName);
        cell.setCellStyle(titleStyle2);

        cell = row.createCell(1);
        cell.setCellStyle(cellStyle);
        // 合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列

        //-------------------添加工单信息-----------------------
        cell = row.createCell(2);
        cell.setCellValue("物料名称：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(3);
        cell.setCellValue(orderVo.getMatName());
        cell.setCellStyle(cellStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 3, 4));

        cell = row.createCell(4);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(5);
        cell.setCellValue("任务号：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(6);
        cell.setCellValue(orderVo.getBillCode());
        cell.setCellStyle(cellStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 6, 7));

        cell = row.createCell(7);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(8);
        cell.setCellValue("工序号：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(9);
        cell.setCellValue(orderVo.getSchedulCode());
        cell.setCellStyle(cellStyle);

        cell = row.createCell(10);
        cell.setCellValue("工序名：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(11);
        cell.setCellValue(orderVo.getSchedulName());
        cell.setCellStyle(cellStyle);

        //*****************************************************第二行
        row = sheet.createRow(++rowNum);

        cell = row.createCell(0);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(1);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(2);
        cell.setCellValue("物料编码：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(3);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 3, 4));
        cell.setCellValue(orderVo.getMatCode());
        cell.setCellStyle(cellStyle);

        cell = row.createCell(4);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(5);
        cell.setCellValue("加工设备：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(6);
        cell.setCellValue(orderVo.getEqpSystem());
        cell.setCellStyle(cellStyle2);
//
        cell = row.createCell(7);
        cell.setCellValue("生产数量：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(8);
        cell.setCellValue(orderVo.getQuanity().toString());
        cell.setCellStyle(cellStyle);

        cell = row.createCell(9);
        cell.setCellValue("加工时间：");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(10);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 10, 11));
        cell.setCellValue(sdf.format(orderVo.getRealStartTime()) + " ~ " + sdf.format(orderVo.getRealEndTime()));
        cell.setCellStyle(cellStyle2);

        cell = row.createCell(11);
        cell.setCellStyle(cellStyle);

        // ----------------------创建工单数据 2 行 ----------------------------
        int columnNum = 0;
        row = sheet.createRow(++rowNum);
        cell = row.createCell(columnNum);
        cell.setCellValue("测量内容");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(++columnNum);
        cell.setCellValue("目标值");
        cell.setCellStyle(cellStyle);
        //处理每页实测值表头
        TreeMap<Integer, Object> tempColumnData = singlePage.get(0).getColumnData();
        for (Integer integer : tempColumnData.keySet()) {
            cell = row.createCell(++columnNum);
            cell.setCellValue("实测值" + integer);
            cell.setCellStyle(cellStyle);
        }
        //填补记录表格的样式
        if (columnNum < 11) {
            for (int k = columnNum; k < 11; k++) {
                cell = row.createCell(++columnNum);
                cell.setCellStyle(cellStyle);
            }
        }
        for (QualityReportColumnDataVo columnDataVo : singlePage) {
            //按行生成值
            row = sheet.createRow(++rowNum);
            columnNum = 0;
            cell = row.createCell(columnNum);
            cell.setCellValue(columnDataVo.getName());
            cell.setCellStyle(cellStyle);

            cell = row.createCell(++columnNum);
            cell.setCellValue(columnDataVo.getTargetValue());
            cell.setCellStyle(cellStyle);
            //循环填充工件实测值
            TreeMap<Integer, Object> columnData = columnDataVo.getColumnData();
            for (Integer integer : columnData.keySet()) {
                QualityReportColumnMetaDataVo meta = (QualityReportColumnMetaDataVo) columnData.get(integer);
                cell = row.createCell(++columnNum);
                if (StringUtils.isNotBlank(meta.getSpecialValue()))
                    cell.setCellValue(meta.getSpecialValue());
                else if (StringUtils.isNotBlank(meta.getVerifyValue()))
                    cell.setCellValue(meta.getVerifyValue());
                else if (StringUtils.isNotBlank(meta.getRealValue()))
                    cell.setCellValue(meta.getRealValue());
                cell.setCellStyle(cellStyle);
            }
            //填补记录表格的样式
            if (columnNum < 11) {
                for (int k = columnNum; k < 11; k++) {
                    cell = row.createCell(++columnNum);
                    cell.setCellStyle(cellStyle);
                }
            }
        }
        int nullRowStart = rowNum + 1;
        //添加 控制结果
        rowNum = excelRowNums * (pageNum + 1);
        row = sheet.createRow(--rowNum);
        cell = row.createCell(0);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 11));
        int page = pageNum + 1;
        cell.setCellValue("第 " + page + " 页，共 " + totalPage + " 页");
        cell.setCellStyle(cellStyle);

        for (int s = 1; s < 12; s++) {
            cell = row.createCell(s);
            cell.setCellStyle(cellStyle);
        }

        //****************************************************************
        row = sheet.createRow(--rowNum);
        cell = row.createCell(0);
        cell.setCellValue("操作者");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(1);
        cell.setCellValue("");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(2);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 2, 3));
        cell.setCellValue("日期");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(3);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(4);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 4, 5));
        cell.setCellValue("");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(5);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(6);
        cell.setCellValue("检验");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(7);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 7, 8));
        cell.setCellValue("");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(8);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(9);
        cell.setCellValue("日期");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(10);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 10, 11));
        cell.setCellValue("");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(11);
        cell.setCellStyle(cellStyle);

        //****************************************************************
        row = sheet.createRow(--rowNum);
        cell = row.createCell(0);
        cell.setCellValue("合格数");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(1);
        cell.setCellValue("");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(2);
        CellRangeAddress cellAddresses = new CellRangeAddress(rowNum, rowNum, 2, 3);
        sheet.addMergedRegion(cellAddresses);
        cell.setCellValue("返修合格数");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(3);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(4);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 4, 5));
        cell.setCellValue("");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(5);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(6);
        cell.setCellValue("合格率");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(7);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 7, 8));
        cell.setCellValue("");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(8);
        cell.setCellStyle(cellStyle);

        cell = row.createCell(9);
        cell.setCellValue("报废数");
        cell.setCellStyle(cellStyle);

        cell = row.createCell(10);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 10, 11));
        cell.setCellValue("");

        cell = row.createCell(11);
        cell.setCellStyle(cellStyle);

        //****************************************************************
        row = sheet.createRow(--rowNum);
        cell = row.createCell(0);
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 11));
        cell.setCellValue("控制结果");
        cell.setCellStyle(cellStyle);

        for (int s = 1; s < 12; s++) {
            cell = row.createCell(s);
            cell.setCellStyle(cellStyle);
        }
        //空白区域添加样式
        int nullRowEnd = rowNum;
        for (int k = nullRowStart; k < nullRowEnd; k++) {
            row = sheet.createRow(k);
            for (int s = 0; s < 12; s++) {
                cell = row.createCell(s);
                cell.setCellStyle(cellStyle);
            }
        }
/*        //解决合并单元格遗留空白边框问题
        CellRangeAddress cellAddresses = new CellRangeAddress(0, 100, 0, 100);
        RegionUtil.setBorderRight(BorderStyle.THIN, cellAddresses, sheet);*/
        // ------------------处理数据end--------------------------------------
        // 输出Excel文件
    }
}
