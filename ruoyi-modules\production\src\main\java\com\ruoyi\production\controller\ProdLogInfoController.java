package com.ruoyi.production.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDto;
import com.ruoyi.production.service.IProdOperationTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("logInfo")
@Api(value = "生产日志", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class ProdLogInfoController extends BaseModuleController{

    @Autowired
    private IProdOperationTraceService operationTraceService;

    @ApiOperation("分页查询记录列表")
    @PostMapping("listForPage")
    public AjaxResult listForPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdOperationTraceDto operationTraceDto) {

        return AjaxResult.success(operationTraceService.logInfoListForPage(operationTraceDto, pageNum, pageSize));
    }


}
