package com.ruoyi.production.domain.dto.operation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "调度出、入库参数模型", description = "调度出、入库参数模型")
public class Room2LineDto {
    @ApiModelProperty(value = "工单id")
    private String operationId;
    @ApiModelProperty(value = "托盘位置")
    private String trayPosition;

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public String getTrayPosition() {
        return trayPosition;
    }

    public void setTrayPosition(String trayPosition) {
        this.trayPosition = trayPosition;
    }
}
