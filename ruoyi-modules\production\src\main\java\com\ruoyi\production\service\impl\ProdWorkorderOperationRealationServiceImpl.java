package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.ProdWorkorderOperationRealation;
import com.ruoyi.production.mapper.ProdWorkOrderMapper;
import com.ruoyi.production.mapper.ProdWorkorderOperationRealationMapper;
import com.ruoyi.production.service.IProdWorkorderOperationRealationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-05-30
 */
@Service
public class ProdWorkorderOperationRealationServiceImpl extends ServiceImpl<ProdWorkorderOperationRealationMapper, ProdWorkorderOperationRealation> implements IProdWorkorderOperationRealationService {

    @Autowired
    private ProdWorkOrderMapper orderMapper;

    @Override
    public List<ProdWorkOrder> getWorkOrderListByOperationId(Long OperationId) {
        QueryWrapper<ProdWorkorderOperationRealation> queryWrapper = new QueryWrapper();
        queryWrapper.eq("operation_id", OperationId);
        List<ProdWorkorderOperationRealation> realationList = this.getBaseMapper().selectList(queryWrapper);
        if (realationList.size() < 1)
            return null;
        List<String> orderList = realationList.stream().map(ProdWorkorderOperationRealation::getWorkOrderId).collect(Collectors.toList());
        QueryWrapper<ProdWorkOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.in("opreation_id", orderList);
        return orderMapper.selectList(orderQueryWrapper);
    }

    @Override
    public void removeRealtionByWorkOrder(ProdWorkOrder prodWorkOrder) {
        QueryWrapper<ProdWorkorderOperationRealation> queryWrapper = new QueryWrapper();
        queryWrapper.eq("work_order_id", prodWorkOrder.getOperationId());
        this.getBaseMapper().delete(queryWrapper);

    }

    @Override
    public void removeRealtionByWorkOrderList(List<ProdWorkOrder> prodWorkOrderList) {
        List<String> OrderList = prodWorkOrderList.stream()
                .filter(order -> StringUtils.isNotBlank(order.getOperationId()))
                .map(ProdWorkOrder::getOperationId).collect(Collectors.toList());
        QueryWrapper<ProdWorkorderOperationRealation> queryWrapper = new QueryWrapper();
        queryWrapper.in("work_order_id", OrderList);
        if (OrderList.size() > 0)
            this.getBaseMapper().delete(queryWrapper);
    }
}
