<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.SysPlatformApiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.SysPlatformApi">
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
        <result column="remark" property="remark" />
        <result column="provider" property="provider" />
        <result column="url" property="url" />
        <result column="api_method" property="apiMethod" />
        <result column="api_code" property="apiCode" />
        <result column="description" property="description" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_by,
        create_time,
        update_by,
        update_time,
        id,
        deleted,
        remark, provider, url, api_method, api_code, desc
    </sql>

</mapper>
