<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.BaseEquipmentMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.api.domain.BaseEquipment">
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
        <result column="aip_id" property="aipId"/>
        <result column="eqp_code" property="eqpCode"/>
        <result column="eqp_name" property="eqpName"/>
        <result column="type" property="type"/>
        <result column="line_id" property="lineId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_by
                ,
        create_time,
        update_by,
        update_time,
        id,
        deleted,
        remark, aip_id, eqp_code, eqp_name, type, line_id
    </sql>

    <select id="listByCondition" resultType="com.ruoyi.production.api.domain.BaseEquipment">
        select eqp.* from base_equipment eqp
        <where>
            <if test="params.lineId != null">
                and eqp.line_id = #{params.lineId}
            </if>
            <if test="params.type != null">
                and eqp.type = #{params.type}
            </if>
            <if test="params.eqpCode != null">
                and eqp.eqp_code = #{params.eqpCode}
            </if>
            <if test="params.processing != null ">
                and eqp.processing = #{params.processing}
            </if>
            <if test="params.autoProcess != null">
                and eqp.auto_process = #{params.autoProcess}
            </if>
            <if test="params.status != null">
                and eqp.status = #{params.status}
            </if>
        </where>
        order by eqp.id asc
    </select>
</mapper>
