package com.ruoyi.production.domain.dto.operation;

import com.ruoyi.production.domain.vo.TrayInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "调度出、入库参数模型", description = "调度出、入库参数模型")
public class DockxBenchDto {
    @ApiModelProperty(value = "工单id")
    private String operationId;
    @ApiModelProperty(value = "托盘位置")
    private String trayPosition;
    @ApiModelProperty(value = "内容类型code")
    private Integer fillerType;

    private TrayInfoVo trayInfoVo;

    public TrayInfoVo getTrayInfoVo() {
        return trayInfoVo;
    }

    public void setTrayInfoVo(TrayInfoVo trayInfoVo) {
        this.trayInfoVo = trayInfoVo;
    }

    public Integer getFillerType() {
        return fillerType;
    }

    public void setFillerType(Integer fillerType) {
        this.fillerType = fillerType;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public String getTrayPosition() {
        return trayPosition;
    }

    public void setTrayPosition(String trayPosition) {
        this.trayPosition = trayPosition;
    }
}
