<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProdWorkOrderMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.ProdWorkOrder">
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
        <result column="line_id" property="lineId"/>
        <result column="line_code" property="lineCode"/>
        <result column="eqp_id" property="eqpId"/>
        <result column="eqp_code" property="eqpCode"/>
        <result column="bill_code" property="billCode"/>
        <result column="schedul_code" property="schedulCode"/>
        <result column="mat_code" property="matCode"/>
        <result column="mat_id" property="matId"/>
        <result column="status" property="status"/>
        <result column="BZ1" property="bz1"/>
        <result column="operation_split" property="operationSplit"/>
        <result column="schedul_start_date" property="schedulStartDate"/>
        <result column="schedul_end_date" property="schedulEndDate"/>
        <result column="operation_id" property="operationId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_by
        ,
        create_time,
        update_by,
        update_time,
        id,
        deleted,
        remark, line_id, line_code, eqp_id, eqp_code, bill_code, schedul_code, mat_code, mat_id, status, BZ1, BZ2, BZ3, BZ4, BZ5, BZ6, workplace_group, workplace_name, material_number, operation_number, order_number, operation_split, schedul_start_date, schedul_end_date, component_number, batch_number, requirement_quantity, workpiece_id, storage_bin, storage_location, target_quantity, postion_number, workplace_id, operation_id, component_id
    </sql>

    <select id="listByCondition" parameterType="map" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select
        b.eqpGroup,o.*,'1' as num,DATE_FORMAT(o.schedul_start_date,'%Y-%m-%d') as
        schedulStartNyr,DATE_FORMAT(o.schedul_end_date,'%Y-%m-%d') as schedulEndNyr
        FROM
        prod_work_order o
        left join (select GROUP_CONCAT(eqp_code) as eqpGroup,bill_schedul_code from prod_order_mach_relation where
        enable=1 group by
        bill_schedul_code) b
        on b.bill_schedul_code= o.bill_schedul_code
        <where>
            <!--            <if test="params.lineId != null">-->
            <!--                and o.line_id = #{params.lineId}-->
            <!--            </if>-->
            <if test="params.lineId != null">
                and o.line_id = #{params.lineId}
            </if>
            <if test="params.operationId != null">
                and o.operation_id = #{params.operationId}
            </if>
            <if test="params.schedulStartDate != null">
                and DATE_FORMAT(o.schedul_start_date,'%Y-%m-%d') &gt;=
                DATE_FORMAT(#{params.schedulStartDate},'%Y-%m-%d')
            </if>
            <!--            <if test="params.schedulStartDate != null">-->
            <!--                and DATE_FORMAT(o.schedul_start_date,'%Y-%m-%d') &gt;=-->
            <!--                DATE_FORMAT(#{params.schedulStartDate},'%Y-%m-%d')-->
            <!--            </if>-->
            <if test="params.schedulEndDate != null">
                and DATE_FORMAT(o.schedul_start_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{params.schedulEndDate},'%Y-%m-%d')
            </if>
            <if test="params.billCode != null">
                and o.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and o.schedul_code = #{params.schedulCode}
            </if>
            <if test="params.status != null">
                and o.status = #{params.status}
            </if>
            <if test="params.eqpCode != null">
                and o.eqp_code = #{params.eqpCode}
            </if>
            <if test="params.distributed != null">
                and o.distributed = #{params.distributed}
            </if>
            <if test="params.statusList != null and params.statusList.size()>0">
                AND o.status IN
                <foreach item="item" index="index" collection="params.statusList" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.realEndDateBegin != null">
                and DATE_FORMAT(o.real_end_time,'%Y-%m-%d') &gt;=
                DATE_FORMAT(#{params.realEndDateBegin},'%Y-%m-%d')
            </if>
            <if test="params.realEndDateStop != null">
                and DATE_FORMAT(o.real_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{params.realEndDateStop},'%Y-%m-%d')
            </if>
        </where>
        order by o.status asc,o.schedul_start_date asc
        <!--        order by FIELD(o.status,5,6,0,1,2,3,4),o.schedul_start_date asc-->
    </select>

    <select id="getTotalList" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select
        b.eqpGroup,a.allTotal,a.allDoneNum,a.allRemainNum,count(*) as total,o.*,
        count(case when status >= 5 then 1 end) as doneNum,
        IFNULL(ROUND(ROUND(sum((UNIX_TIMESTAMP(real_end_time)-UNIX_TIMESTAMP(real_start_time))))/3600,1),0) as
        allDoneTimeCost,
        IFNULL(ROUND(ROUND(sum((UNIX_TIMESTAMP(real_end_time)-UNIX_TIMESTAMP(real_start_time)))/count(case when status
        >=5 then 1 end))/60,1),0) as aveDoneTimeCost,
        (count(*) - count(case when status >= 5 then 1 end)) as remainNum,
        IFNULL(ROUND(ROUND(sum((UNIX_TIMESTAMP(real_end_time)-UNIX_TIMESTAMP(real_start_time)))/count(case when status
        >=5 then 1 end))*(count(*) - count(case when status >= 5 then 1 end))/3600,1),0) as remainTime
        ,min(schedul_start_date) as orderStartDate
        ,max(schedul_end_date) as orderEndDate
        FROM
        prod_work_order o
        LEFT JOIN (select count(*) as allTotal,count(case when status >= 5 then 1 end) as
        allDoneNum,bill_schedul_code,(count(*) - count(case when status >= 5 then 1 end)) as allRemainNum
        from prod_work_order
        group by bill_schedul_code) a on a.bill_schedul_code=o.bill_schedul_code
        left join (select GROUP_CONCAT(eqp_code) as eqpGroup,bill_schedul_code from prod_order_mach_relation
        <where>
            enable=1
            <if test="params.lineId != null">
                and line_id = #{params.lineId}
            </if>
        </where>
        group by bill_schedul_code) b
        on b.bill_schedul_code= o.bill_schedul_code
        <where>
            <if test="params.operationId != null">
                and o.operation_id = #{params.operation_id}
            </if>
            <if test="params.schedulStartDate != null">
                and o.schedul_start_date &gt;= #{params.schedulStartDate}
            </if>
            <if test="params.schedulEndDate != null">
                and o.schedul_start_date &lt;= #{params.schedulEndDate}
            </if>
            <if test="params.billCode != null">
                and o.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and o.schedul_code = #{params.schedulCode}
            </if>
            <if test="params.eqpCode != null">
                and o.eqp_code = #{params.eqpCode}
            </if>
            <if test="params.realEqp != null">
                and o.real_eqp = #{params.realEqp}
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
        GROUP BY o.bill_schedul_code
        order by o.schedul_start_date desc
    </select>


    <select id="getProcessTraceList" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select c.eqpGroup,count(case when status >= 5 then 1 end) as doneNum,
        IFNULL(ROUND(ROUND(sum((UNIX_TIMESTAMP(real_end_time)-UNIX_TIMESTAMP(real_start_time))))/3600,1),0) as
        allDoneTimeCost,
        IFNULL(ROUND(ROUND(sum((UNIX_TIMESTAMP(real_end_time)-UNIX_TIMESTAMP(real_start_time)))/count(case when status
        >=5 then 1 end))/60,1),0) as aveDoneTimeCost,(count(*) - count(case when status >= 5 then 1 end)) as remainNum,
        o.*
        from prod_work_order o
        inner join
        (select a.bill_schedul_code,b.eqpGroup from
        (select bill_schedul_code,(count(*) - count(case when status >= 5 then 1 end)) as remainNum
        from prod_work_order group by bill_schedul_code)a
        inner join (select GROUP_CONCAT(eqp_code) as eqpGroup,bill_schedul_code from prod_order_mach_relation where
        enable=1 group by
        bill_schedul_code ) b on b.bill_schedul_code= a.bill_schedul_code
        where a.remainNum >0)c on c.bill_schedul_code=o.bill_schedul_code
        <where>
            <if test="params.operationId != null">
                and o.operation_id = #{params.operation_id}
            </if>
            <if test="params.schedulStartDate != null">
                and o.schedul_start_date &gt;= #{params.schedulStartDate}
            </if>
            <if test="params.schedulEndDate != null">
                and o.schedul_start_date &lt;= #{params.schedulEndDate}
            </if>
            <if test="params.billCode != null">
                and o.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and o.schedul_code = #{params.schedulCode}
            </if>
            <if test="params.eqpCode != null">
                and o.eqp_code = #{params.eqpCode}
            </if>
            <if test="params.realEqp != null">
                and o.real_eqp = #{params.realEqp}
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
        group by o.real_eqp
        order by o.schedul_start_date desc
    </select>

    <select id="listForPage" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select pwo.*,c.machPreparedNums from (SELECT
        SUBSTRING_INDEX(
        group_concat(
        id
        ORDER BY
        schedul_start_date ASC
        ),',',1) AS tempid
        FROM
        (
        (select
        o.*
        FROM
        prod_work_order o
        <where>
            <if test="params.dataScrop == 1">
                and DATE(NOW()) > DATE(schedul_start_date)
                and status in(0,1)
            </if>
            <if test="params.dataScrop != 1">
                and DATE(NOW()) = DATE(schedul_start_date)
                and o.status = 0
            </if>
            <if test="params.operationId != null">
                and o.operation_id = #{params.operationId}
            </if>
            <if test="params.schedulStartDate != null and params.dataScrop != 1">
                and o.schedul_start_date &gt;= #{params.schedulStartDate}
            </if>
            <if test="params.schedulStartDate != null and params.dataScrop != 1">
                and o.schedul_start_date &gt;= #{params.schedulStartDate}
            </if>
            <if test="params.schedulEndDate != null and params.dataScrop != 1">
                and o.schedul_start_date &lt;= #{params.schedulEndDate}
            </if>
            <if test="params.billCode != null">
                and o.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and o.schedul_code = #{params.schedulCode}
            </if>
            <if test="params.eqpCode != null">
                and o.eqp_code = #{params.eqpCode}
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
        ))as a
        GROUP BY
        eqp_code)b join prod_work_order pwo on pwo.id=b.tempid
        left join (select count(*) as machPreparedNums,eqp_code
        from prod_work_order wo
        INNER JOIN base_line_storage sto on sto.operation_id=wo.operation_id
        group by wo.eqp_code)c
        on c.eqp_code=pwo.eqp_code
    </select>

    <select id="pageList" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select
        o.*
        FROM
        prod_work_order o
        <where>
            <if test="params.operationId != null">
                and o.operation_id = #{params.operationId}
            </if>
            <if test="params.billCode != null">
                and o.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and o.schedul_code = #{params.schedulCode}
            </if>
            <if test="params.ticketNumber != null">
                and o.ticket_number like concat('%', #{params.ticketNumber}, '%')
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
    </select>

    <select id="listForPageToDock" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select e.* from (select pwo.*,d.eqpGroup from (SELECT
        SUBSTRING_INDEX(
        group_concat(
        id
        ORDER BY
        schedul_start_date ASC
        ),',',1) AS tempid
        FROM
        (
        (select
        o.*
        FROM
        prod_work_order o
        <where>
            DATE(NOW()) = DATE(schedul_start_date)
            and o.status = 0
            <if test="params.operationId != null">
                and o.operation_id = #{params.operationId}
            </if>
            <if test="params.schedulStartDate != null">
                and o.schedul_start_date &gt;= #{params.schedulStartDate}
            </if>
            <if test="params.schedulStartDate != null">
                and o.schedul_start_date &gt;= #{params.schedulStartDate}
            </if>
            <if test="params.schedulEndDate != null">
                and o.schedul_start_date &lt;= #{params.schedulEndDate}
            </if>
            <if test="params.billCode != null">
                and o.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and o.schedul_code = #{params.schedulCode}
            </if>
            <if test="params.eqpCode != null">
                and o.eqp_code = #{params.eqpCode}
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
        ))as a
        GROUP BY bill_schedul_code)b join prod_work_order pwo on pwo.id=b.tempid
        left join (select GROUP_CONCAT(eqp_code) as eqpGroup,bill_schedul_code from prod_order_mach_relation where
        enable=1 group by
        bill_schedul_code) d
        on d.bill_schedul_code= pwo.bill_schedul_code)e
        where e.eqpGroup is not null
    </select>

    <select id="getEntityByParam" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select b.eqpGroup,o.*
        from prod_work_order o
        left join (select GROUP_CONCAT(eqp_code) as eqpGroup, bill_schedul_code from prod_order_mach_relation where
        enable=1 group by
        bill_schedul_code) b
        on b.bill_schedul_code= o.bill_schedul_code
        <where>
            <if test="params.operationId != null">
                and o.operation_id = #{params.operationId}
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
    </select>

    <select id="getDetectionReportData" resultType="com.ruoyi.production.domain.vo.detection.DetectionReportVo">
        select * from (select bill_code,schedul_code,mat_name,o1.bill_schedul_code,o2.totalNum,count(*) as
        doneNum,count( case when detection_status =1 then 1 end) as qualifiedNum
        ,count(self_detection_status) as verifyQualifiedNum,count(case when special_detection_status =1 then 1 end) as
        specialQualifiedNum
        ,ROUND((count(case when detection_status =1 then 1 end)/count(*))*100,1) as qualifiedRate,ROUND((count(case when
        special_detection_status =1 then 1 end)/count(*))*100,1) as specialQualifiedRate
        from prod_work_order o1
        left join (select count(*) as totalNum,bill_schedul_code from prod_work_order group by bill_schedul_code)o2 on
        o2.bill_schedul_code=o1.bill_schedul_code
        <where>
            status>=5
            <if test="params.realEndDateBegin != null">
                and DATE_FORMAT(real_end_time,'%Y-%m-%d') &gt;=
                DATE_FORMAT(#{params.realEndDateBegin},'%Y-%m-%d')
            </if>
            <if test="params.realEndDateStop != null">
                and DATE_FORMAT(real_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{params.realEndDateStop},'%Y-%m-%d')
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o1.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
        group by bill_schedul_code order by real_end_time desc)d where d.totalNum = d.doneNum
    </select>

    <select id="listForPageDetectionReport" resultType="com.ruoyi.production.domain.vo.detection.DetectionReportVo">
        select * from (select bill_code,schedul_code,mat_name,o1.bill_schedul_code,o2.totalNum,count(*) as
        doneNum,count( case when detection_status =1 then 1 end) as qualifiedNum
        ,count(self_detection_status) as verifyQualifiedNum,count(case when special_detection_status =1 then 1 end) as
        specialQualifiedNum
        ,ROUND((count(case when detection_status =1 then 1 end)/count(*))*100,1) as qualifiedRate,ROUND((count(case when
        special_detection_status =1 then 1 end)/count(*))*100,1) as specialQualifiedRate
        from prod_work_order o1
        left join (select count(*) as totalNum,bill_schedul_code from prod_work_order group by bill_schedul_code)o2 on
        o2.bill_schedul_code=o1.bill_schedul_code
        <where>
            status>=5
            <if test="params.realEndDateBegin != null">
                and DATE_FORMAT(real_end_time,'%Y-%m-%d') &gt;=
                DATE_FORMAT(#{params.realEndDateBegin},'%Y-%m-%d')
            </if>
            <if test="params.realEndDateStop != null">
                and DATE_FORMAT(real_end_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{params.realEndDateStop},'%Y-%m-%d')
            </if>
            <!--            <if test="params.lineId != null">-->
            <!--                and o1.line_id = #{params.lineId}-->
            <!--            </if>-->
        </where>
        group by bill_schedul_code order by real_end_time desc)d where d.totalNum = d.doneNum
    </select>

    <select id="apiGetWorkOrderList" resultType="com.ruoyi.production.domain.vo.ProdWorkOrderVo">
        select o.*,
        IFNULL(ROUND(ROUND((UNIX_TIMESTAMP(o.real_end_time)-UNIX_TIMESTAMP(o.real_start_time)))/60,1),0) as
        realCost
        from prod_work_order o
        <where>
            <if test="params.operationId != null">
                and o.operation_id = #{params.operationId}
            </if>
            <if test="params.schedulStartDate != null">
                and o.schedul_start_date &gt;= #{params.schedulStartDate}
            </if>
            <if test="params.schedulEndDate != null">
                and o.schedul_start_date &lt;= #{params.schedulEndDate}
            </if>
            <if test="params.billCode != null">
                and o.bill_code = #{params.billCode}
            </if>
            <if test="params.schedulCode != null">
                and o.schedul_code = #{params.schedulCode}
            </if>
            <if test="params.eqpCode != null">
                and o.eqp_code = #{params.eqpCode}
            </if>
            <if test="params.realEqp != null">
                and o.real_eqp = #{params.realEqp}
            </if>
            <if test="params.realStartTime != null">
                and o.real_start_time &gt;= #{params.realStartTime}
            </if>
            <if test="params.realEndTime != null">
                and o.real_end_time &lt;= #{params.realEndTime}
            </if>
            <if test="params.lineId != null">
                and o.line_id = #{params.lineId}
            </if>
            <if test="params.status != null">
                and o.status = #{params.status}
            </if>
        </where>
        order by o.schedul_start_date desc
    </select>
</mapper>
