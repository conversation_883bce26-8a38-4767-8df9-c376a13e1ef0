<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.message.mapper.MessageNewsMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.message.api.domain.MessageNews">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="creater_id" property="createrId"/>
        <result column="receiver_id" property="receiverId"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
        <result column="source" property="source"/>
        <result column="target_url" property="targetUrl"/>
        <result column="line_id" property="lineId"/>
        <result column="button_group_id" property="buttonGroupId"/>
        <result column="is_deal" property="isDeal"/>
        <result column="is_read" property="isRead"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        remark,
        deleted,
        create_by, create_time, update_by, update_time, creater_id, receiver_id, title, content, type, source, target_url, line_id, button_group_id, is_deal, is_read,opreation_id
    </sql>
    <select id="listByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from message_news
        <where>
            <if test="params.isRead != null and params.isRead != ''">
                and is_read = #{params.isRead}
            </if>
            <if test="params.lineId != null and params.lineId != ''">
                and line_id = #{params.lineId}
            </if>
        </where>
        order by id asc
    </select>

</mapper>
