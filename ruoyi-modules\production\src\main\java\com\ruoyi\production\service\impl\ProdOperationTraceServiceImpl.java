package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDto;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo;
import com.ruoyi.production.mapper.ProdOperationTraceMapper;
import com.ruoyi.production.service.IProdOperationTraceDetailService;
import com.ruoyi.production.service.IProdOperationTraceService;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.production.utils.RabbitMsgUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-05-07
 */
@Service
public class ProdOperationTraceServiceImpl extends ServiceImpl<ProdOperationTraceMapper, ProdOperationTrace> implements IProdOperationTraceService {

    @Autowired
    IProdOperationTraceDetailService detailService;
    @Autowired
    ISysPlatformApiService apiService;
    @Autowired
    private RabbitMsgUtils rabbitMsgUtils;

    @Override
    public List<ProdOperationTraceVo> listByCondition(ProdOperationTrace ProdOperationTrace) {
        return this.getBaseMapper().listByCondition(ProdOperationTrace);
    }

    @Override
    public IPage<ProdOperationTraceVo> listForPage(ProdOperationTraceDto operationTrace, Integer current, Integer pageSiz) {
        Page<ProdOperationTraceVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);
        return this.getBaseMapper().listForPage(page, operationTrace);
    }

    @Override
    public IPage<ProdOperationTraceVo> logInfoListForPage(ProdOperationTraceDto operationTrace, Integer current, Integer pageSiz) {
        Page<ProdOperationTraceVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);
        return this.getBaseMapper().logInfoListForPage(page, operationTrace);
    }

    @Override
    public void saveOrUpdateOperationTrace(ProdOperationTrace operationTrace) {
        if ("done".equals(operationTrace.getStatus()) && null == operationTrace.getRemark()) {
            operationTrace.setRemark("完成");
        }
        this.saveOrUpdate(operationTrace);
        detailService.saveDetailByOperation(operationTrace);
        //2不相同，设置缓存，生产消息
//        SysPlatformApi api = apiService.getEntityByCode("dcs_getOperationTraceList");
//        WebSocketVo webSocketVo = new WebSocketVo();
//        webSocketVo.setUrl(api.getUrl());
//        webSocketVo.setMethod(api.getApiMethod());
//        webSocketVo.setLineId(operationTrace.getLineId());
//        rabbitTemplate.convertAndSend("direct_interface_exchange","normal", JSON.toJSONString(webSocketVo));
/*                WebSocketVo webSocketVo = WebSocketVo.builder()
                        .lineId(presetId)
                        .method(api.getApiMethod())
                        .url(api.getUrl())
                        .build();*/
/*                WebSocketVo webSocketVo = WebSocketVo.builder()
                        .LineId(presetId)
                        .method("123")
                        .url("a.com")
                        .build();*/

        rabbitMsgUtils.updateOpreationTraceList(operationTrace.getLineId());
    }

    @Override
    public void deleteOperation(ProdOperationTrace operationTrace) {
        detailService.deleteDetailByOperation(operationTrace);
        this.removeById(operationTrace);
    }

    @Override
    public ProdOperationTrace getEntityByLineAndProcess(Long lineId, Long processId) {
        QueryWrapper<ProdOperationTrace> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("line_id", lineId);
        queryWrapper.eq("process_id", processId);
        queryWrapper.orderByDesc("create_time");
        queryWrapper.last("limit 1");
        return this.baseMapper.selectOne(queryWrapper);
    }

}
