package com.ruoyi.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.message.domian.EquipmentSpindleCollisionData;
import com.ruoyi.message.mapper.EquipmentSpindleCollisionDataMapper;
import com.ruoyi.message.service.IEquipmentSpindleCollisionDataService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Service
public class EquipmentSpindleCollisionDataServiceImpl extends ServiceImpl<EquipmentSpindleCollisionDataMapper, EquipmentSpindleCollisionData> implements IEquipmentSpindleCollisionDataService {

    @Autowired
    private RabbitTemplate rabbitTemplate;


    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public boolean saveOrUpdateWithMQ(EquipmentSpindleCollisionData entity) {
        if (null == entity) {
            return false;
        } else {
            if (null == entity.getId())
                this.save(entity);
            else
                this.updateById(entity);
            EquipmentSpindleCollisionData baseLineStorage = this.baseMapper.selectById(entity.getId());
            //2不相同，设置缓存，生产消息
//            SysPlatformApi api = apiService.getEntityByCode("dcs_getStorageInfo");
        }
        return true;
    }

    @Override
    public List<EquipmentSpindleCollisionData> listByCondition(Map entity) {
        return this.baseMapper.listByCondition(entity);
    }
}
