package com.ruoyi.production.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDto;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> @since 2022-05-07
 */
public interface IProdOperationTraceService extends IService<ProdOperationTrace> {

    /**
     * 根据条件查询信息
     * @param ProdOperationTrace
     * @return
     */
    List<ProdOperationTraceVo> listByCondition(ProdOperationTrace ProdOperationTrace);

    IPage<ProdOperationTraceVo> listForPage(ProdOperationTraceDto operationTraceDto, Integer current, Integer pageSiz);

    IPage<ProdOperationTraceVo> logInfoListForPage(ProdOperationTraceDto operationTraceDto, Integer current, Integer pageSiz);

    void saveOrUpdateOperationTrace(ProdOperationTrace operationTrace);

    void deleteOperation(ProdOperationTrace operationTrace);

    ProdOperationTrace getEntityByLineAndProcess(Long lineId,Long processId);



}
