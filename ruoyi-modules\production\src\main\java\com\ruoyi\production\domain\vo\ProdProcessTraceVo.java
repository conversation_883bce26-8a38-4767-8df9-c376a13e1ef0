package com.ruoyi.production.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.production.domain.ProdProcessTrace;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
@Data
public class  ProdProcessTraceVo extends ProdProcessTrace {

    @ApiModelProperty(value = "流程名称")
    private String processName;
    @ApiModelProperty(value = "物料名称")
    private String matName;
    @ApiModelProperty(value = "工序编号")
    private String schedulCode;
    @ApiModelProperty(value = "任务号")
    private String billCode;
    @ApiModelProperty(value = "设备状态")
    private Integer eqpStatus;
    @ApiModelProperty(value = "设备名称")
    private String eqpName;
    @ApiModelProperty(value = "设备编码")
    private String eqpCode;
    @ApiModelProperty(value = "工单号")
    private String ticketNumber;
    @ApiModelProperty(value = "实际加工开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realStartTime;
    @ApiModelProperty(value = "实际加工结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndTime;
    @ApiModelProperty(value = "产线对应流程链")
    List<BaseProcessVo> nodes;

    /*20220630添加产线工单列表*/
    List<ProdWorkOrderVo> workOrderVos;

    @ApiModelProperty(value = "设备ID")
    private Long eqpId;
    @ApiModelProperty(value = "自动加工")
    private boolean autoProcess;
    @ApiModelProperty(value = "是否自动加工中")
    private boolean processing;

    /*20230714添加产线工单状态*/
    private int orderStatus;
}
