package com.ruoyi.production.utils;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.production.domain.SysPlatformApi;
import com.ruoyi.production.service.ISysPlatformApiService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RabbitMsgUtils {

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ISysPlatformApiService apiService;

    public void sendMessage(WebSocketVo webSocketVo, String exchange) {
        RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
        rabbitTemplate.convertAndSend(exchange, null, JSON.toJSONString(webSocketVo));
    }

    public void  sendMessage(Long lineId, String exchange) {
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setLineId(lineId);
        sendMessage(webSocketVo, exchange);
    }

    public void  sendMessage( String exchange,String routingKey,WebSocketVo webSocketVo) {
        rabbitTemplate.convertAndSend(exchange,routingKey, JSON.toJSONString(webSocketVo));
    }

    public void updateOpreationTraceList(Long lineId){
        SysPlatformApi api = apiService.getEntityByCode("dcs_getOperationTraceList");
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setUrl(api.getUrl());
        webSocketVo.setMethod(api.getApiMethod());
        webSocketVo.setLineId(lineId);
//        this.sendMessage("direct_interface_exchange","normal",webSocketVo);
        rabbitTemplate.convertAndSend("direct_interface_exchange","normal", JSON.toJSONString(webSocketVo));
    }

    public void updateStorageInfo(Long lineId){
        SysPlatformApi api = apiService.getEntityByCode("dcs_getStorageInfo");
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setUrl(api.getUrl());
        webSocketVo.setMethod(api.getApiMethod());
        webSocketVo.setLineId(lineId);
        rabbitTemplate.convertAndSend("direct_interface_exchange", "normal", JSON.toJSONString(webSocketVo));
    }

    public void updateLineTrayInfo(Long lineId){
        SysPlatformApi api = apiService.getEntityByCode("dcs_getLineTrayInfo");
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setUrl(api.getUrl());
        webSocketVo.setMethod(api.getApiMethod());
        webSocketVo.setLineId(lineId);
        rabbitTemplate.convertAndSend("direct_interface_exchange", "normal", JSON.toJSONString(webSocketVo));
    }

    public void updateRoomTrayInfo(Long lineId){
        SysPlatformApi api = apiService.getEntityByCode("dcs_getRoomTrayInfo");
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setUrl(api.getUrl());
        webSocketVo.setMethod(api.getApiMethod());
        webSocketVo.setLineId(lineId);
        rabbitTemplate.convertAndSend("direct_interface_exchange", "normal", JSON.toJSONString(webSocketVo));
    }

    public void updateProcessTraceList(Long lineId){
        SysPlatformApi api = apiService.getEntityByCode("dcs_getProcessTraceList");
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setUrl(api.getUrl());
        webSocketVo.setMethod(api.getApiMethod());
        webSocketVo.setLineId(lineId);
        rabbitTemplate.convertAndSend("direct_interface_exchange", "normal", JSON.toJSONString(webSocketVo));
    }

    public void updateIOTAutoProcess(Long lineId){
        SysPlatformApi api = apiService.getEntityByCode("dcs_getIotAutoProcess");
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setUrl(api.getUrl());
        webSocketVo.setMethod(api.getApiMethod());
        webSocketVo.setLineId(lineId);
        rabbitTemplate.convertAndSend("direct_interface_exchange", "normal", JSON.toJSONString(webSocketVo));
    }


}
