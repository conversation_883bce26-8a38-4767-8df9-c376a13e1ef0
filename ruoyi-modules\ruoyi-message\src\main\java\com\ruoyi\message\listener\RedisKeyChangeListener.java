//package com.ruoyi.message.listener;
//
//import com.ruoyi.common.redis.service.RedisService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.connection.Message;
//import org.springframework.data.redis.connection.MessageListener;
//import org.springframework.data.redis.listener.PatternTopic;
//import org.springframework.data.redis.listener.Topic;
//import org.springframework.stereotype.Component;
//
//import java.nio.charset.Charset;
//
///**
// * <AUTHOR>
// * @createTime 2021-05-01 08:53:19
// * @description 期望是可以监听某个key的变化，而不是失效
// */
//@Component
//public class RedisKeyChangeListener implements MessageListener/* extends KeyspaceEventMessageListener */ {
//
//    @Autowired
//    private RedisService redisService;
////    private final String listenerKeyName; // 监听的key的名称
//    private static final Topic TOPIC_ALL_KEYEVENTS = new PatternTopic("__keyevent@*"); //表示只监听所有的key
//    private static final Topic TOPIC_KEYEVENTS_SET = new PatternTopic("__keyevent@0__:set"); //表示只监听所有的key
//    private static final Topic TOPIC_KEYNAMESPACE_NAME = new PatternTopic("__keyspace@0__:name"); // 不生效
//    // 监控
//    //private static final Topic TOPIC_KEYEVENTS_NAME_SET_USELESS = new PatternTopic("__keyevent@0__:set myKey");
//    private String keyspaceNotificationsConfigParameter = "KEA";
//
//
//    @Override
//    public void onMessage(Message message, byte[] pattern) {
//        System.out.println("key发生变化===》" + message);
//        byte[] body = message.getBody();
//        String string = new String(body, Charset.forName("utf-8"));
//        Object o = redisService.getCacheObject("ttttttttt");
//        System.out.println(string);
//        System.out.println(redisService.getCacheObject("ttttttttt").toString());
//    }
//}