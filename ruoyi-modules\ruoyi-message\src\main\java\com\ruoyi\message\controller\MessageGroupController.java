package com.ruoyi.message.controller;


import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.message.service.IMessageGroupService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@RestController
@RequestMapping("group")
public class MessageGroupController extends BaseModuleController {

    @Autowired
    private IMessageGroupService groupService;

    @ApiOperation("获取所有分组信息")
    @GetMapping("getAll")
    @Cacheable(value = "message:group")
    public AjaxResult getAll() {
        return AjaxResult.success(groupService.getAll());
    }


}
