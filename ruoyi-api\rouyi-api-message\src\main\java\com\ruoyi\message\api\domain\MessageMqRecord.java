package com.ruoyi.message.api.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-30
 */
@Data
@ApiModel(value = "MessageMqRecord对象", description = "")
public class MessageMqRecord extends MyBaseEntity {

    public static final Integer TYPE_NOT_CONSUMED = 0;
    public static final Integer TYPE_CONSUMED = 1;
    public static final Integer TYPE_FAILED = 2;

    @ApiModelProperty(value = "交换机")
    private String exchange;

    @ApiModelProperty(value = "交换机绑定")
    private String routingkey;

    @ApiModelProperty(value = "请求队列")
    private String queue;

    @ApiModelProperty(value = "请求消息体")
    private String message;

    @ApiModelProperty(value = "请求消息体ID")
    private String messageId;

    @ApiModelProperty(value = "消费状态：0未消费，1已消费，2消费失败")
    private Integer consumeStatus;

    @ApiModelProperty(value = "消息消费次数")
    private Integer count;

}
