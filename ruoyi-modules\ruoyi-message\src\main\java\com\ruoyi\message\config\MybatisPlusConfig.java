package com.ruoyi.message.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration
@MapperScan("com.ruoyi.message.mapper")
public class MybatisPlusConfig {
    /**
     * 分页插件
     *
     * @return
     */
    //@Bean
    //public PaginationInterceptor paginationInterceptor() {
    //    PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
    //    // 设置请求的页面大于最大页后操作， true调回到首页，false 继续请求  默认false
    //    // paginationInterceptor.setOverflow(false);
    //    // 设置最大单页限制数量，默认 500 条，-1 不受限制
    //    // paginationInterceptor.setLimit(500);
    //    return paginationInterceptor;
    //}
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(tenantLineInnerInterceptor());
        interceptor.addInnerInterceptor(deletedInnerInterceptor());//添加deleted=0插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor()); // 乐观锁插件
        // DbType：数据库类型(根据类型获取应使用的分页方言)
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); // 分页插件
        return interceptor;
    }

    public TenantLineInnerInterceptor tenantLineInnerInterceptor() {

        return new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (loginUser != null) {
                    //多租户模式，租户id从当前用户获取
                    return new LongValue(loginUser.getSysUser().getLineId());
                } else {
                    //多租户模式，租户id为null
                    return new LongValue(11);
                }
            }

            @Override
            public String getTenantIdColumn() {
                // 对应数据库租户ID的列名
                return "line_id";
            }

            // 这是 default 方法,默认返回 false 表示所有表都需要拼多租户条件
            @Override
            public boolean ignoreTable(String tableName) {
                LoginUser loginUser = SecurityUtils.getLoginUser();
                // 判断是否登录，如果登录则过滤
                if (null != loginUser) {
                    // 判断是否平台超级管理员，如果是平台超级管理员则拥有所有数据权限
//                    if (!SecurityUtils.isAdmin(loginUser.getUserid())) {
                    if (true) {
                        // 需要过滤租户的表
                        List<String> tableNameList = Arrays.asList(
                                // 系统模块
//                                "sys_user" , "sys_role" , "sys_dept" , "sys_post" , "sys_oper_log" , "sys_notice",
//                                // 测试模块
//                                "test_demo", "test_tree"
                                // ... 省略业务模块表 ...
                                //业务原因需要跨产线查询
//                                "base_equipment",
                                // 需要过滤租户的表
                                "message_news"
                        );
                        return !tableNameList.contains(tableName);
                    }
                }
                return true;
            }
        });
    }

    public TenantLineInnerInterceptor deletedInnerInterceptor() {

        return new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                return new LongValue(0);
            }

            @Override
            public String getTenantIdColumn() {
                // 对应数据库租户ID的列名
                return "deleted";
            }

            // 这是 default 方法,默认返回 false 表示所有表都需要拼多租户条件
            @Override
            public boolean ignoreTable(String tableName) {
                if (true) {
                    // 需要过滤租户的表
                    List<String> tableNameList = Arrays.asList(
                            // 系统模块
//                                "sys_user" , "sys_role" , "sys_dept" , "sys_post" , "sys_oper_log" , "sys_notice",
//                                // 测试模块
//                                "test_demo", "test_tree"
                            // ... 省略业务模块表 ...
                            //业务原因需要跨产线查询
//                                "base_equipment","base_process","prod_operation_trace_detail"
                            // 需要过滤租户的表
                            "message_news"
                    );
                    return !tableNameList.contains(tableName);
                }
                return true;
            }
        });
    }


}