package com.ruoyi.production.core.observable;

import com.ruoyi.production.domain.ProdWorkOrder;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class OrderStatusChangedEvent extends ApplicationEvent {
    private ProdWorkOrder workOrder;

    public OrderStatusChangedEvent(Object source, ProdWorkOrder workOrder) {
        super(source);
        this.workOrder = workOrder;
    }
}
