<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.BaseProcessMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.BaseProcess">
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="id" property="id"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
        <result column="line_id" property="lineId"/>
        <result column="parent_id" property="parentId"/>
        <result column="order_num" property="orderNum"/>
        <result column="process_name" property="processName"/>
        <result column="process_code" property="processCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_by,
        create_time,
        update_by,
        update_time,
        id,
        deleted,
        remark, line_id, parent_id, order_num, process_name, process_code
    </sql>

    <select id="getProcessLinksByParentId" resultMap="BaseResultMap">
        SELECT ID.LEVEL,
               DATA.*
        FROM (
                     SELECT @ids AS _ids,
		( SELECT @ids := GROUP_CONCAT( id ) FROM base_process WHERE FIND_IN_SET(parent_id, @ids ) ) AS cids,
		@l := @l + 1 AS LEVEL
                     FROM
                             base_process,
                             ( SELECT @ids := #{parentId}, @l := 0 ) b
                     WHERE
                             @ids IS NOT NULL
                     ) ID,
             base_process DATA
        WHERE FIND_IN_SET(DATA.id, ID._ids)
        ORDER BY LEVEL
    </select>

    <select id="listByCondition" resultType="com.ruoyi.production.domain.vo.BaseProcessVo">
        select bp.* from base_process bp
        <where>
            <if test="params.lineId != null">
                and bp.line_id = #{params.lineId}
            </if>
            <if test="params.processCode != null">
                and bp.process_code = #{params.processCode}
            </if>
        </where>
        order by bp.order_num asc
    </select>
</mapper>
