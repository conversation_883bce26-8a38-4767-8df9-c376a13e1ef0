# Tomcat
server:
  port: 9225

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-message
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
#      username: nacos
#      password: hnht1qaz!QAZ
      discovery:
        # 服务注册地址
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
#        server-addr: ***********:8848
#        server-addr: ***********:8848
#        server-addr: *************:8848
#        server-addr: ************:8848

      config:
        # 配置中心地址
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
#        server-addr: ***********:8848
#        server-addr: ***********:8848
#        server-addr: *************:8848
#        server-addr: ************:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
