package com.ruoyi.production.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.core.observable.OrderDoneEvent;
import com.ruoyi.production.core.observable.OrderRestoreEvent;
import com.ruoyi.production.core.observable.OrderWaitEvent;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.domain.vo.detection.DetectionReportVo;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceVo;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.mapper.ProdWorkOrderMapper;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.service.ISysPlatformApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Slf4j
@Service
public class ProdWorkOrderServiceImpl extends ServiceImpl<ProdWorkOrderMapper, ProdWorkOrder> implements IProdWorkOrderService {

    @Autowired
    private ProdWorkOrderMapper prodWorkOrderMapper;
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private ISysPlatformApiService apiService;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public List<ProdWorkOrderVo> listByCondition(ProdWorkOrderDto prodWorkOrderDto) {
        //if (ObjectUtil.isEmpty(prodWorkOrder))
        //    throw new ServiceException("查询参数为空");
//        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
//        if (prodWorkOrder.getStatus() != null)
//            queryWrapper.ge("status",prodWorkOrder.getStatus());
//        if (prodWorkOrder.getSchedulStartDate() != null)
//            queryWrapper.ge("schedul_start_date",prodWorkOrder.getSchedulStartDate());
//        if (prodWorkOrder.getSchedulEndDate() != null)
//            queryWrapper.le("schedul_end_date",prodWorkOrder.getSchedulEndDate());
//        queryWrapper.notExists("SELECT * from prod_process_trace where prod_process_trace.operation_id = prod_work_order.operation_id");
////        return this.getBaseMapper().selectList(queryWrapper);
        //return this.getBaseMapper().listByCondition(prodWorkOrder);
        return prodWorkOrderMapper.listByCondition(prodWorkOrderDto);
    }

    @Override
    public List<Object> getOperationIdList(ProdWorkOrder prodWorkOrder) {
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("operation_id");
        if (prodWorkOrder.getSchedulStartDate() != null)
            queryWrapper.ge("schedul_start_date", prodWorkOrder.getSchedulStartDate());
        if (prodWorkOrder.getSchedulEndDate() != null)
            queryWrapper.le("schedul_start_date", prodWorkOrder.getSchedulEndDate());
        return this.getBaseMapper().selectObjs(queryWrapper);
    }

    @Override
    public List<ProdWorkOrder> getEntityListByParam(ProdWorkOrderDto prodWorkOrderDto) {
        if (null == prodWorkOrderDto)
            return null;
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        if (null != prodWorkOrderDto.getBillCode() && StringUtils.isNotBlank(prodWorkOrderDto.getBillCode()))
            queryWrapper.eq("bill_code", prodWorkOrderDto.getBillCode());
        if (null != prodWorkOrderDto.getSchedulCode() && StringUtils.isNotBlank(prodWorkOrderDto.getSchedulCode()))
            queryWrapper.eq("schedul_code", prodWorkOrderDto.getSchedulCode());
        if (null != prodWorkOrderDto.getStatus())
            queryWrapper.eq("status", prodWorkOrderDto.getStatus());
        if (null != prodWorkOrderDto.getBillSchedulCode())
            queryWrapper.eq("bill_schedul_code", prodWorkOrderDto.getBillSchedulCode());
        if (null != prodWorkOrderDto.getStatusList() && prodWorkOrderDto.getStatusList().size() > 0)
            queryWrapper.in("status", prodWorkOrderDto.getStatusList());
        if (null != prodWorkOrderDto.getRealEqp())
            queryWrapper.eq("real_eqp", prodWorkOrderDto.getRealEqp());
        if (null != prodWorkOrderDto.getDistributed())
            queryWrapper.eq("distributed", prodWorkOrderDto.getDistributed());
        if (null != prodWorkOrderDto.getNotInStatusList() && prodWorkOrderDto.getNotInStatusList().size() > 0)
            queryWrapper.notIn("status", prodWorkOrderDto.getNotInStatusList());

        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public List<ProdWorkOrderVo> getTotalList(ProdWorkOrder prodWorkOrder) {
        return this.getBaseMapper().getTotalList(prodWorkOrder);
    }

    @Override
    public List<ProdWorkOrderVo> getProcessTraceList(ProdWorkOrder prodWorkOrder) {
        return this.getBaseMapper().getProcessTraceList(prodWorkOrder);
    }

    @Override
    public List<Object> getBillCodeList(ProdWorkOrder prodWorkOrder) {
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct bill_code");
        if (StringUtils.isNotBlank(prodWorkOrder.getStatus() + ""))
            queryWrapper.eq("status", prodWorkOrder.getStatus());

        return this.getBaseMapper().selectObjs(queryWrapper);
    }

    @Override
    public List<Object> getDoneBillCodeList(ProdWorkOrder prodWorkOrder) {
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct bill_code");
        queryWrapper.ge("status", 4);

        return this.getBaseMapper().selectObjs(queryWrapper);
    }

    @Override
    public List<Object> getSchedulCodeList(String billcode) {
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct schedul_code").eq("bill_code", billcode);

        return this.getBaseMapper().selectObjs(queryWrapper);
    }

    @Override
    public ProdWorkOrderVo getEntityByParam(ProdWorkOrderDto prodWorkOrderDto) {
        return this.getBaseMapper().getEntityByParam(prodWorkOrderDto);
    }

    @Override
    public ProdWorkOrder getEntityByOperationId(String operationId) {
        if (StringUtils.isBlank(operationId))
            return null;
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);

        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public IPage<ProdWorkOrderVo> listForPage(ProdWorkOrderDto prodWorkOrderDto, Integer current, Integer pageSiz) {
        Page<ProdOperationTraceVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);
        return this.getBaseMapper().listForPage(page, prodWorkOrderDto);
    }

    @Override
    public IPage<ProdWorkOrderVo> listForPageToDock(ProdWorkOrderDto prodWorkOrderDto, Integer current, Integer pageSiz) {
        Page<ProdOperationTraceVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);
        return this.getBaseMapper().listForPageToDock(page, prodWorkOrderDto);
    }

    @Override
    public IPage<ProdWorkOrderVo> pageList(ProdWorkOrderDto prodWorkOrderDto, Integer current, Integer pageSiz) {
        Page<ProdOperationTraceVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);
        return this.getBaseMapper().pageList(page, prodWorkOrderDto);
    }

    @Override
    public void updateCompleteByOperationId(String operationId) {
        if (StringUtils.isBlank(operationId))
            return;
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        ProdWorkOrder prodWorkOrder = this.getBaseMapper().selectOne(queryWrapper);
        if (prodWorkOrder == null)
            return;
        prodWorkOrder.setStatus(WorkOrderStatusEnum.DONE.getCode());
        this.updateById(prodWorkOrder);
    }

    @Override
    public List<ProdWorkOrder> getListByOperationId(String operationId) {
        if (StringUtils.isBlank(operationId))
            return null;
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public Integer deletedByBillSchedulCode(ProdWorkOrderDto prodWorkOrderDto) {
        //判断状态是否可删除
        QueryWrapper<ProdWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_code", prodWorkOrderDto.getBillCode());
        queryWrapper.eq("schedul_code", prodWorkOrderDto.getSchedulCode());
        List<ProdWorkOrder> list = this.baseMapper.selectList(queryWrapper);
        queryWrapper.eq("status", WorkOrderStatusEnum.CREATED.getCode());
        List<ProdWorkOrder> statusList = this.baseMapper.selectList(queryWrapper);
        if (list.size() != statusList.size())
            throw new ServiceException("存在已开工工单，不允许删除！");
        return this.baseMapper.delete(queryWrapper);
    }

    @Override
    public void orderStatusStepForward(String operationId) {

    }

    @Override
    public void orderStatusStepRollback(String operationId) {

    }

    @Override
    public void updateWorkOrderWithStatus(ProdWorkOrder workOrder) {
        ProdWorkOrder updateOrder = new ProdWorkOrder();
        updateOrder.setId(workOrder.getId());
        updateOrder.setStatus(workOrder.getStatus());
        if (null != workOrder.getDistributed())
            updateOrder.setDistributed(workOrder.getDistributed());
        if (null != workOrder.getBz1())
            updateOrder.setBz1(workOrder.getBz1());
        this.updateById(updateOrder);
        int status = workOrder.getStatus();
        if (WorkOrderStatusEnum.WAIT.getCode().equals(status)) {
            applicationContext.publishEvent(new OrderWaitEvent(this, workOrder));
        } else if (WorkOrderStatusEnum.DONE.getCode().equals(status)) {
            applicationContext.publishEvent(new OrderDoneEvent(this, workOrder));
        } else if (WorkOrderStatusEnum.CREATED.getCode().equals(status)) {
            //重置货位信息
            applicationContext.publishEvent(new OrderRestoreEvent(this, workOrder));
        }
    }

    @Override
    @Transactional
    public void forceDoneWorkOrder(ProdWorkOrder order) {
        order.setStatus(WorkOrderStatusEnum.DONE.getCode());
        this.updateWorkOrderWithStatus(order);
        //2023年6月19日 10:57:52 添加 删除梁慧工单接口，协同动作
        String delProcUrl = apiService.getEntityByCode("iot_delproc_"+order.getLineId()).getUrl();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("procid", order.getOperationId());
        String result = HttpRequest.post(delProcUrl).timeout(3000)
                .body(jsonObject.toJSONString())
                .execute().body();
        //机床解绑
        BaseEquipment baseEquipment = equipmentService.getEntityByCode(order.getLineId(), EqpTypeEnum.MACHINE.getCode(), order.getRealEqp());
        if (null == baseEquipment)
            throw new ServiceException("未查询到指定机床");
        BaseEquipment update = new BaseEquipment();
        update.setId(baseEquipment.getId());
        update.setProcessing(0);
        equipmentService.updateById(update);
        log.info("********** 设置机床 未加工 中：" + baseEquipment.getEqpCode());
    }

    @Override
    public List<DetectionReportVo> getDetectionReportData(ProdWorkOrder prodWorkOrder) {
        return this.getBaseMapper().getDetectionReportData(prodWorkOrder);
    }

    @Override
    public IPage<DetectionReportVo> listForPageDetectionReport(ProdWorkOrderDto prodWorkOrder, Integer current, Integer pageSiz) {
        Page<DetectionReportVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);
        return this.getBaseMapper().listForPageDetectionReport(page, prodWorkOrder);
    }

    @Override
    public List<ProdWorkOrderVo> apiGetWorkOrderList(ProdWorkOrderDto prodWorkOrderDto) {
        return prodWorkOrderMapper.apiGetWorkOrderList(prodWorkOrderDto);
    }

}
