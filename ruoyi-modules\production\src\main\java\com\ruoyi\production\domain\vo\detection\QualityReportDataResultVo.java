package com.ruoyi.production.domain.vo.detection;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class QualityReportDataResultVo {
    @ApiModelProperty("合格数")
    private int qualifiedNum;
    @ApiModelProperty("返修合格数")
    private int repairQualifiedNum;
    @ApiModelProperty("合格率")
    private int qualifiedRate;
    @ApiModelProperty("报废数")
    private int scrappedNum;
    @ApiModelProperty("操作者")
    private String operator;
    @ApiModelProperty("日期1")
    private Date date1;
    @ApiModelProperty("检验")
    private String inspection;
    @ApiModelProperty("日期2")
    private Date date2;
}
