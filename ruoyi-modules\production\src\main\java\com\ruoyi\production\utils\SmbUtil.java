package com.ruoyi.production.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.production.domain.ProdMachineDetection;
import com.ruoyi.production.domain.ProdMachineDetectionDetail;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.SysPlatformApi;
import com.ruoyi.production.enums.DetectionStatusEnum;
import com.ruoyi.production.service.IProdMachineDetectionDetailService;
import com.ruoyi.production.service.IProdMachineDetectionService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.system.api.domain.SysDictData;
import jcifs.smb.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.MalformedURLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> AllenJ
 * @since : 2021/5/31
 */
//@Async("myTaskExecutor")
@Component
public class SmbUtil {

    @Autowired
    private ISysPlatformApiService apiService;
    @Autowired
    private IProdMachineDetectionService detectionService;
    @Autowired
    private IProdMachineDetectionDetailService detailService;
    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;

    @Value("${checkFileServer.baseDir}")
    private String baseDir;

    @Value("${checkFileServer.user}")
    private String user;

    @Value("${checkFileServer.pass}")
    private String pass;

    @Value("${checkFileServer.userDomain}")
    private String userDomain;

    @Value("${checkFileServer.suffix}")
    private String suffix;

    //****************** ***********文件服务器  *********************//

    @Value("${checkFileServer110.mach}")
    private String mach110;

    @Value("${checkFileServer110.baseDir}")
    private String baseDir110;

    @Value("${checkFileServer110.user}")
    private String user110;

    @Value("${checkFileServer110.pass}")
    private String pass110;

    @Value("${checkFileServer110.userDomain}")
    private String userDomain110;

    @Value("${checkFileServer110.suffix}")
    private String suffix110;

    //****************** 10.72.1.14文件服务器  *********************//
    @Value("${checkFileServer14.mach}")
    private String mach14;

    @Value("${checkFileServer14.baseDir}")
    private String baseDir14;

    @Value("${checkFileServer14.user}")
    private String user14;

    @Value("${checkFileServer14.pass}")
    private String pass14;

    @Value("${checkFileServer14.userDomain}")
    private String userDomain14;

    @Value("${checkFileServer14.suffix}")
    private String suffix14;

    private static Logger logger = LoggerFactory.getLogger(SmbUtil.class);

    private synchronized void test() {

        try {
            Thread.sleep(2000);
            System.out.println(Thread.currentThread().getName());
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * @date 2020-03-19
     * 遍历指定目录下的文件
     * shareDirectory: smb://192.168.4.19/x804shareTest/
     */
    public void getFileAndSave(ProdWorkOrder order) {
        logger.info("********** 开始远程读取在线检测结果文件，数据库id：" + order.getId() + " OperationId：" + order.getOperationId());
        SysPlatformApi api ;

        ProdMachineDetection detection = new ProdMachineDetection();
        detection.setOperationId(order.getOperationId());
        detection.setLineId(order.getLineId());
        detection.setStatus(2);

        Map<Long, SmbFile> map = new TreeMap<>(Comparator.reverseOrder());

        List<ProdMachineDetectionDetail> detailList = new ArrayList<>();
        try {
            // 域服务器验证
            NtlmPasswordAuthentication auth;
            SmbFile remoteFile;
            //设置机内检测主表字段
            //先生成默认未检测的二维码，防止出错无法添加
            String[] machs110 = mach110.split(",");
            String[] machs14 = mach14.split(",");
            //西门子
            if (Arrays.asList(machs14).contains(order.getRealEqp())) {
                api = apiService.getEntityByCode("iot_machineDetection_xmz");
                auth = new NtlmPasswordAuthentication(userDomain14, user14, pass14);
                remoteFile = new SmbFile(baseDir14 + order.getRealEqp() + "/", auth);
            } else if (Arrays.asList(machs110).contains(order.getRealEqp())) {
                //海德汉
                api = apiService.getEntityByCode("iot_machineDetection_hdh");
                auth = new NtlmPasswordAuthentication(userDomain110, user110, pass110);
                remoteFile = new SmbFile(baseDir110 + order.getRealEqp() + "/", auth);
            } else {
                throw new ServiceException("未配置此机床检测文件路径！！！");
            }

            // 域服务器验证
//            auth = new NtlmPasswordAuthentication(userDomain, user, pass);
//            remoteFile = new SmbFile(baseDir + order.getRealEqp() + "/", auth);
//            remoteFile = new SmbFile(baseDir + "96/", auth);

            if (remoteFile.exists()) {
//                SmbFile[] files = remoteFile.listFiles();
                SmbFile[] files = remoteFile.listFiles((fileList, name) -> name.endsWith("." + suffix));
                if (files.length <= 0) {
                    logger.error("@@@@@@@@@@ 未查询到 " + order.getRealEqp() + "号机床，" + suffix + " 结尾的机内检测文件！！！");
//                    detection.setStatus(2);
                } else {
//                    Arrays.stream(files).sorted(Comparator.comparing(SmbFile::createTime, Comparator.reverseOrder()));
                    //时间降序排序
                    for (SmbFile f : files) {
                        map.put(f.createTime(), f);
                    }
                    for (Long aLong : map.keySet()) {
                        SmbFile f = map.get(aLong);
                        //判断文件是否为空
                        if (f.length() == 0L) {
//                            detection.setStatus(2);
                            logger.error("检测文件为空（O KB） 机床：" + order.getRealEqp());
                            break;
                        }
                        //添加检验是同一文件重复读取的判断
                        String fileName = redisService.getCacheObject("detectionfile:" + order.getRealEqp());
                        if (fileName != null && fileName.equals(f.getName())) {
//                            detection.setStatus(2);
                            logger.error("该次检测文件与上次检测文件重复，请检查是否上传检测文件！ 机床：" + order.getRealEqp());
                            break;
                        } else {
                            redisService.setCacheObject("detectionfile:" + order.getRealEqp(), f.getName());
                        }
                        InputStream is = f.getInputStream();
                        byte[] bytes = new byte[f.getContentLength()];
                        is.read(bytes);
                        //文本内容
                        String result = new String(bytes);
                        is.close();
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put(f.getName(), result);
                        JSONArray jsonArray = new JSONArray();
                        jsonArray.add(jsonObject);
//                        logger.info("********* 》》》 调用接口参数打印：" + jsonArray.toJSONString());
                        detection.setFilename(f.getName());//文件名
                        detection.setResultContent(jsonArray.toJSONString());//请求解析接口的参数
                        result = HttpRequest.post(api.getUrl())
                                .timeout(3000)
                                .body(jsonArray.toJSONString())
                                .execute().body();
                        JSONArray jsonResult = JSON.parseArray(unicodeToString(result));

                        JSONArray resultLines = jsonResult.getJSONObject(0).getJSONArray(f.getName());


                        if (resultLines == null || resultLines.size() <= 0) {
                            detection.setStatus(2);
                        } else {
//                            String xyz = "XY";
                            for (int i = 0; i < resultLines.size(); i++) {

                                Object resultLine = resultLines.get(i);
                                JSONArray list = JSON.parseArray(resultLine.toString());
                                ProdMachineDetectionDetail detail = new ProdMachineDetectionDetail();
                                detection.setProgramName(list.getString(0) + "");//程序名

                                detail.setName(list.get(1) + "");//列名
                                detail.setTargetValue(list.getString(2));//目标值
                                detail.setRealValue(list.getString(3));//实测值
                                detail.setUpperTolerance(("/").equals(list.getString(5)) ? "" : list.getString(5));//上公差
                                detail.setLowerTolerance(("/").equals(list.getString(6)) ? "" : list.getString(6));//下公差

                                detail.setStatus(Integer.parseInt(list.getString(7)) == 2 ? null : Integer.parseInt(list.getString(7)));//在线监测结论

                                //跳过无用数据 规则 前三条 XYZ+值为0+i<3
/*                                if (xyz.contains(detail.getName()) && i < 3 && (int) Float.parseFloat(detail.getTargetValue()) == 0) {
                                    continue;
                                }*/

                                if (detail.getStatus() != null) {
                                    detail.setStatusValue(DetectionStatusEnum.getInfoByKey(detail.getStatus()));
                                }
                                detailList.add(detail);

                                //检测文件生成时间
                                detection.setTime(new Date(f.createTime()));
                            }

                            //在线检测主表处理
                            int noResultNums = 0;
                            int chaoChaNums = 0;
                            for (ProdMachineDetectionDetail detail : detailList) {
                                if (detail.getStatus() == null) {
                                    noResultNums++;
                                } else
                                    //超差的处理 有一个超差，主结果超差
                                    if (detail.getStatus() == 0) {
                                        chaoChaNums++;
                                        break;
                                    }
                            }
                            //子表明细全部是 未检测，主结果无结论
                            if (detailList.size() == noResultNums) {
                                detection.setStatus(3);
                            } else if (chaoChaNums > 0) {
                                detection.setStatus(0);
                            } else {
                                detection.setStatus(1);
                            }
                        }
                        //由于现场bug，导致最新文件一直被某进程占用，一删除就报错
                        map.remove(aLong);
                        break;
                    }
                }
            } else {
                detection.setStatus(2);
                logger.error("@@@@@@@@@@ 未查询到 " + order.getRealEqp() + " 机内检测机床路径！！！");
            }
            //删除文件
/*            for (Long aLong : map.keySet()) {
                SmbFile f = map.get(aLong);
                f.delete();
            }*/
        } catch (Exception e) {
//            if(e instanceof IOException){
//            detection.setStatus(2);
//            }
//            if (null == detection.getStatus()) {
//                detection.setStatus(2);
//            }
//            e.printStackTrace();
            logger.error("@@@@@@@@@@ 在线监测过程出错 >>>  operationId>>>" + order.getOperationId() + "  reason>>> " + e.getMessage());
        } finally {
            ProdWorkOrder updateOrder = new ProdWorkOrder();
            updateOrder.setId(order.getId());
            //生成条码
            String barCode = order.getTicketNumber().replace("-", "").replace("_", "").replace(":", "");
            barCode = barCode + detection.getStatus();
            //工单关联条码
            updateOrder.setBarcode(barCode);
            //保存检测结果至工单
            updateOrder.setDetectionStatus(detection.getStatus());
            //需求特殊，只给“质量报表”统计用，不给真实专检表字段赋值
            updateOrder.setSpecialDetectionStatus(detection.getStatus());
            boolean result = orderService.updateById(updateOrder);
            //生成 条形码
            File file = BarcodeUtils.generateFile(barCode, order.getRealEqp() + ".png");
            String base64Str = BarcodeUtils.encodeBase64File(file);
            detection.setBarcode(base64Str);
            detectionService.save(detection);
            //翻译检测子表 name
            if (detailList.size() > 0) {
                List<SysDictData> dictDataList = DictUtils.getDictCache("testing_cn_us");
                for (ProdMachineDetectionDetail detail : detailList) {
                    detail.setPid(detection.getId());
                    for (SysDictData sysDictData : dictDataList) {
                        if (sysDictData.getDictValue().equals(detail.getName())) {
                            detail.setName(sysDictData.getDictLabel());
                            break;
                        }
                    }
                }
                detailService.saveBatch(detailList);
            }

            //2024-07-19 添加逻辑 连续两次不合格消息通知
            if(detection.getStatus() == 0)
            {
                String key = "detectionstatus0:" + order.getRealEqp();
                if(redisService.hasKey(key)){
                    //连续两次在线检测超差通知
                    MessageNews messageNews = new MessageNews();
                    messageNews.setContent("连续两次在线检测超差通知 >>>" +
                            "机床号：" + order.getRealEqp());
                    messageNews.setCreaterId(1L);
                    messageNews.setLineId(order.getLineId());
                    messageNews.setButtonGroupId(2L);
                    remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
                    redisService.setCacheObject(key, order.getOperationId());
                }else {
                    redisService.deleteObject(key);
                }

            }

            logger.info("**************** 检测完毕 >>>  operation_id:" + order.getOperationId() + " 更新结果：" + result);
        }
    }

    /**
     * unicode转字符串
     *
     * @param unicodeStr unicode
     * @return 字符串
     */
    public String unicodeToString(String unicodeStr) {
        // XDigit是POSIX字符类，表示十六进制数字，\p{XDigit}等价于[a-fA-F0-9]
        // pattern用于匹配形如\\u6211的字符串
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(unicodeStr);
        char ch;
        while (matcher.find()) {
            // 捕获组按开括号'('从左到右编号（从1开始），以(A(B(C)))为例，group(1)表示(A(B(C))，group(2)表示(B(C))，group(3)表示(C)
            // group(2)表示第二个捕获组，即(\p{XDigit}{4})
            // Integer.parseInt(str, 16)把16进制的数字字符串转化为10进制，比如Integer.parseInt("16", 16) = 22
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            // 把第一个捕获组，即形如\\u6211这样的字符串替换成中文
            unicodeStr = unicodeStr.replace(matcher.group(1), ch + "");
        }
        return unicodeStr;
    }

    /**
     * 一次性读取全部文件数据
     *
     * @param strFile
     */
    public void readFile(String strFile) {
        try {
            InputStream is = new FileInputStream(strFile);
            int iAvail = is.available();
            byte[] bytes = new byte[iAvail];
            is.read(bytes);
            logger.info("********** 文件内容:\n" + new String(bytes));
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @Param shareUrl:smb://192.168.4.19/x804shareTest/abc.jpg
     * @Param localDirectory 本地目录，如d://home/<USER>
     */
    public void smdDownload(String shareUrl, String localDirectory) {
        NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(userDomain, user,
                pass);
        SmbFile remoteFile = null;
        try {
            remoteFile = new SmbFile(shareUrl, auth);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        try {
            if (!remoteFile.exists()) {
                System.out.print("共享文件不存在");
                return;
            }
        } catch (SmbException e) {
            e.printStackTrace();
        }
        // 有文件的时候再初始化输入输出流
        InputStream in = null;
        OutputStream out = null;
        try {
            String fileName = remoteFile.getName();
            File localFile = new File(localDirectory + File.separator + fileName);
            File fileParent = localFile.getParentFile();
            if (null != fileParent && !fileParent.exists()) {
                fileParent.mkdirs();
            }
            in = new BufferedInputStream(new SmbFileInputStream(remoteFile));
            out = new BufferedOutputStream(new FileOutputStream(localFile));
            byte[] buffer = new byte[1024];
            int byteRead;
            while ((byteRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, byteRead);
            }
            out.flush(); //刷新缓冲区输出流
        } catch (Exception e) {
            logger.error("@@@@@@@@@@ ", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 下载文件到指定文件夹
     *
     * @param remoteUrl
     * @param shareFolderPath
     * @param fileName
     * @param localDir
     */
    public static void downloadFileToFolder(String remoteUrl, String fileName, String localDir) {
        InputStream in = null;
        OutputStream out = null;
        try {
            SmbFile remoteFile = new SmbFile(remoteUrl + "/" + fileName);
            File localFile = new File(localDir + File.separator + fileName);
            in = new BufferedInputStream(new SmbFileInputStream(remoteFile));
            out = new BufferedOutputStream(new FileOutputStream(localFile));
            byte[] buffer = new byte[1024];
            while (in.read(buffer) != -1) {
                out.write(buffer);
                buffer = new byte[1024];
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                out.close();
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @Param shareDirectory 共享目录
     * @Param localFilePath 本地目录中的文件路径
     */
    public static void smbUpload(String shareDirectory, String localFilePath) {
        InputStream in = null;
        OutputStream out = null;
        try {
            File localFile = new File(localFilePath);
            String fileName = localFile.getName();
            SmbFile remoteFile = new SmbFile(shareDirectory + "/" + fileName);
            in = new BufferedInputStream(new FileInputStream(localFile));
            out = new BufferedOutputStream(new SmbFileOutputStream(remoteFile));
            byte[] buffer = new byte[1024];
            int byteRead;
            while ((byteRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, byteRead);
            }
            out.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) throws ParseException, MalformedURLException, SmbException {

//        Map<Integer, Integer> map = new TreeMap<>(Comparator.reverseOrder());
//        map.put(2, 2);
//        map.put(3, 2);
//        map.put(1, 2);
//        System.out.println(map);
/*        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
        Date date = sdf.parse("2023-06-08 08:00:00");
        for (int i = 0; i < 10; i++) {
            for (int j = 0; j < 10; j++) {

                String s = "insert into prod_work_order (create_by,line_id,bill_code,schedul_code," +
                        "bill_schedul_code,status,schedul_start_date,schedul_end_date,operation_id," +
                        "material_desc,mat_name,ticket_number) values ('007','1','9898989898-01','55'," +
                        "'9898989898-01_55','0','"+sdf.format(date)+"','"+sdf.format(date)+"','"+sdf2.format(date)+(i+j)+"'," +
                        "'新5号线测试','新5号线测试','test-line-5');";
                System.out.println(s);

            }
            date = DateUtil.offsetDay(date, 1);
        }*/

        NtlmPasswordAuthentication auth;
        SmbFile remoteFile;
        auth = new NtlmPasswordAuthentication("hnht", "developer01", "123456a.");
        remoteFile = new SmbFile("smb://***********/detectionFiles/" + "97" + "/", auth);
        if (remoteFile.exists()) {
            System.out.println(1);
                SmbFile[] files = remoteFile.listFiles();
            System.out.println(1);
        }
    }

}

