package com.ruoyi.production.enums;

/**
 * 货位类型
 * 
 * <AUTHOR>
 */
public enum StorageFillerTypeEnum
{
    EMPTY(0, "空"),
    PART(1, "物料");
//    TRAYA(2, "大托盘"),
//    TRAYB(3, "小托盘");

//    TRAYA(2, "托盘-夹爪"),
//    TRAYB(3, "托盘-虎钳"),
//    TRAYC(4, "托盘-工装");

    private final Integer code;
    private final String info;

    StorageFillerTypeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(Integer code) {
        for (StorageFillerTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public static Integer getInfoByValue(String value) {
        for (StorageFillerTypeEnum type : values()) {
            if (type.getInfo().equals(value)) {
                return type.getCode();
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
