package com.ruoyi.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.message.domian.EquipmentSpindleCollisionData;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface IEquipmentSpindleCollisionDataService extends IService<EquipmentSpindleCollisionData> {
    public boolean saveOrUpdateWithMQ(EquipmentSpindleCollisionData entity);
    List<EquipmentSpindleCollisionData> listByCondition(Map entity);

}
