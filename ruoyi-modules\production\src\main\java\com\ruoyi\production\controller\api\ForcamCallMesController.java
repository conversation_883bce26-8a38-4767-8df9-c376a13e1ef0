//package com.ruoyi.production.controller.api;
//
//import cn.hutool.http.HttpRequest;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.ruoyi.common.core.constant.ProductionConstans;
//import com.ruoyi.common.core.domain.R;
//import com.ruoyi.common.core.exception.CheckedException;
//import com.ruoyi.common.log.annotation.Log;
//import com.ruoyi.common.log.enums.BusinessType;
//import com.ruoyi.production.controller.BaseModuleController;
//import com.ruoyi.production.domain.BaseLineStorage;
//import com.ruoyi.production.domain.ProdWorkOrder;
//import com.ruoyi.production.domain.api.StartOrderEntity;
//import com.ruoyi.production.domain.vo.BaseLineStorageVo;
//import com.ruoyi.production.enums.WorkOrderStatusEnum;
//import com.ruoyi.production.service.IBaseLineStorageService;
//import com.ruoyi.production.service.IProdWorkOrderService;
//import com.ruoyi.production.service.ISysPlatformApiService;
//import com.ruoyi.production.utils.CallForcamEntityUtil;
//import io.swagger.annotations.Api;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.http.MediaType;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
//@Deprecated
//@RestController
//@Api(value = "开放给富堪的接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//@RequestMapping("api/forcam")
//public class ForcamCallMesController extends BaseModuleController {
//
//    @Autowired
//    private IProdWorkOrderService prodWorkOrderService;
//    @Autowired
//    private ISysPlatformApiService platformApiService;
//    @Autowired
//    private CallForcamEntityUtil callForcamEntityUtil;
//    @Autowired
//    private IBaseLineStorageService storageService;
//    @Autowired
//    private RedisTemplate redisTemplate;
//
//    @PostMapping(value = "startOrder", produces = {"application/json;charset=UTF-8"})
//    @ResponseBody
//    @Log(title = "富堪开工调用", businessType = BusinessType.OTHER)
//    public R<?> startOrder(@Validated @RequestBody StartOrderEntity entity) {
//
//        log.info("********** 富堪开工调用 >>> 查找工单-ID：" + entity.getOperationId());
//        //调用管控端接口开工，根据返回结果，调用富堪报工接口设置开工，插入跟踪记录
//        //根据operationId查找对应工单
//        ProdWorkOrder workOrder = prodWorkOrderService.getEntityByOperationId(entity.getOperationId());
//        if (null == workOrder)
//            throw new CheckedException("未查询到工单 " + entity.getOperationId());
//        log.info("********** 富堪开工调用 >>> 找到工单-工单号：" + workOrder.getTicketNumber() + ",工单状态 >>> " + workOrder.getStatus());
//        //查询五分钟内是否二次 成功开工
//        String startOrderKey = ProductionConstans.PROD_START_ORDER + entity.getOperationId();
//        Object o = redisTemplate.opsForValue().get(startOrderKey);
//        if (o != null) {
//            log.error("@@@@@@@@@@  此工单已在10分钟内二次开工 》》》 " + entity.getOperationId());
//            return R.fail("此工单已在10分钟内二次开工 》》》 " + entity.getOperationId());
//        }
//        //校验是否已完成加工
//        if (WorkOrderStatusEnum.DONE.getCode() == workOrder.getStatus()
//                || WorkOrderStatusEnum.INSTOCK.getCode() == workOrder.getStatus()) {
//            log.info("********** 富堪开工调用 >>> 工单状态无法开工：" + WorkOrderStatusEnum.getInfoByKey(workOrder.getStatus()));
//            return R.fail(">>>DCS系统中工单状态已完成，无法开工");
//        }
//
//        String apiCode = "iot_pushproc_" + workOrder.getLineId();
//        //获取产线号
//        String lineCode = workOrder.getLineCode();
//        //apiParam.setApiCode(apiCode.replace("line", "line" + lineCode));
//        //调用管控端参数查询
//        //管控端地址查询
//        String iotUrl = platformApiService.getEntityByCode(apiCode).getUrl();
//        //参数
//        BaseLineStorage storageParam = new BaseLineStorage();
//        storageParam.setOperationId(workOrder.getOperationId());
//        List<BaseLineStorageVo> storageList = storageService.listByCondition(storageParam);
//        if (storageList.size() < 1) {
//            log.info("********** 富堪开工调用 >>> 系统中未在线边库查询到工单，无法开工：" + workOrder.getTicketNumber());
//            return R.fail(">>>DCS系统中未在线边库查询到工单，无法开工 " + entity.getOperationId());
//        }
//
//        JSONObject iotParam = new JSONObject();
//
//        iotParam.put("procid", workOrder.getOperationId());
//        iotParam.put("subtime", new Date());
//        iotParam.put("starttime", null);
//        iotParam.put("endtime", null);
//        iotParam.put("objname", workOrder.getMatName());
//        iotParam.put("part-type", "mat");
//        iotParam.put("curpos", storageList.get(0).getAlias());
//        iotParam.put("target", Integer.parseInt(workOrder.getEqpCode()) + "");
//        iotParam.put("status", "waiting");
//
//        String result = HttpRequest.post(iotUrl).body(iotParam.toJSONString()).timeout(3000)
//                .execute().body();
//        log.info("********** mes开工调用 >>> 管控开工接口返回 >>> " + result);
//        JSONObject response = JSON.parseObject(result);
//        if (response.getInteger("code") == 0) {
//            redisTemplate.opsForValue().set(startOrderKey, result);
//            redisTemplate.expire(startOrderKey, 10, TimeUnit.MINUTES);
//        }
//
//        return R.ok(result);
////        return R.ok(result);
//    }
//
//
//}
