package com.ruoyi.production.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.service.ISysPlatformApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;

@Async("myTaskExecutor")
@Component
public class CallMesUtil {

    private static Logger log = LoggerFactory.getLogger(CallMesUtil.class);

    @Autowired
    private ISysPlatformApiService apiService;

    @Retryable(value = {SocketTimeoutException.class}, backoff = @Backoff(value = 10000, multiplier = 1.5))
    public void reportCompleted(ProdWorkOrder workOrder) {

        String mes_report = apiService.getEntityByCode("mes_finishOrder").getUrl();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operationId", workOrder.getOperationId());
        String result = HttpRequest.post(mes_report).timeout(3000)
                .body(jsonObject.toJSONString())
                .execute().body();
        log.info("********** >>>产线id：" + workOrder.getLineId() + ",工单id：" + workOrder.getOperationId() + ",完工报工返回：" + result);
    }

    @Retryable(value = {SocketTimeoutException.class}, backoff = @Backoff(value = 10000, multiplier = 1.5))
    public void requestMaterail(ProdWorkOrder workOrder) {
        String mes_postEdgeInfo = apiService.getEntityByCode("mes_postEdgeInfo").getUrl();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operationId", workOrder.getOperationId());
        jsonObject.put("lineCode", workOrder.getLineId());
        jsonObject.put("deviceCode", workOrder.getEqpCode());
        String result = HttpRequest.post(mes_postEdgeInfo).timeout(3000)
                .body(jsonObject.toJSONString())
                .execute().body();
        log.info("********** >>>产线id：" + workOrder.getLineId() + ",工单id：" + workOrder.getOperationId() + ",物料齐套返回：" + result);
    }

    //    @Deprecated
    @Retryable(value = {SocketTimeoutException.class}, backoff = @Backoff(value = 10000, multiplier = 1.5))
    public void restoreOrder(ProdWorkOrder workOrder) {

        if (null == workOrder || workOrder.getStatus() >= 4) {
            return;
        }
        String mes_restoreOrder = apiService.getEntityByCode("mes_restoreOrder").getUrl();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("refId", workOrder.getOperationId());
        String result = HttpRequest.post(mes_restoreOrder).timeout(3000)
                .body(jsonObject.toJSONString())
                .execute().body();
        log.info("********** >>> 重置mes工单状态id：" + workOrder.getLineId() + ",工单id：" + workOrder.getOperationId() + ",工单重置返回：" + result);
    }

    @Retryable(value = {SocketTimeoutException.class}, backoff = @Backoff(value = 10000, multiplier = 1.5))
    public void startOrder(ProdWorkOrder workOrder) {
        String mes_postEdgeInfo = apiService.getEntityByCode("mes_startOrder").getUrl();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", "0");
        jsonObject.put("userName", "管控系统");
        jsonObject.put("refId", workOrder.getOperationId());
        jsonObject.put("deviceCode", workOrder.getRealEqp());
        String result = HttpRequest.post(mes_postEdgeInfo).timeout(3000)
                .body(jsonObject.toJSONString())
                .execute().body();
        log.info("**********  >>> 产线id：" + workOrder.getLineId() + "工单id：" + workOrder.getOperationId() + ",开工通知mes返回：" + result);
    }
}
