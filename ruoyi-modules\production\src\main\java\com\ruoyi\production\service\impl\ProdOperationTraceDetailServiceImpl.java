package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.ProdOperationTraceDetail;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDetailDto;
import com.ruoyi.production.domain.vo.operation.ProdOperationTraceDetailVo;
import com.ruoyi.production.mapper.ProdOperationTraceDetailMapper;
import com.ruoyi.production.service.IProdOperationTraceDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-05-30
 */
@Service
public class ProdOperationTraceDetailServiceImpl extends ServiceImpl<ProdOperationTraceDetailMapper, ProdOperationTraceDetail> implements IProdOperationTraceDetailService {

    @Override
    public void saveDetailByOperation(ProdOperationTrace operationTrace) {
        ProdOperationTraceDetail detail = new ProdOperationTraceDetail();
        detail.setPid(operationTrace.getId());
//        detail.setEqpId(operationTrace.getEqpId());
        detail.setRemark(operationTrace.getRemark());
        detail.setStatus(operationTrace.getStatus());
        this.save(detail);
    }

    @Override
    public void deleteDetailByOperation(ProdOperationTrace operationTrace) {
        QueryWrapper<ProdOperationTraceDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid", operationTrace.getId());
        this.getBaseMapper().delete(queryWrapper);

    }

    @Override
    public List<ProdOperationTraceDetailVo> getDetailList(ProdOperationTraceDetailDto traceDetailDto) {
        return this.getBaseMapper().getDetailList(traceDetailDto);
    }
}
