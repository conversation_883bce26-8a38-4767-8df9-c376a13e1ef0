package com.ruoyi.production.enums;

/**
 * <AUTHOR>
 */

public enum EqpTypeEnum {

    MACHINE(0, "机床"),
    BOT(1, "机械手"),
    AGV(2, "AGV"),
    TRAY(3, "托盘位"),
    BENCH1(4, "预调台"),
    SHELVES(5, "线边货架位置");

    private final Integer code;
    private final String info;

    EqpTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(int code) {
        for (EqpTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type.getInfo();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
