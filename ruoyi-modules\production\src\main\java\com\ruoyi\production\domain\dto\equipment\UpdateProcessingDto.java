package com.ruoyi.production.domain.dto.equipment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UpdateProcessingDto {
    @ApiModelProperty(value = "设备ID")
    @NotNull(message = "设备id不可为空")
    private Long eqpId;
    @NotNull(message = "设备加工中值不可为空")
    @ApiModelProperty(value = "加工中")
    private boolean processing;
}
