package com.ruoyi.message.test;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class EmailListener implements ApplicationListener<EquipmentTestObservable> {
    @Override
    public void onApplicationEvent(EquipmentTestObservable event) {
        System.out.println(Thread.currentThread() + "用户下单成功！手机收到邮件!内容为{}" + event.getSource());
    }
}
