package com.ruoyi.production.core.thread;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.constant.ProductionConstans;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.SysPlatformApi;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IBaseLineStorageService;
import com.ruoyi.production.service.ISysPlatformApiService;
import com.ruoyi.production.utils.RabbitMsgUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

public class SyncLineEqpInfoThread implements Callable {

    private Long lineId;

    private static final Logger logger = LoggerFactory.getLogger(SyncLineEqpInfoThread.class);

    private static final ISysPlatformApiService apiService;
    private static final RedisService redisService;
    private static final IBaseEquipmentService equipmentService;
    private static RabbitMsgUtils rabbitMsgUtils;

    public SyncLineEqpInfoThread(Long lineId) {
        this.lineId = lineId;
    }

    static {
        apiService = SpringUtils.getBean(ISysPlatformApiService.class);
        redisService = SpringUtils.getBean(RedisService.class);
        equipmentService = SpringUtils.getBean(IBaseEquipmentService.class);
        rabbitMsgUtils = SpringUtils.getBean(RabbitMsgUtils.class);
    }

    @Override
    public Object call() {
        try {
            List<BaseEquipment> equipments;
            List<BaseEquipment> updateList = new ArrayList<>();
            Map<Integer, Map<String, Object>> map = new HashMap<>();
            //循环遍历机床信息
            BaseEquipment eqpParam = new BaseEquipment();
            eqpParam.setLineId(lineId);
            equipments = equipmentService.listByCondition(eqpParam);

            if (equipments == null || equipments.size() == 0) {
                return ">>>产线：" + lineId + " 无设备";
            }
            SysPlatformApi api = apiService.getEntityByCode("iot_lineStatus_" + lineId);
            if (null == api) {
                return ">>>产线：" + lineId + " 无同步设备状态接口地址";
            }
            String result = HttpRequest.post(api.getUrl())
                    .timeout(3000)
                    .execute().body();
            JSONObject response = JSON.parseObject(result);
//            logger.info("********** 同步PLC点位接口返回：" + result);

            for (BaseEquipment equipment : equipments) {
                BaseEquipment updateEqp = new BaseEquipment();
                updateEqp.setId(equipment.getId());
                if (response.containsKey(equipment.getAlias())) {
                    Map<String, Object> sonMap;
                    if (map.containsKey(equipment.getType())) {
                        sonMap = map.get(equipment.getType());
                    } else {
                        sonMap = new HashMap<>();
                    }
                    if (response.get(equipment.getAlias()) instanceof Boolean) {
                        if (response.getBoolean(equipment.getAlias())) {
                            updateEqp.setStatus(1);
                        } else {
                            updateEqp.setStatus(0);
                            //2023年3月17日 15:36:59 添加，机床离线，设置自动加工关闭
                            updateEqp.setAutoProcess(0);
                        }
                        //判断状态是否有变化才
                        if (!updateEqp.getStatus().equals(equipment.getStatus())) {
                            updateList.add(updateEqp);
                            logger.info("********* 状态同步 》》》 状态发生变化 :" + equipment.getEqpCode() + " " + updateEqp.getStatus());
                        }
                    }
                    sonMap.put(equipment.getAlias(), response.get(equipment.getAlias()));
                    map.put(equipment.getType(), sonMap);
                }
            }

            //有变化才保存,减少不必要数据库压力
            if (updateList.size() > 0) {
                equipmentService.updateBatchById(updateList);
                rabbitMsgUtils.updateProcessTraceList(lineId);
            }

            //2024年6月27日13:36:14 添加线边库位状态同步
            if(response.containsKey("box")){
                map.put(99,response.getJSONObject("box"));
            }



            //设置redis缓存
            for (Map.Entry<Integer, Map<String, Object>> entry : map.entrySet()) {
//                Map<String, Object> catchMap = redisService.getCacheObject(ProductionConstans.PROD_EQP_STAT + lineId + ":" + entry.getKey());
//                if(!catchMap.equals(entry.getValue())){
//                    redisService.setCacheMap(ProductionConstans.PROD_EQP_STAT + lineId + ":" + entry.getKey(), entry.getValue());
//                }
                redisService.setCacheMap(ProductionConstans.PROD_EQP_STAT + lineId + ":" + entry.getKey(), entry.getValue());
            }

        } catch (Exception e) {
//            e.printStackTrace();
            logger.error("@@@@@@@@@@ 产线id:" + lineId + " plc状态更新失败>>>" + e.getMessage());
            return "@@@@@@@@@@ 产线id:" + lineId + " plc状态更新失败>>>" + e.getMessage();
        }
        return "********** 产线id:" + lineId + " plc状态更新成功";
    }
}
