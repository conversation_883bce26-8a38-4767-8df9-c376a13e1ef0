package com.ruoyi.production.controller;


import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.production.domain.BaseFillerType;
import com.ruoyi.production.domain.BaseLineStorage;
import com.ruoyi.production.domain.vo.BaseLineStorageVo;
import com.ruoyi.production.enums.StorageFillerTypeEnum;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IBaseFillerTypeService;
import com.ruoyi.production.service.IBaseLineStorageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@Api(value = "线边库", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("lineStorage")
public class BaseLineStorageController extends BaseModuleController {

    @Autowired
    private IBaseLineStorageService baseLineStorageService;
    @Autowired
    private IBaseFillerTypeService fillerTypeService;

    @ApiOperation("查询线边库列表")
    @PostMapping("listByCondition")
    public AjaxResult listByCondition(@RequestBody BaseLineStorage storage) {
//        storage.setLineId(getLineId());
        List<BaseLineStorageVo> result = baseLineStorageService.listByCondition(storage);
        result.forEach(vo -> {
            String matType = vo.getMatType();
            if (null != matType) {
                vo.setMatTypeName(WorkOrderStatusEnum.getInfoByKey(Integer.parseInt(matType)));
            }
            BaseFillerType baseFillerType = fillerTypeService.getByFillerType(getLineId(), vo.getFillerType());
            if (null != baseFillerType) {
                vo.setFillerTypeName(baseFillerType.getFillerName());
            }

        });
        return AjaxResult.success(result);
    }

    @PostMapping
    @ApiOperation("创建线边库")
    public AjaxResult add(@RequestBody BaseLineStorage baseLineStorage) {
        return AjaxResult.success(baseLineStorageService.save(baseLineStorage));
    }

    @PutMapping("")
    @ApiOperation("更新线边库")
    @RequiresPermissions("lineStorage:edit")
    @Log(title = "线边库管理", businessType = BusinessType.UPDATE)
    public AjaxResult update(@RequestBody BaseLineStorageVo baseLineStorage) {
        log.info("*********修改线边库:" + baseLineStorage);
        baseLineStorage.setEmpty(baseLineStorage.getFillerType().equals(StorageFillerTypeEnum.EMPTY.getCode()) ? 1 : 0);
        baseLineStorage.setOperationId(baseLineStorage.getFillerType().equals(StorageFillerTypeEnum.PART.getCode()) ? baseLineStorage.getOperationId() : "");
        return AjaxResult.success(baseLineStorageService.updateLineStorageWithRealtion(baseLineStorage));
    }

}
