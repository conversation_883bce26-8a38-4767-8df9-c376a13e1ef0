# 基础镜像
FROM  openjdk:8-jre
# author
MAINTAINER ruoyi

# 挂载目录
VOLUME /home/<USER>
# 创建目录
RUN mkdir -p /home/<USER>
# 指定路径
WORKDIR /home/<USER>
# 复制jar文件到路径
COPY ./ruoyi-system.jar /home/<USER>/ruoyi-system.jar
# COPY ${JAR_FILE}.jar /home/<USER>/${JAR_FILE}.jar
# 启动网关服务
ENTRYPOINT ["java","-jar","ruoyi-system.jar"]
# ENTRYPOINT ["java","-jar","${JAR_FILE}.jar"]
# docker build --build-arg JAR_FILE=ruoyi-system -t ruoyi-system:v1