//package com.ruoyi.message.config;
//
//import com.ruoyi.message.listener.RedisKeyChangeListener;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.env.Environment;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.listener.PatternTopic;
//import org.springframework.data.redis.listener.RedisMessageListenerContainer;
//
//@Configuration
//public class RedisListenerConfig {
//
//    @Autowired
//    private RedisKeyChangeListener redisKeyChangeListener;
//
//    @Autowired
//    private Environment env;
//
//    @Bean
//    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
//        RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
//        redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
//        redisMessageListenerContainer.addMessageListener(redisKeyChangeListener, new PatternTopic("__keyspace@" + env.getProperty("spring.redis.database") + "__:ttttttttt"));
////        redisMessageListenerContainer.addMessageListener(redisKeyChangeListener, new PatternTopic("__keyevent@*"));
//        return redisMessageListenerContainer;
//
//    }
//
//}
