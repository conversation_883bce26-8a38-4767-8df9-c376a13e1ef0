package com.ruoyi.production.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.api.factory.RemoteEquipmentFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteEquipmentService", value = ServiceNameConstants.PRODUCTION_SERVICE, fallbackFactory = RemoteEquipmentFallbackFactory.class)
public interface RemoteEquipmentService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param mqttAlias 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/equipment/getEquipmentByMqttAlias/{mqttAlias}")
    public R<BaseEquipment> getEquipmentByMqttAlias(@PathVariable("mqttAlias") String mqttAlias, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
