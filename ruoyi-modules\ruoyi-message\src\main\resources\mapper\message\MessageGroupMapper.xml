<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.message.mapper.MessageGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.message.domian.MessageGroup">
    <result column="id" property="id" />
    <result column="remark" property="remark" />
    <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime" />
        <result column="group_name" property="groupName" />
        <result column="group_code" property="groupCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        remark,
        deleted,
        create_by, create_time, update_by, update_time, group_name, group_code
    </sql>

</mapper>
