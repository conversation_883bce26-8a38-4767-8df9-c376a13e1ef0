package com.ruoyi.production.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdMachineDetectionDetail;
import com.ruoyi.production.domain.dto.detection.AddDetailColumnDto;
import com.ruoyi.production.domain.dto.detection.DeleteDetailColumnDto;
import com.ruoyi.production.domain.dto.detection.QualityReportDataDto;
import com.ruoyi.production.domain.vo.detection.GetColumnVo;
import com.ruoyi.production.domain.vo.detection.ProdMachineDetectionDetailVo;
import com.ruoyi.production.domain.vo.detection.QualityReportDataVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
public interface IProdMachineDetectionDetailService extends IService<ProdMachineDetectionDetail> {

    /**
     * 根据条件查询信息
     *
     * @param detail
     * @return
     */
    List<ProdMachineDetectionDetailVo> listByCondition(ProdMachineDetectionDetail detail);

    IPage<ProdMachineDetectionDetailVo> listForPage(ProdMachineDetectionDetail detail, Integer current, Integer pageSiz);

    Boolean updateDetectionDetail(List<ProdMachineDetectionDetail> detailList);

    List<GetColumnVo> getColumns(String barcode);

    List<GetColumnVo> getColumnsByParam(QualityReportDataDto dto);

    QualityReportDataVo getQualityReportData(QualityReportDataDto dto);

    void exportQualityReportExcel(HttpServletResponse response,QualityReportDataDto dto);

    void addDetailColumn(AddDetailColumnDto dto);

    int deleteDetailColumn(DeleteDetailColumnDto dto);

}
