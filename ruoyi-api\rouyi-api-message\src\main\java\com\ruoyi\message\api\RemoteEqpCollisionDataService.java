package com.ruoyi.message.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.message.api.domain.EquipmentSpindleCollisionData;
import com.ruoyi.message.api.factory.RemoteEqpCollisionDataFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.HashMap;
import java.util.List;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteEqpCollisionDataService", value = ServiceNameConstants.MESSAGE_SERVICE, fallbackFactory = RemoteEqpCollisionDataFallbackFactory.class)
public interface RemoteEqpCollisionDataService
{
    @PostMapping("/collisionData/getCollisionDataListByMap")
    public R<List<EquipmentSpindleCollisionData>> getCollisionDataListByMap(@RequestBody HashMap params, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
