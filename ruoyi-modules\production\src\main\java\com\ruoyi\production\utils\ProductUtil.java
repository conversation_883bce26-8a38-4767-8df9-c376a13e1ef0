package com.ruoyi.production.utils;

import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.service.IProdWorkOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ProductUtil {

    @Autowired
    private IProdWorkOrderService orderService;

    List<ProdWorkOrderVo> getMachineTask(String startDate,String endDate,Long lineId){
        return null;
    }

    List<ProdWorkOrderVo> getMachineTodayTask(Long lineId){
        return this.getMachineTask(ProdDateUtil.getTodayStart(),ProdDateUtil.getTodayStart(),lineId);
    }
}
