package com.ruoyi.production.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@ApiModel(value = "ProdProcessTrace对象", description = "")
@Data
public class ProdProcessTrace extends MyBaseEntity {

    private Long lineId;

    private Long processId;

    private Long woId;

    @ApiModelProperty(value = "0执行中，1完成，2故障")
    private Integer status;

    private String stopReason;

    private String currentPosition;

    @ApiModelProperty(value = "全流程，预调操作记录流程")
    private Integer traceType;

    @ApiModelProperty(value = "设备code")
    private String eqpCode;

    private String matType;

    private String processCode;
    private String operationId;
    private String lineCode;
    private String cmd;
}
