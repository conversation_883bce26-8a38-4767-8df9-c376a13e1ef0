package com.ruoyi.production.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.core.observable.AutoProcessEvent;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.equipment.AutoProcessDto;
import com.ruoyi.production.domain.dto.equipment.UpdateProcessingDto;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.mapper.BaseEquipmentMapper;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IBaseLineStorageService;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.service.ISysPlatformApiService;
import io.seata.core.context.RootContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Service
public class BaseEquipmentServiceImpl extends ServiceImpl<BaseEquipmentMapper, BaseEquipment> implements IBaseEquipmentService {

    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private IBaseLineStorageService storageService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private ISysPlatformApiService apiService;

    @Override
    public List<BaseEquipment> listByCondition(BaseEquipment baseEquipment) {
        return this.getBaseMapper().listByCondition(baseEquipment);
    }

    @Override
    public BaseEquipment getEntityByCode(Long lineId, Integer type, String code) {
        QueryWrapper<BaseEquipment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("eqp_code", code);
        queryWrapper.eq("type", type);
        queryWrapper.eq("line_id", lineId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public BaseEquipment getEquipmentByMqttAlias(String mqttAlias) {
        QueryWrapper<BaseEquipment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mqtt_alias", mqttAlias);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public void checkMachMatNums(Long lineId, String eqpCode) {
        BaseEquipment eqpParam = new BaseEquipment();
        eqpParam.setLineId(lineId);
        eqpParam.setType(EqpTypeEnum.MACHINE.getCode());
        List<BaseEquipment> equipments = equipmentService.listByCondition(eqpParam);
        for (BaseEquipment equipment : equipments) {
            if (equipment.getEqpCode().equals(eqpCode)) {
                Integer nums = storageService.getRemainMatNums(equipment.getId());
                if (nums < 2) {
                    MessageNews messageNews = new MessageNews();
                    messageNews.setContent(equipment.getEqpCode() + "号机床仅剩" + nums + "件已装调件在线边库，请及时补料！");
                    messageNews.setCreaterId(1L);
                    messageNews.setLineId(lineId);
                    messageNews.setButtonGroupId(2L);
                    remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
                }
                break;
            }
        }
    }

    @Override
    public void autoProcess(AutoProcessDto dto) {
        BaseEquipment equipment = this.getById(dto.getEqpId());
        if (equipment.getStatus() == 0)
            throw new ServiceException("机床离线，无法设置自动加工属性");
        equipment.setAutoProcess(dto.isAutoProcess() ? 1 : 0);
        this.updateById(equipment);
        //触发自动加工流程---修改为开启自动加工
        if (dto.isAutoProcess())
            applicationContext.publishEvent(new AutoProcessEvent(this, equipment.getLineId()));
        RootContext.getXID();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessing(UpdateProcessingDto dto) {

        BaseEquipment equipment = this.getById(dto.getEqpId());
        if (equipment.getAutoProcess().equals(1) && !dto.isProcessing()) {
            throw new ServiceException("请先将机床自动加工关闭，再设置机床状态回退！");
        }
        BaseEquipment update = new BaseEquipment();
        update.setId(dto.getEqpId());
        update.setProcessing(dto.isProcessing() ? 1 : 0);
        this.updateById(update);
        //回退工单为待加工状态
        if (StringUtils.isNotBlank(equipment.getOperationId())){
            ProdWorkOrder order = orderService.getEntityByOperationId(equipment.getOperationId());
            //2023年7月14日 10:57:52 添加 删除梁慧工单接口，协同动作
            String delProcUrl = apiService.getEntityByCode("iot_delproc_"+order.getLineId()).getUrl();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("procid", order.getOperationId());
            String result = HttpRequest.post(delProcUrl).timeout(3000)
                    .body(jsonObject.toJSONString())
                    .execute().body();

            ProdWorkOrder updateOrder = new ProdWorkOrder();
            updateOrder.setDistributed(0);
            updateOrder.setId(order.getId());
            orderService.updateById(updateOrder);
        }


        //触发自动加工流程---修改为非加工中
//        if (!dto.isProcessing())
//            applicationContext.publishEvent(new AutoProcessEvent(this, equipment.getLineId()));

    }
}
