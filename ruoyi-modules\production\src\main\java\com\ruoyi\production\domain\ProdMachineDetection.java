package com.ruoyi.production.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
@ApiModel(value = "ProdMachineDetection对象", description = "")
public class ProdMachineDetection extends MyBaseEntity {

    private Long lineId;

    @ApiModelProperty(value = "0：超差，1：合格，2：未检测")
    private Integer status;

    @ApiModelProperty(value = "0：超差，1：合格，2：未自测")
    private Integer verifyStatus;

    @ApiModelProperty(value = "0：超差，1：合格，2：未专检")
    private Integer specialStatus;

    @ApiModelProperty(value = "在线检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    @ApiModelProperty(value = "自检检测时间")
    @TableField(value = "verify_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;

    private String operationId;

    @ApiModelProperty(value = "检测结果文件名")
    private String filename;

    @ApiModelProperty(value = "程序文件名")
    private String programName;

    @ApiModelProperty(value = "条形码base64")
    private String barcode;

    private String resultContent;

    private Integer printTag;

    @TableField(exist = false)
    private String operationSplit;
}
