//package com.ruoyi.production.utils;
//
//import cn.hutool.http.HttpRequest;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.ruoyi.production.domain.ProdWorkOrder;
//import com.ruoyi.production.domain.api.CallForcamReportEnity;
//import com.ruoyi.production.service.IProdWorkOrderService;
//import com.ruoyi.production.service.ISysPlatformApiService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
////@Async
//@Deprecated
//@Component
//public class CallForcamEntityUtil {
//
//    private static Logger log = LoggerFactory.getLogger(CallForcamEntityUtil.class);
//
//    @Autowired
//    private ISysPlatformApiService apiService;
//    @Autowired
//    private IProdWorkOrderService workOrderService;
//
//    @Deprecated
//    public String getForcamToken(Long lineId) {
//            String result = HttpRequest.post(apiService.getEntityByCode("forcam_auth_" + lineId).getUrl()).timeout(3000)
//                    .execute().body();
//            JSONObject jsonObject = JSON.parseObject(result);
//            //todo 确认key
//        return "Bearer " + jsonObject.getString("access_token");
//    }
//
//    public static CallForcamReportEnity reportProcessing(String operationId, String workplaceId) {
//        return new CallForcamReportEnity(operationId, workplaceId, "PROCESSING", "OPERATION_PHASE");
//    }
//
//    public static CallForcamReportEnity reportInterrupted(String operationId, String workplaceId) {
//        return new CallForcamReportEnity(operationId, workplaceId, "INTERRUPTED", "OPERATION_PHASE");
//    }
//
//    public CallForcamReportEnity reportCompleted(String operationId, String workplaceId,Long lineId) {
//        ProdWorkOrder workOrder = workOrderService.getEntityByOperationId(operationId);
//        CallForcamReportEnity callForcamReportEnity = new CallForcamReportEnity(operationId, workplaceId, "COMPLETED", "OPERATION_PHASE");
//
//        //过渡时期  何工mes和富勘共存  判断ticketnumber 是否包含冒号区分
//        if(null == workOrder || workOrder.getTicketNumber().contains(":"))
//            return callForcamReportEnity;
//
//        String token = this.getForcamToken(lineId);
//        String forcam_report = apiService.getEntityByCode("forcam_report_"+lineId).getUrl();
//        String result = HttpRequest.post(forcam_report).timeout(3000)
//                .header("Authorization", token)
//                .body(JSON.toJSONString(callForcamReportEnity))
//                .execute().body();
//        log.info("********** >>>完工报工返回：" + result);
//        return callForcamReportEnity;
//    }
//
//    public JSONObject reportQuantity(String operationId, String workplaceId,Long lineId) {
//
//        ProdWorkOrder workOrder = workOrderService.getEntityByOperationId(operationId);
//        //过渡时期  何工mes和富勘共存  判断ticketnumber 是否包含冒号区分
//        if(null == workOrder || workOrder.getTicketNumber().contains(":"))
//            return null;
//
//        String token = this.getForcamToken(lineId);
//        String forcam_report = apiService.getEntityByCode("forcam_report_"+lineId).getUrl();
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("operationId", operationId);
//        jsonObject.put("workplaceId", workplaceId);
//        jsonObject.put("qualityDetailId", "C7E58538901F4809BF0D58459C3B2AA5");
//        jsonObject.put("quantity", 1.0);
//        jsonObject.put("type", "OPERATION_QUANTITY");
//
//        String result = HttpRequest.post(forcam_report).timeout(3000)
//                .header("Authorization", token)
//                .body(jsonObject.toJSONString())
//                .execute().body();
//        log.info("********** >>>完工数量报工返回：" + result);
//        return jsonObject;
//    }
//
//    @Deprecated
//    public JSONObject requestMaterialCheck(String operationId, Boolean check, Long lineId) {
//        ProdWorkOrder workOrder = workOrderService.getEntityByOperationId(operationId);
//        //过渡时期  何工mes和富勘共存  判断ticketnumber 是否包含冒号区分
//        if(null == workOrder || workOrder.getTicketNumber().contains(":"))
//            return null;
//
//        String forcam_report = apiService.getEntityByCode("forcam_report_" + lineId).getUrl();
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("attributeName", "DB_OPERATION_USER_FIELD");
//
//        JSONObject attributeValue = new JSONObject();
//        attributeValue.put("type", "MAP");
//        JSONObject value = new JSONObject();
//        JSONObject fieldNumber = new JSONObject();
//        fieldNumber.put("value", "3");
//        fieldNumber.put("type", "STRING");
//        JSONObject content = new JSONObject();
//        if (check)
//            content.put("value", "true");
//        else
//            content.put("value", "false");
//        content.put("type", "STRING");
//        value.put("fieldNumber", fieldNumber);
//        value.put("content", content);
//
//        jsonObject.put("attributeValue", attributeValue);
//        attributeValue.put("value", value);
//
//        jsonObject.put("objectId", operationId);
//        jsonObject.put("domain", "OPERATION");
//        jsonObject.put("type", "DOMAIN_ATTRIBUTE_CHANGE");
//
//        //7777777
//        String result = HttpRequest.post(forcam_report).timeout(3000)
//                .header("Authorization", getForcamToken(lineId))
//                .body(jsonObject.toJSONString())
//                .execute().body();
//        log.info("********** >>> 通知富勘物料齐套接口调用结果:" + result);
//
//        return jsonObject;
//    }
//
//    @Deprecated
//    public JSONObject requesstUpdateCode(ProdWorkOrder workOrder
//            , String storageBin
//            , String storageLocation, Boolean reset, Long lineId) {
//
//        //过渡时期  何工mes和富勘共存  判断ticketnumber 是否包含冒号区分
//        if(null == workOrder || workOrder.getTicketNumber().contains(":"))
//            return null;
//
//        String forcam_report = apiService.getEntityByCode("forcam_updatecode_" + lineId).getUrl();
//        forcam_report = forcam_report.replace("{operationId}", workOrder.getOperationId())
//                .replace("{componentId}", workOrder.getComponentId());
//        JSONObject entity = new JSONObject();
//        //batch
//        JSONObject batch = new JSONObject();
//        batch.put("lotControlled", true);
//        batch.put("number", null);//库位 ??
//        entity.put("batch", batch);
//
//        //componentMaterial
//        JSONObject componentMaterial = new JSONObject();
//        componentMaterial.put("number", workOrder.getMaterialNumber());//物料编码
//        componentMaterial.put("type", null);
//        componentMaterial.put("group", null);
//
//        JSONObject description = new JSONObject();
//        JSONArray translations = new JSONArray();
//        JSONObject translation = new JSONObject();
//        translation.put("language", "en-US");
//        translation.put("translation", "input material");
//        translations.add(translation);
//        description.put("translations", translations);
//        componentMaterial.put("description", description);
//        entity.put("componentMaterial", componentMaterial);
//
//        entity.put("confirmationNumber", "string");
//
//        //customFields
//        JSONArray customFields = new JSONArray();
//        JSONObject customField = new JSONObject();
//        customField.put("id", "string");
//        customField.put("value", "string");
//        customFields.add(customField);
//        entity.put("customFields", customFields);
//
//        entity.put("quantity", 1);
//        entity.put("quantityUnit", "piece");
//        entity.put("positionNumber", workOrder.getOperationNumber());
//        entity.put("requirementDate", new Date());
//        entity.put("reservationNumber", workOrder.getOrderNumber() + "-" + workOrder.getOperationNumber() + "-" + workOrder.getWorkplaceName());
//        if (reset) {
//            entity.put("storageBin", "");
//            entity.put("storageLocation", "");
//        } else {
//            entity.put("storageBin", storageBin);
//            entity.put("storageLocation", storageLocation);
//        }
//
//        entity.put("traceNeeded", true);
//
//        //7777777
//        String result = HttpRequest.put(forcam_report)
//                .header("Authorization", getForcamToken(lineId))
//                .body(entity.toJSONString())
//                .execute().body();
//
//        log.info("********** >>> 更新工件编码接口调用结果:" + result);
//
//        return entity;
//    }
//
////    public void
//
//
//}
