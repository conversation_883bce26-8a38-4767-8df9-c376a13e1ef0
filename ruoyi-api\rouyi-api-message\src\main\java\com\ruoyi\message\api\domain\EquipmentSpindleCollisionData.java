package com.ruoyi.message.api.domain;

import com.ruoyi.common.core.web.domain.MyBaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Data
//@Builder
@ApiModel(value = "EquipmentSpindleCollisionData对象", description = "")
public class EquipmentSpindleCollisionData extends MyBaseEntity {

    private String coordinate;

    private String value;

    private String mach;

    private Date syncTime;
}
