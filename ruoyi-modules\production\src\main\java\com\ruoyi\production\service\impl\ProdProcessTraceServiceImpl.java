package com.ruoyi.production.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.BaseProcess;
import com.ruoyi.production.domain.ProdProcessTrace;
import com.ruoyi.production.domain.vo.BaseProcessVo;
import com.ruoyi.production.domain.vo.ProdProcessTraceVo;
import com.ruoyi.production.mapper.ProdProcessTraceMapper;
import com.ruoyi.production.service.IBaseProcessService;
import com.ruoyi.production.service.IProdProcessTraceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Service
public class ProdProcessTraceServiceImpl extends ServiceImpl<ProdProcessTraceMapper, ProdProcessTrace> implements IProdProcessTraceService {

    @Autowired
    IBaseProcessService processService;

    @Override
    public List<ProdProcessTraceVo> listByCondition(ProdProcessTrace processTrace) {
        return this.getBaseMapper().listByCondition(processTrace);
    }

    @Override
    public List<ProdProcessTraceVo> getProcessList(ProdProcessTrace processTrace) {

        Long lineId = processTrace.getLineId();
        processTrace.setLineId(lineId);

        BaseProcess processParam = new BaseProcess();
        processParam.setLineId(lineId);
        List<BaseProcess> processList = processService.getMainProcessLinksByLineId(lineId);
        List<ProdProcessTraceVo> prodProcessTraceList = this.listByCondition(processTrace);

        //流程链给节点添加status字段
        for (ProdProcessTraceVo trace : prodProcessTraceList) {
            Boolean nullFlag = false;
            List<BaseProcessVo> newProcessList = new ArrayList<>();
            if (null == trace.getProcessCode())
                nullFlag = true;
            //newProcessList.addAll(processList);
            //BeanUtils.copyProperties(processList, newProcessList);
            String status = "1";
            for (BaseProcess newProcess : processList) {
                BaseProcessVo newProcessVo = new BaseProcessVo();
                String traceCode = trace.getProcessCode();
                String newCode = newProcess.getProcessCode();
                newProcessVo.setProcessCode(newProcess.getProcessCode());
                newProcessVo.setProcessName(newProcess.getProcessName());
                if (nullFlag) {
                    newProcessVo.setStatus("");
                } else if (traceCode.equals(newCode)) {
                    newProcessVo.setStatus("2");
                    status = "";
                } else {
                    newProcessVo.setStatus(status);
                }
                newProcessList.add(newProcessVo);
            }
            trace.setNodes(newProcessList);
        }

        return prodProcessTraceList;
    }
}
