package com.ruoyi.production.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.dto.processTrace.ProdOperationTraceDetailDto;
import com.ruoyi.production.service.IProdOperationTraceDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "操作跟踪明细-控制器", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("operationTraceDetail")
public class ProdOperationTraceDetailController extends BaseModuleController {

    @Autowired
    private IProdOperationTraceDetailService operationTraceDetailService;

    @ApiOperation("查询操作记录详情列表")
    @PostMapping("listByCondition")
    public AjaxResult listByCondition(@RequestBody ProdOperationTraceDetailDto traceDetailDto) {
        return AjaxResult.success(operationTraceDetailService.getDetailList(traceDetailDto));
    }
}
