package com.ruoyi.production.domain.dto.detection;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class QualityStatisticsDto {

    @JsonFormat(pattern = "yyyy-MM-dd")
//    @NotBlank(message = "开始时间不能为空")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
//    @NotBlank(message = "结束时间不能为空")
    private Date endTime;

}
