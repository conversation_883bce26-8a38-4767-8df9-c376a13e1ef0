package com.ruoyi.production.core.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.message.api.RemoteMessageMqRecordService;
import com.ruoyi.message.api.domain.MessageMqRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@RabbitListener(queues = {"bot.direct.queue"})
public class DirectBotConsumer {

    @Autowired
    private RemoteMessageMqRecordService mqRecordService;

    Logger logger = LoggerFactory.getLogger(RemoteMessageMqRecordService.class);

    @RabbitHandler
    public void receiveMsg(String msg, Message message, Channel channel) throws IOException {
        logger.info("********** >>>开始消费消息：" + msg);
//        log.info("********** >>>message " + message);
//        log.info("********** >>>channel " + channel);

        MessageMqRecord mqRecord = JSON.toJavaObject(JSON.parseObject(msg), MessageMqRecord.class);
        try {
            mqRecord = mqRecordService.getByMesgId(mqRecord.getMessageId(), SecurityConstants.INNER).getData();
            if (null == mqRecord) {
                logger.error("@@@@@@@@@@ >>>消息不存在 " + msg);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            if (mqRecord.getConsumeStatus() == 1) {
                logger.warn("$$$$$$$$$$ >>>消息已消费,跳过 " + msg);
                return;
            }
            //最多重试三次
            if (mqRecord.getCount() > 1) {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),
                        false, false);
                mqRecord.setConsumeStatus(MessageMqRecord.TYPE_FAILED);
                return;
            }
//            throw new UnknownServiceException("111");

            //todo

            //逻辑走完
            mqRecord.setConsumeStatus(1);
//            Boolean result = mqRecordService.update(mqRecord, SecurityConstants.INNER).getData();
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            logger.info("********** >>>正常消费 ");
        } catch (Exception e) {
            e.printStackTrace();
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        } finally {
            if (mqRecord != null) {
                mqRecord.setCount(mqRecord.getCount() + 1);
                mqRecordService.updateByMesgId(mqRecord, SecurityConstants.INNER);
            }
        }
    }
}
