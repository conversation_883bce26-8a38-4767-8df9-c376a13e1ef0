package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.production.domain.ProdInstockTrace;
import com.ruoyi.production.mapper.ProdInstockTraceMapper;
import com.ruoyi.production.service.IProdInstockTraceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-05-19
 */
@Service
public class ProdInstockTraceServiceImpl extends ServiceImpl<ProdInstockTraceMapper, ProdInstockTrace> implements IProdInstockTraceService {

    @Override
    public ProdInstockTrace getEntityByOpId(String opid) {
        if (StringUtils.isBlank(opid))
            return null;
        QueryWrapper<ProdInstockTrace> queryWrapper = new QueryWrapper();
        queryWrapper.eq("instock_op_id", opid);
        return this.getBaseMapper().selectOne(queryWrapper);
    }
}
