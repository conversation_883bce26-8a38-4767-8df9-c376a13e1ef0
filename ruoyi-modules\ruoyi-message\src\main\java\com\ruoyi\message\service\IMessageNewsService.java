package com.ruoyi.message.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.message.api.domain.MessageNews;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface IMessageNewsService extends IService<MessageNews> {

    IPage<MessageNews> listForPage(MessageNews detail, Integer current, Integer pageSiz);

    List<MessageNews> getUnHandleList(MessageNews messageNews);

    void readMessage(Long id);

    JSONObject getPopNewsByLineId(Long id);

    Boolean reset(Long id);

}
