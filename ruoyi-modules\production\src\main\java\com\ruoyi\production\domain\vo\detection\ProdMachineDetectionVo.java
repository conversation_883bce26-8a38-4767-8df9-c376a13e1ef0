package com.ruoyi.production.domain.vo.detection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.production.domain.ProdMachineDetection;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProdMachineDetectionVo extends ProdMachineDetection {

    private Long eqpId;

    @ApiModelProperty(value = "产线号")
    private String lineCode;

    @ApiModelProperty(value = "机床号")
    private String eqpCode;

    @ApiModelProperty(value = "机床型号")
    private String eqpName;

    @ApiModelProperty(value = "控制系统")
    private String eqpSystem;

    @ApiModelProperty(value = "任务号")
    private String billCode;

    @ApiModelProperty(value = "工序号")
    private String schedulCode;

    @ApiModelProperty(value = "任务号+工序号")
    private String billSchedulCode;

    @ApiModelProperty(value = "物料号")
    private String matCode;

    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "物料id")
    private Integer matId;

//    @ApiModelProperty(value = "工单状态")
//    private Integer status;

    @ApiModelProperty(value = "工单号")
    private String ticketNumber;

    @ApiModelProperty(value = "实际加工开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realStartTime;

    @ApiModelProperty(value = "实际加工结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndTime;

    @ApiModelProperty(value = "托盘大小类型：big、small")
    private String bz1;

    @ApiModelProperty(value = "排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date schedulStartDate;

    @ApiModelProperty(value = "排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date schedulEndDate;

    @ApiModelProperty(value = "富堪工单唯一ID")
    private String operationId;

    @ApiModelProperty(value = "更新者名称")
    private String updateName;

    @ApiModelProperty(value = "真实加工机床")
    private String realEqp;

}
