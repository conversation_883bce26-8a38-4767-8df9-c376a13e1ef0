package com.ruoyi.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.message.api.domain.MessageMqRecord;
import com.ruoyi.message.mapper.MessageMqRecordMapper;
import com.ruoyi.message.service.IMessageMqRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-30
 */
@Service
public class MessageMqRecordServiceImpl extends ServiceImpl<MessageMqRecordMapper, MessageMqRecord> implements IMessageMqRecordService {

    @Override
    public List<MessageMqRecord> listByCondition(MessageMqRecord mqRecord) {
        QueryWrapper<MessageMqRecord> queryWrapper = new QueryWrapper<>();
        if (null != mqRecord.getMessage()) {
            queryWrapper.eq("message", mqRecord.getMessage());
        }
        if (null != mqRecord.getExchange()) {
            queryWrapper.eq("exchange", mqRecord.getExchange());
        }
        if (null != mqRecord.getRoutingkey()) {
            queryWrapper.eq("routingkey", mqRecord.getRoutingkey());
        }
        if (null != mqRecord.getId()) {
            queryWrapper.eq("id", mqRecord.getId());
        }
        if (null != mqRecord.getMessageId()) {
            queryWrapper.eq("message_id", mqRecord.getMessageId());
        }
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public int updateByMsgId(MessageMqRecord mqRecord) {
        QueryWrapper<MessageMqRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("message_id", mqRecord.getMessageId());
        return this.baseMapper.update(mqRecord, queryWrapper);
    }

    @Override
    public MessageMqRecord getByMsgId(String msgId) {
        QueryWrapper<MessageMqRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("message_id", msgId);
        return this.baseMapper.selectOne(queryWrapper);
    }

}
