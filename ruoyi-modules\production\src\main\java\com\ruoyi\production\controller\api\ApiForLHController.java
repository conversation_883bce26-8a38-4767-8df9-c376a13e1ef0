package com.ruoyi.production.controller.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.DictUtils;
import com.ruoyi.message.api.RemoteMessageMqRecordService;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.MessageMqRecord;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.core.observable.OperationDoneEvent;
import com.ruoyi.production.domain.*;
import com.ruoyi.production.domain.dto.AntiCollisionDto;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.dto.lineStorage.UpdateStorageByWODto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.domain.vo.TrayInfoVo;
import com.ruoyi.production.domain.vo.iot.WarnMsgVo;
import com.ruoyi.production.enums.*;
import com.ruoyi.production.service.*;
import com.ruoyi.production.utils.RabbitMsgUtils;
import com.ruoyi.production.utils.SyncEqpStatusUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.xml.bind.ValidationException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Api(value = "开放给管控端的接口", produces = MediaType.APPLICATION_JSON_VALUE)
@RequestMapping("api/control")
public class ApiForLHController {

    Logger log = LoggerFactory.getLogger(ApiForLHController.class);

    @Autowired
    private IProdProcessTraceService processTraceService;
    @Autowired
    private IProdOperationTraceService operationTraceService;
    @Autowired
    private IBaseProcessService processService;
    @Autowired
    private IBaseLineStorageService storageService;
    @Autowired
    private IProdWorkOrderService prodWorkOrderService;
    @Autowired
    private IBaseFillerTypeService fillerTypeService;
    @Autowired
    private IBaseLineService lineService;
    @Autowired
    private IBaseEquipmentService equipmentService;
    @Autowired
    private IBasePresetService presetService;
    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;
    @Autowired
    private RemoteMessageMqRecordService mqRecordService;
    @Autowired
    private SyncEqpStatusUtil syncEqpStatusUtil;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RabbitMsgUtils rabbitMsgUtils;

    /**
     * 新：生成加工流程记录，完工报工富勘
     *
     * @param response
     * @throws ValidationException
     */
//    @ApiOperation(value = "管控端同步车间工单状态")
//    @PostMapping(value = "/syncOrderStatus", produces = {"application/json;charset=UTF-8"})
    private void syncOrderStatus(@RequestBody JSONObject response) throws ValidationException {
//        log.info("********** 工单加工状态更新 >>> " + response.toJSONString());

        //工单id
        String refId = response.getString("procid");
        ProdWorkOrder workOrder = prodWorkOrderService.getEntityByOperationId(refId);
        if (null == workOrder) {
            throw new ServiceException("未查询到工单 " + refId);
        }
        //产线id
        Long lineId = workOrder.getLineId();
        //物料类别 mat 原料，pod 成品
        String objType = (response.containsKey("part-type")) ? response.getString("part-type") : response.getString("status");
        //当前位置
        String curPos = response.getString("curpos");
        //目标
        String target = response.getString("target");
        String bizType = "";

        if ("mat".equals(objType)) {
            if (target.contains("mach")) {
                bizType = "MatInMachine";
            }
//
//            if (curPos.contains("bench2"))
//                bizType = "AGV_MATERIAL_TRANSPORT";
//            else if (curPos.contains("box"))
//                bizType = "LINE_LIBRARY_MATERIAL_IN";
//            else if (curPos.contains("mat"))
//                bizType = "MatInMachine";//原料入机床毛坯缓存
        } else if ("pod".equals(objType)) {
            if (target.contains("box")) {
                bizType = "PodInLineStorage";
            }

//            if (curPos.contains("bench1"))
//                bizType = "AGV_PRODUCT_TRANSPORT";
//            else if (curPos.contains("bench2"))
//                bizType = "LINE_BRACKET_PRODUCT_IN";
//            else if (curPos.contains("box"))
//                bizType = "PodInLineStorage";//成品入线边库
//            else if (curPos.contains("pod"))
//                bizType = "PodInPodCache";//成品入机床成品缓存
//            else if (curPos.contains("mach"))
//                bizType = "MatInMachine";//原料入机床
        }
        if (StringUtils.isBlank(bizType)) {
            throw new ValidationException("bizType校验失败");
        }
        //查询产线加工流程是否为最终节点
        // 1.最终节点完工接口
        // 2.入库，更新线边库信息
        // 3.出库，更新线边库信息

        ProdProcessTrace processTrace = new ProdProcessTrace();
        //富勘工单id
        processTrace.setOperationId(refId);
        //流程代码
        processTrace.setProcessCode(bizType);
        //流程ID
        processTrace.setProcessId(processService.getEntityByProcessCode(lineId, bizType).getId());
        processTrace.setLineId(lineId);
        processTrace.setCreateTime(new Date());
        //当前最新位置
        processTrace.setCurrentPosition(curPos);
        //跟踪类型-2：工单跟踪
        processTrace.setTraceType(2);
        processTrace.setMatType(objType);
        //执行状态
        processTrace.setStatus(OrderTraceStatusEnum.DONE.getCode());
        //查找工单机床
        processTrace.setEqpCode(workOrder.getRealEqp());

        processTraceService.save(processTrace);

        //工单真实加工时间、结束时间添加
        ProdWorkOrder updateOrder = new ProdWorkOrder();
        updateOrder.setId(workOrder.getId());
        if (response.containsKey("starttime") && StringUtils.isNotBlank(response.getString("starttime"))) {
            updateOrder.setRealStartTime(response.getDate("starttime"));
        }

        if (response.containsKey("endtime") && StringUtils.isNotBlank(response.getString("endtime"))) {
            updateOrder.setRealEndTime(response.getDate("endtime"));
        }

        prodWorkOrderService.updateById(updateOrder);

    }

    @ApiOperation(value = "管控端同步操作状态")
    @PostMapping(value = "/syncOperationStatus", produces = {"application/json;charset=UTF-8"})
    public R<?> syncOperationStatus(@RequestBody JSONObject jsonObject) throws ValidationException {
        log.info("********** 操作状态回调 >>> " + jsonObject.toJSONString());

        //字段校验
        String operationId = "";
        if (jsonObject.containsKey("actid")) {
            operationId = jsonObject.getString("actid").split("-")[0];
        } else {
            throw new ValidationException("缺少操作记录id ");
        }

        ProdOperationTrace operationTrace = operationTraceService.getById(operationId);
        // 20220620 设置缓存

        // 20220620 结束
        if (null == operationTrace) {
            throw new ValidationException("未查询到操作操作记录单，id-" + operationId);
        }

        if (jsonObject.containsKey("message") && jsonObject.getString("message").contains("机械手忙碌"))
            return R.ok();

        String status = jsonObject.getString("status");
        operationTrace.setStatus(status);
        operationTrace.setRemark(jsonObject.getString("message"));
        operationTrace.setUpdateTime(new Date());
        operationTraceService.saveOrUpdateOperationTrace(operationTrace);

        if (LHOPStatusEnum.DONE.getCode().equals(status)) {
            applicationContext.publishEvent(new OperationDoneEvent(this, operationTrace));
        }
        //强制更新状态
        //同步产线托盘信息
        syncEqpStatusUtil.syncTrayInfo(operationTrace.getLineId());
        //同步产线设备状态
        syncEqpStatusUtil.syncEqpStatus(operationTrace.getLineId());
        return R.ok();
    }

    @ApiOperation(value = "机械手动作同步接口")
    @PostMapping(value = "/syncBotActionInfo", produces = {"application/json;charset=UTF-8"})
    @Log(title = "被调用--机械手动作同步接口", businessType = BusinessType.OTHER)
//    @Transactional(rollbackFor = Exception.class)
    public R<?> syncBotActionInfo(@RequestBody JSONObject source) throws Exception {
        //todo 此方法加@Transactional注解后工单状态不会变更，其他都正常，暂时无法查明原因
        log.info("********** 机械手动作同步接口 >>> " + source);

        //2022年11月14日 14:29:55 添加---修改同步托盘信息逻辑，此接口被调用就同步

        String to = source.getString("to");
        String from = source.getString("from");
        String wplid = source.getString("wplid");
        BaseLine baseLine = lineService.getEntityByAlias(wplid);
        if (null == baseLine) {
            log.error("@@@@@@@@@@  缺少关键字段产线id(wplid)");
            return R.fail("缺少关键字段产线id(wplid)");
        }
        try {
            Long lineId = baseLine.getId();
            //判断 to 和 from 是否包含 线边库
            BaseLineStorage fromStorange = storageService.getEntityByAlias(lineId, from);
            BaseLineStorage toStorange = storageService.getEntityByAlias(lineId, to);
            //判断 to 和 from 是否包含 机床
            BaseEquipment fromMach = equipmentService.getEntityByCode(lineId, EqpTypeEnum.MACHINE.getCode(), from);
            BaseEquipment toMach = equipmentService.getEntityByCode(lineId, EqpTypeEnum.MACHINE.getCode(), to);
            //判断 to 是否包含 预调台
            BasePreset basePreset = presetService.getEntityByAlias(to);
            ProdWorkOrder order = null;

            if (fromStorange == null && toStorange == null) {
                //物料/托盘 到预调台
                if (basePreset != null) {

                    ProdOperationTrace operationTrace = operationTraceService.getEntityByLineAndProcess(basePreset.getLineId(), 20L);
                    TrayInfoVo trayInfoVo = JSON.toJavaObject(JSON.parseObject(operationTrace.getOperationData()), TrayInfoVo.class);
//                log.info("********** >>> 操作台到货数据" + JSON.toJSONString(trayInfoVo));

                    //弹窗提示物料 到位
                    if (StorageFillerTypeEnum.PART.getCode().equals(trayInfoVo.getFillerType())) {
                        MessageNews messageNews = new MessageNews();
                        messageNews.setContent(trayInfoVo.getTicketNumber() + " / " + trayInfoVo.getMatName());
                        messageNews.setCreaterId(1L);
                        messageNews.setLineId(basePreset.getLineId());
                        messageNews.setOperationId(trayInfoVo.getOperationId());
                        order = prodWorkOrderService.getEntityByOperationId(trayInfoVo.getOperationId());
                        if (order != null && order.getStatus() > 2) {
                            messageNews.setButtonGroupId(1L);
                        } else {
                            messageNews.setButtonGroupId(2L);
                        }

                        remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
                    } else {
                        MessageNews messageNews = new MessageNews();
                        messageNews.setContent("托盘已到达装调站，请及时处理！");
                        messageNews.setCreaterId(1L);
                        messageNews.setLineId(basePreset.getLineId());
                        messageNews.setButtonGroupId(2L);
                        remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
                    }
                    return R.ok();
                }
                return R.ok();
            }

            JSONObject obj = source.getJSONObject("obj");
            JSONObject tray = obj.getJSONObject("tray");
            JSONObject part = obj.getJSONObject("part");
            String objtype = obj.getString("objtype");
            if ("part".equals(objtype) && order == null) {
                order = prodWorkOrderService.getEntityByOperationId(part.getString("procid"));
            }

            //优化开始
            UpdateStorageByWODto dto = new UpdateStorageByWODto();
            dto.setFrom(fromStorange);
            dto.setTo(toStorange);
            dto.setLineId(lineId);
            //优化结束

            //出线边库
            if (fromStorange != null) {
                //出线边库-去机床，生成加工记录
                if (toMach != null) {
                    String target = part.getString("target");
                    part.put("target", "mach" + target);
                    part.put("starttime", new Date());
                    this.syncOrderStatus(part);
                }
                if (null != part) {
                    //判断 毛坯出库去机床 和
                    String partype = (part.containsKey("part-type")) ? part.getString("part-type") : part.getString("status");

                    BaseFillerType fillerType = fillerTypeService.getByAlias(lineId, "part");
                    dto.setWorkOrder(order);
                    dto.setFillerType(fillerType.getFillerType());
                    //成品出库回预调间
                    //添加工人误操作取毛坯的情况
                    if (LHMatTypeEnum.MAT.getCode().equals(partype) && toMach != null) {
                        //毛坯出库去机床
                        order.setStatus(WorkOrderStatusEnum.DOING.getCode());
                        prodWorkOrderService.updateWorkOrderWithStatus(order);
                    }
                    //检查产线机床毛坯料剩余情况
                    //自动加工注销 2023-03-30 17:25:57
//                equipmentService.checkMachMatNums(lineId, order.getEqpCode());
                    storageService.checkWorkOrderNums(lineId, order);
                }

                //入线边库
            } else {
                //入线边库-托盘
                if (null != tray) {
                    BaseFillerType fillerType = fillerTypeService.getByAlias(lineId, tray.getString("trayid"));
                    dto.setFillerType(fillerType.getFillerType());
                }
                //物料加工判断 生成加工记录
                if (null != part) {
                    //判断毛坯入库和成品入库
                    String partype = part.getString("part-type");

                    BaseFillerType fillerType = fillerTypeService.getByAlias(lineId, "part");
                    dto.setWorkOrder(order);
                    dto.setFillerType(fillerType.getFillerType());

                    if (LHMatTypeEnum.POD.getCode().equals(partype)) {
                        //成品入库
                        order.setStatus(WorkOrderStatusEnum.DONE.getCode());
                        prodWorkOrderService.updateWorkOrderWithStatus(order);
                        //解除绑定机床状态
                        if (fromMach != null) {
                            BaseEquipment update = new BaseEquipment();
                            update.setId(fromMach.getId());
                            update.setProcessing(0);
                            equipmentService.updateById(update);
                            log.info("********** 设置机床 未加工 中：" + fromMach.getEqpCode());
                        }
//                    applicationContext.publishEvent(new OrderDoneEvent(this, order));
                    } else {
                        //毛坯入库
                        //2023-02-15 添加判断 已是成品状态不修改--原因：误操作将成品拉回线边
                        if (!order.getStatus().equals(WorkOrderStatusEnum.DONE.getCode())) {
                            order.setStatus(WorkOrderStatusEnum.WAIT.getCode());
                        }
                        prodWorkOrderService.updateWorkOrderWithStatus(order);
//                    applicationContext.publishEvent(new OrderWaitEvent(this, order));
                    }
//                prodWorkOrderService.updateById(order);

                    //出机床  车间加工回调 ， 判断to、from是否包含机床code
                    if (fromMach != null) {
                        String target = part.getString("target");
                        part.put("target", "box" + target);
                        part.put("endtime", new Date());
                        this.syncOrderStatus(part);
                    }
                }
            }
            storageService.updateLineStorage(dto);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //强制更新状态
            //同步产线托盘信息
            syncEqpStatusUtil.syncTrayInfo(baseLine.getId());
            //同步产线设备状态
            syncEqpStatusUtil.syncEqpStatus(baseLine.getId());
            //更新产线状态列表
            rabbitMsgUtils.updateProcessTraceList(baseLine.getId());
        }
        return R.ok();
    }

    @ApiOperation(value = "机械手动作同步接口")
    @PostMapping(value = "/syncBotActionInfoTest", produces = {"application/json;charset=UTF-8"})
    public R<?> syncBotActionInfoTest(@RequestBody JSONObject source) throws Exception {
        log.info("********** 机械手动作同步接口 >>> " + source);

        MessageMqRecord mqRecord = new MessageMqRecord();
        mqRecord.setExchange("direct_bot_exchange");
        mqRecord.setRoutingkey("bot");
        mqRecord.setMessage(source.toJSONString());
        mqRecord.setCount(0);
        mqRecord.setMessageId(UUID.randomUUID().toString());

//        List<MessageMqRecord> mqRecordList = mqRecordService.list(mqRecord, SecurityConstants.INNER).getData();
//        if (null != mqRecordList && mqRecordList.size() > 0) {
//            return R.ok();
//        }

        Boolean save = mqRecordService.add(mqRecord, SecurityConstants.INNER).getData();
        if (!save) {
            throw new ServiceException("mq记录保存失败");
        }
        rabbitTemplate.convertAndSend("direct_bot_exchange", "bot", JSON.toJSONString(mqRecord));
        return R.ok();
    }

    /**
     * 提供的数据是报警数据，不包含false数据
     *
     * @param source
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "报警信息同步接口")
    @PostMapping(value = "/syncErrorMsg", produces = {"application/json;charset=UTF-8"})
    public R<?> syncErrorMsg(@RequestBody JSONArray source) throws Exception {

        List<WarnMsgVo> allList = new ArrayList<>();

        for (int i = 0; i < source.size(); i++) {
            JSONObject jsonObject = source.getJSONObject(i);
            for (String code : jsonObject.keySet()) {
                WarnMsgVo warn = new WarnMsgVo();
                warn.setCode(code);
                allList.add(warn);
            }
        }

        //获取所有产线
        List<BaseLine> lineList = lineService.getDistinctLineList();

        //所有产线的特殊报警数组准备
        List<WarnMsgVo> roomBotList = new ArrayList<>();
        //拆分产线，并添加中文提示
        Map<String, List<WarnMsgVo>> map = new HashMap();
        StringBuilder machKeyBuilder = new StringBuilder();
        StringBuilder roomKeyBuilder = new StringBuilder();

        for (BaseLine baseLine : lineList) {
            List<WarnMsgVo> lineWarnList = new ArrayList<>();
            for (int i = 0; i < allList.size(); i++) {
                WarnMsgVo o = allList.get(i);
                String code = o.getCode();
                String[] codeStr = code.split("-");
                //翻译产线报警
                if (code.startsWith("opas")) {
                    //翻译预调室报警
                    if (code.contains("bot")) {
                        roomKeyBuilder.append("bot");
                        //预调室机械手报警
                        for (int k = 2; k < codeStr.length; k++) {
                            roomKeyBuilder.append("-").append(codeStr[k]);
                        }
                        o.setText(DictUtils.getDictLabele("warn_opas", roomKeyBuilder.toString()));
                        if (!roomBotList.contains(o)) {
                            roomBotList.add(o);
                        }
                    } else if (code.contains("wbc")) {
                        roomKeyBuilder.append("wbc");
                        for (int k = 2; k < codeStr.length; k++) {
                            roomKeyBuilder.append("-").append(codeStr[k]);
                        }
                        //预调台报警
                        String wbc = codeStr[1].replace("wbc", "");
                        o.setText(DictUtils.getDictLabele("warn_opas", roomKeyBuilder.toString()) + wbc);
                        //判断预调台和产线code是否相同
                        if (wbc.equals(baseLine.getLineCode())) {
                            lineWarnList.add(o);
                        }
                    } else {
                        //todo 中文直接 拼接
                        if (codeStr.length == 2) {
                            o.setText(codeStr[1]);
                            lineWarnList.add(o);
                        }
                    }
                    roomKeyBuilder.setLength(0);
                } else if (baseLine.getAlias().equals(codeStr[0])) {
                    //todo 中文直接 拼接
                    if (codeStr.length == 2) {
                        o.setText(codeStr[1]);
                        lineWarnList.add(o);
                    } else {
                        machKeyBuilder.append("mach");
                        String machCode = codeStr[1].replace("mach", "");
                        for (int k = 2; k < codeStr.length; k++) {
                            machKeyBuilder.append("-").append(codeStr[k]);
                        }
                        o.setText(machCode + "号" + DictUtils.getDictLabele("warn_mach", machKeyBuilder.toString()));
                        lineWarnList.add(o);
                        machKeyBuilder.setLength(0);
                    }
                }
            }
            map.put(baseLine.getLineCode(), lineWarnList);
        }
        //特殊报警处理
        if (roomBotList.size() > 0) {
            for (BaseLine baseLine : lineList) {
                if (map.containsKey(baseLine.getLineCode())) {
                    List<WarnMsgVo> list = map.get(baseLine.getLineCode());
                    list.addAll(roomBotList);
                    map.put(baseLine.getLineCode(), list);
                } else {
                    map.put(baseLine.getLineCode(), roomBotList);
                }
            }
        }

        for (String s : map.keySet()) {
            WebSocketVo webSocketVo = new WebSocketVo();
            JSONObject jsonObject = new JSONObject();
            List<WarnMsgVo> warnMsgVoList = map.get(s);
            List<WarnMsgVo> oldList = redisService.getCacheObject("warn_msg:" + s);
            if (null == oldList || oldList.size() == 0) {
                if (warnMsgVoList.size() > 0) {
                    jsonObject.put("list", warnMsgVoList);
                }
            } else {
                if (warnMsgVoList.size() > 0) {
                    jsonObject.put("list", warnMsgVoList);
                } else {
                    jsonObject.put("list", new ArrayList<>());
                }
            }
            if (jsonObject.containsKey("list")) {
                webSocketVo.setResponse(jsonObject);
                webSocketVo.setLineId(Long.parseLong(s));
                rabbitTemplate.convertAndSend("direct_interface_exchange", "warn", JSON.toJSONString(webSocketVo));
                redisService.setCacheObject("warn_msg:" + s, jsonObject.get("list"));
            }

        }
        return R.ok();
    }

    /**
     * 提供的数据是报警数据，不包含false数据
     *
     * @param source
     * @return
     * @throws Exception
     *//*
    @ApiOperation(value = "报警信息同步接口")
    @PostMapping(value = "/syncErrorMsg", produces = {"application/json;charset=UTF-8"})
    public R<?> syncErrorMsg(@RequestBody JSONArray source) throws Exception {
        //每次处理前将错误信息格式化为实体
        List<WarnMsgVo> sourceList = new ArrayList<>();

        for (int i = 0; i < source.size(); i++) {
            JSONObject jsonObject = source.getJSONObject(i);
            for (String code : jsonObject.keySet()) {
                WarnMsgVo warn = new WarnMsgVo();
                warn.setCode(code);
                sourceList.add(warn);
            }
        }

        //判断报警是否相同，相同则跳过
        if (!redisService.hasKey("warn_msg") && source.size() == 0) {
            return R.ok("无报警");
        }

        //第一次加载不存在key
        if (!redisService.hasKey("warn_msg")) {
            //直接发送
            return R.ok(handleWarnMsgToMap(new ArrayList<>(), sourceList, new ArrayList<>()));
        }
        //过滤上次为空的
        List<WarnMsgVo> oldList = redisService.getCacheList("warn_msg");
        List<String> sourceCodeList = sourceList.stream().map(WarnMsgVo::getCode).collect(Collectors.toList());
        List<String> oldCodeList = oldList.stream().map(WarnMsgVo::getCode).collect(Collectors.toList());

        //获取上次报警，这次不存在的报警
        List<WarnMsgVo> expiredList = oldList.stream().filter(old -> !sourceCodeList.contains(old.getCode())).collect(Collectors.toList());
        //获取新的报警
        List<WarnMsgVo> newWarnList = sourceList.stream().filter(n -> {
            return !oldCodeList.contains(n.getCode());
        }).collect(Collectors.toList());
        //获取上次和这次都存在的报警
        List<WarnMsgVo> continuedList = oldList.stream().filter(old -> sourceCodeList.contains(old.getCode())).collect(Collectors.toList());
        //websocket发送消息 todo

//        return R.ok(handleWarnMsgToMap(continuedList, newWarnList, new ArrayList<>()));
        return R.ok(handleWarnMsgToMap(continuedList, newWarnList, expiredList));
    }*/
    private Map handleWarnMsgToMap(List<WarnMsgVo> continued, List<WarnMsgVo> n, List<WarnMsgVo> expired) {
        //获取所有产线
        List<BaseLine> lineList = lineService.getDistinctLineList();
        //报警次数处理
        continued.stream().forEach(o -> {
            o.setCount(o.getCount() + 1);
        });
        expired.stream().forEach(o -> {
            o.setCount(0);
        });
        n.stream().forEach(o -> {
            o.setCount(1);
        });
        //拼接所有数组
        List<WarnMsgVo> allList = new ArrayList<>(continued);
        allList.addAll(n);
//        allList.addAll(expired);
        //所有产线的特殊报警数组准备
        List<WarnMsgVo> roomBotList = new ArrayList<>();
        //拆分产线，并添加中文提示
        Map<String, List<WarnMsgVo>> map = new HashMap();
        StringBuilder machKeyBuilder = new StringBuilder();
        StringBuilder roomKeyBuilder = new StringBuilder();

        for (BaseLine baseLine : lineList) {
            List<WarnMsgVo> lineWarnList = new ArrayList<>();
            for (int i = 0; i < allList.size(); i++) {
                WarnMsgVo o = allList.get(i);
                String code = o.getCode();
                String[] codeStr = code.split("-");
                //翻译产线报警
                if (code.startsWith("opas")) {
                    //翻译预调室报警
                    if (code.contains("bot")) {
                        roomKeyBuilder.append("bot");
                        //预调室机械手报警
                        for (int k = 2; k < codeStr.length; k++) {
                            roomKeyBuilder.append("-").append(codeStr[k]);
                        }
                        o.setText(DictUtils.getDictLabele("warn_opas", roomKeyBuilder.toString()));
                        if (!roomBotList.contains(o)) {
                            roomBotList.add(o);
                        }
                    } else {
                        roomKeyBuilder.append("wbc");
                        for (int k = 2; k < codeStr.length; k++) {
                            roomKeyBuilder.append("-").append(codeStr[k]);
                        }
                        //预调台报警
                        String wbc = codeStr[1].replace("wbc", "");
                        o.setText(DictUtils.getDictLabele("warn_opas", roomKeyBuilder.toString()) + wbc);
                        //判断预调台和产线code是否相同
                        if (wbc.equals(baseLine.getLineCode())) {
                            lineWarnList.add(o);
                        }
                    }
                    roomKeyBuilder.setLength(0);
                } else if (baseLine.getAlias().equals(codeStr[0])) {
                    machKeyBuilder.append("mach");
                    String machCode = codeStr[1].replace("mach", "");
                    for (int k = 2; k < codeStr.length; k++) {
                        machKeyBuilder.append("-").append(codeStr[k]);
                    }
                    o.setText(machCode + "号" + DictUtils.getDictLabele("warn_mach", machKeyBuilder.toString()));
                    lineWarnList.add(o);
                    machKeyBuilder.setLength(0);
                }
            }
            map.put(baseLine.getLineCode(), lineWarnList);
        }
        //特殊报警处理
        if (roomBotList.size() > 0) {
            for (BaseLine baseLine : lineList) {
                if (map.containsKey(baseLine.getLineCode())) {
                    List<WarnMsgVo> list = map.get(baseLine.getLineCode());
                    list.addAll(roomBotList);
                    map.put(baseLine.getLineCode(), list);
                } else {
                    map.put(baseLine.getLineCode(), roomBotList);
                }
            }
        }

        //redis直接保存传来的错误信息
        redisService.deleteObject("warn_msg");
        if (allList.size() != 0) {
            redisService.setCacheList("warn_msg", allList);
        }

        //向队列发送websocket消息
        //判断报警是否相同
//        if (n.size() == 0 && expired.size() == 0) {
//            return map;
//        }
        for (String s : map.keySet()) {
            WebSocketVo webSocketVo = new WebSocketVo();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("list", map.get(s));
//            if (map.get(s) != null && map.get(s).size() > 0) {
//                webSocketVo.setResponse(jsonObject);
//                webSocketVo.setLineId(Long.parseLong(s));
//                rabbitTemplate.convertAndSend("direct_interface_exchange", "warn", JSON.toJSONString(webSocketVo));
//            }
            webSocketVo.setResponse(jsonObject);
            webSocketVo.setLineId(Long.parseLong(s));
            rabbitTemplate.convertAndSend("direct_interface_exchange", "warn", JSON.toJSONString(webSocketVo));
        }

        return map;
    }

    @ApiOperation("主轴防碰撞")
    @PostMapping("antiCollision")
    public R<?> antiCollision(@Validated @RequestBody AntiCollisionDto antiCollisionDto) throws Exception {

        //循环遍历机床信息
        BaseEquipment eqpParam = new BaseEquipment();
        eqpParam.setLineId(5L);
        eqpParam.setType(EqpTypeEnum.MACHINE.getCode());
        List<BaseEquipment> equipments = equipmentService.listByCondition(eqpParam);
        equipments = equipments.stream().filter(e -> e.getEqpCode().equals(antiCollisionDto.getEqpCode())).collect(Collectors.toList());
        if (equipments.size() == 0)
            return R.fail("机床不存在");

        float x = Float.parseFloat(antiCollisionDto.getX());
        float z = Float.parseFloat(antiCollisionDto.getZ());
        float y = Float.parseFloat(antiCollisionDto.getY());

        if (x < 1 || x > 4)
            return R.ok(true, "警报，x轴加速度超过阈值");

        if (z < 1 || z > 4)
            return R.ok(true, "警报，z轴加速度超过阈值");

        if (y < 4 || y > 6)
            return R.ok(true, "警报，y轴加速度超过阈值");

        return R.ok(false, "正常");
    }

    @ApiOperation("根据产线id获取当前正在加工的工单")
    @GetMapping("getDoingOrders/{lineId}")
    @Log(title = "被调用--根据产线id获取当前正在加工的工单", businessType = BusinessType.OTHER)
    public R<?> getDoingOrders(@PathVariable("lineId") Long lineId) throws Exception {
        ProdWorkOrderDto dto = new ProdWorkOrderDto();
        List<String> stringList = new ArrayList<>();
        stringList.add("3");
        stringList.add("4");
        dto.setStatusList(stringList);
        dto.setDistributed(1);
        dto.setLineId(lineId);
        List<ProdWorkOrderVo> orderVos = prodWorkOrderService.listByCondition(dto);
        List<JSONObject> jsonObjects = new ArrayList<>();
        for (ProdWorkOrderVo orderVo : orderVos) {
            JSONObject iotParam = new JSONObject();
            iotParam.put("procid", orderVo.getOperationId());
            iotParam.put("subtime", new Date());
            iotParam.put("starttime", null);
            iotParam.put("endtime", null);
            iotParam.put("objname", orderVo.getMatName());
            iotParam.put("part-type", "pod");
            iotParam.put("curpos", orderVo.getRealEqp());
            iotParam.put("target", "box?");
            iotParam.put("status", "waiting");
            jsonObjects.add(iotParam);
        }
        return R.ok(jsonObjects);
    }

    @ApiOperation("根据产线ID和线边库alias获取本系统的库位信息")
    @GetMapping("getBoxInfo/{lineId}/{alias}")
    public R<?> getBoxInfo(@PathVariable("lineId") Long lineId, @PathVariable("alias") String alias) throws Exception {
        QueryWrapper<BaseLineStorage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("line_id", lineId);
        queryWrapper.eq("alias", alias);
        List<BaseLineStorage> list = storageService.list(queryWrapper);
        if (list.size() != 1)
            return R.fail("输入的参数有误");
        Boolean empty = list.get(0).getEmpty() == 1 ? false : true;
        return R.ok(empty);
    }
}
