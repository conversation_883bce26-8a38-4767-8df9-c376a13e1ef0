package com.ruoyi.production.domain.dto.processTrace;

import com.ruoyi.production.domain.ProdOperationTraceDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProdOperationTraceDetailDto extends ProdOperationTraceDetail {

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("操作单父id集合")
    private List pidList;
}
