package com.ruoyi.production.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.dto.operation.InOutStockDto;
import com.ruoyi.production.service.ISysPlatformApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@Api(value = "入库接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("api")
public class OutStockController {

    @Autowired
    private ISysPlatformApiService apiService;

    @GetMapping("outstockLOT")
    public AjaxResult LineOneOutStock(
            @ApiParam("任务号") @RequestParam("billCode") String billCode
            , @ApiParam("工序号") @RequestParam("schedulCode") String schedulCode
            , @ApiParam("工序状态") @RequestParam("processStatus") String processStatus) {

        JSONObject requestParam = new JSONObject();
        requestParam.put("reqid", billCode);
        requestParam.put("process", schedulCode);
        requestParam.put("processStatus", processStatus);
        String url = apiService.getEntityByCode("wms_outStockCreate").getUrl();
        String result = HttpRequest.post(url).body(requestParam.toJSONString()).timeout(3000)
                .execute().body();  //todo 暂时无法调用

        return AjaxResult.success();
    }

    @ApiOperation("查询wms库存信息")
    @PostMapping("searchInventory")
    public AjaxResult searchInventory(@RequestBody InOutStockDto stockDto) {
        JSONObject requestParam = new JSONObject();
        requestParam.put("reqid", stockDto.getReqid());
        requestParam.put("process", stockDto.getProcess());
        String url = apiService.getEntityByCode("wms_searchInventory").getUrl();
        String result = HttpRequest.post(url).body(requestParam.toJSONString()).timeout(3000)
                .execute().body();

        JSONObject resultObj = JSON.parseObject(result);
        JSONArray jsonArray = resultObj.getJSONArray("data");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            JSONArray items = item.getJSONArray("items");
            for (int j = 0; j < items.size(); j++) {
                JSONObject stock = items.getJSONObject(j);
                if (0 != stock.getInteger("itemStatus") || 0 != stock.getInteger("processStatus"))
                    items.remove(stock);
            }
        }
        return AjaxResult.success(jsonArray);
    }

}
