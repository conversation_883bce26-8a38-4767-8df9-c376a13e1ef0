package com.ruoyi.production.core.listener;

import com.ruoyi.production.core.observable.OperationDoneEvent;
import com.ruoyi.production.utils.SyncEqpStatusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class OperationDoneRoomPLCListener implements ApplicationListener<OperationDoneEvent> {

    @Autowired
    private SyncEqpStatusUtil syncEqpStatusUtil;

    @Override
    public void onApplicationEvent(OperationDoneEvent event) {
        //关联同步预调室点位信息
        syncEqpStatusUtil.syncEqpStatus(11L);
    }
}
