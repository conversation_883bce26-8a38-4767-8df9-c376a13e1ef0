package com.ruoyi.production.domain.vo.detection;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QualityReportColumnMetaDataVo {
    @ApiModelProperty("在线检测主id")
    private Long pid;
    @ApiModelProperty("详情id")
    private Long id;
    @ApiModelProperty(value = "机内检测数值")
    private String realValue;
    @ApiModelProperty(value = "机内检测结果（0：超差，1：合格，2：无检测）")
    private Integer status;
    @ApiModelProperty(value = "自检值")
    private String verifyValue;
    @ApiModelProperty(value = "自检结果（0：超差，1：合格）")
    private Integer verifyResult;
    @ApiModelProperty(value = "专检结论")
    private String specialValue;
    @ApiModelProperty(value = "（0：超差，1：合格）")
    private Integer specialResult;

}
