package com.ruoyi.production.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "ProdWorkOrderVo", description = "生产日志Vo对象")
public class ProdLogInfoVo {

    @ApiModelProperty(value = "日志类型")
    private Integer logType;
    @ApiModelProperty(value = "详细信息")
    private String detail;
    @ApiModelProperty(value = "发生时间")
    private String occurTime;
    @ApiModelProperty(value = "操作单号")
    private String opId;
    @ApiModelProperty(value = "操作名称")
    private String opName;

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(String occurTime) {
        this.occurTime = occurTime;
    }

    public String getOpId() {
        return opId;
    }

    public void setOpId(String opId) {
        this.opId = opId;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }
}
