package com.ruoyi.message.utils;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

public class RabbitMsgUtils {

    public static void SendMessage(WebSocketVo webSocketVo, String exchange) {
        RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
        rabbitTemplate.convertAndSend(exchange, null, JSON.toJSONString(webSocketVo));
    }

    public static void  SendMessage(Long lineId, String exchange) {
        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setLineId(lineId);
        SendMessage(webSocketVo, exchange);
    }
}
