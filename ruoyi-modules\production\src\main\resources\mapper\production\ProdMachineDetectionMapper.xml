<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProdMachineDetectionMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.ProdMachineDetection">
        <result column="id" property="id"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="line_id" property="lineId"/>
        <result column="status" property="status"/>
        <result column="time" property="time"/>
        <result column="operation_id" property="operationId"/>
        <result column="filename" property="filename"/>
        <result column="program_name" property="programName"/>
        <result column="barcode" property="barcode"/>
        <result column="result_content" property="resultContent"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        remark,
        deleted,
        create_by, create_time, update_by, update_time, line_id, status, time, wo_id, filename, program_name,barcode,result_content
    </sql>

    <select id="selectById" resultType="com.ruoyi.production.domain.vo.detection.ProdMachineDetectionVo">
        SELECT detection.*,
               wo.*,
               eqp.eqp_name,
               eqp.eqp_system,
               user.user_name
        FROM prod_machine_detection detection
                 LEFT JOIN prod_work_order wo ON wo.operation_id = detection.operation_id
                 LEFT JOIN base_equipment eqp ON eqp.eqp_code = wo.eqp_code
                 LEFT JOIN sys_user user
        ON user.user_id = detection.update_by
        where detection.id = #{id}
    </select>

    <select id="listForPage" resultType="com.ruoyi.production.domain.vo.detection.ProdMachineDetectionVo">
        SELECT detection.*,
        wo.*,
        eqp.eqp_name,
        eqp.eqp_system,
        user.user_name
        FROM prod_machine_detection detection
        LEFT JOIN prod_work_order wo ON wo.operation_id = detection.operation_id
        LEFT JOIN base_equipment eqp ON eqp.eqp_code = wo.eqp_code
        LEFT JOIN sys_user user ON user.user_id = detection.update_by
        <where>
            <if test="params.startTime != null">
                and detection.create_time &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and detection.create_time &lt;= #{params.endTime}
            </if>
            <if test="params.updateBy != null and params.updateBy != ''">
                and detection.update_by like concat('%', #{params.updateBy}, '%')
            </if>

            <if test="params.schedulCode != null and params.schedulCode != ''">
                and wo.schedul_code like concat('%', #{params.schedulCode}, '%')
            </if>
            <if test="params.billCode != null and params.billCode != ''">
                and wo.bill_code like concat('%', #{params.billCode}, '%')
            </if>
            <if test="params.woStatus != null and params.woStatus != ''">
                and wo.status = #{params.woStatus}
            </if>
            <if test="params.realEqp != null">
                and wo.real_eqp = #{params.realEqp}
            </if>
        </where>
        order by detection.create_time desc
    </select>
</mapper>
