package com.ruoyi.production.enums;

public enum DetectionStatusEnum {
    OUTOFTOLERANCE(0, "超差"),
    QUALIFIED(1, "合格"),
    NOTDETECTED(2, "未检测"),
    NORESULT(3,"无结论");


    private final Integer code;
    private final String info;

    DetectionStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(int code) {
        for (DetectionStatusEnum type : values()) {
            if (type.getCode() == code) {
                return type.getInfo();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
