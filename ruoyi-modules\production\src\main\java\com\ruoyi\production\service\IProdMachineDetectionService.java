package com.ruoyi.production.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.ProdMachineDetection;
import com.ruoyi.production.domain.dto.ProdMachineDetectionDto;
import com.ruoyi.production.domain.dto.detection.QualityStatisticsDto;
import com.ruoyi.production.domain.vo.detection.ProdMachineDetectionVo;
import com.ruoyi.production.domain.vo.detection.SpecialBarcodeVo;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
public interface IProdMachineDetectionService extends IService<ProdMachineDetection> {

    ProdMachineDetectionVo selectById(@Param("id") Long id);

    ProdMachineDetection selectByOperationId(@Param("operationId") String operationId);

    IPage<ProdMachineDetectionVo> listForPage(ProdMachineDetectionDto params, Integer current, Integer pageSiz);

    HashMap qualityStatistics(QualityStatisticsDto params,Long lineId);

    HashMap detectionCompare(QualityStatisticsDto params,Long lineId);

    List<SpecialBarcodeVo> getLikeSpecialBarcodeVos(@Param("barcode") String barcode);

    Long getDetectionIdByBarcode(@Param("barcode") String barcode);

    int printTag(Long id);
}
