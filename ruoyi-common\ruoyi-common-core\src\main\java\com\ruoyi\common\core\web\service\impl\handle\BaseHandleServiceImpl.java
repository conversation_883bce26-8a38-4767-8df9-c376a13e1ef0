package com.ruoyi.common.core.web.service.impl.handle;


import com.ruoyi.common.core.web.domain.MyBaseEntity;
import com.ruoyi.common.core.web.manager.BaseManager;
import com.ruoyi.common.core.web.mapper.BaseMapper;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 服务层 操作方法 基类实现通用数据处理
 *
 * @param <D>  Dto
 * @param <DG> DtoManager
 * @param <DM> DtoMapper
 * <AUTHOR>
 */
public class BaseHandleServiceImpl<D extends MyBaseEntity, DG extends BaseManager<D, DM>, DM extends BaseMapper<D>> {

    @Autowired
    protected DG baseManager;
}
