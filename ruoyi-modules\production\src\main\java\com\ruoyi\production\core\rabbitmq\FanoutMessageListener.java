package com.ruoyi.production.core.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.production.utils.WebSocketServer;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 消息弹窗消费者
 */
//@Component
public class FanoutMessageListener {

    DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;
    @Autowired
    private RemoteUserService remoteUserService;

    Logger log = LoggerFactory.getLogger(FanoutMessageListener.class);

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${spring.cloud.client.ip-address}:${server.port}-popnews", durable = "false", autoDelete = "true"),
            exchange = @Exchange(value = "popnews_fanout_exchange", type = ExchangeTypes.FANOUT)))
    public void handleMessage(String data) {
        WebSocketVo webSocketVo = JSON.toJavaObject(JSON.parseObject(data), WebSocketVo.class);
        webSocketVo.setType("message");

        Long lineId = webSocketVo.getLineId();
        JSONObject jsonObject = remoteMessageNewsService.getPopNewsByLineId(lineId, SecurityConstants.INNER).getData();
        webSocketVo.setResponse(jsonObject);
        webSocketVo.setTime(System.currentTimeMillis());

        SysUser sysUser = new SysUser();
        sysUser.setLineId(webSocketVo.getLineId());
        List<SysUser> userList = remoteUserService.listByCondition(sysUser, SecurityConstants.INNER).getData();
        for (SysUser user : userList) {
            webSocketVo.setUserId(user.getUserId());
            try {
                WebSocketServer.sendInfo(JSON.toJSONString(webSocketVo), user.getUserId() + "");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
