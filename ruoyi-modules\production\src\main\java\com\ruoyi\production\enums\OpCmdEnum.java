package com.ruoyi.production.enums;

public enum OpCmdEnum {

    OUTSTOCK("outstock", "出库"),
    INSTOCK("instock", "入库"),
    BOXCOME("boxcome", "取料箱"),
    BOXGO("boxgo", "送料箱"),
    TOBENCH("tobench", "接驳到预调"),
    TODOCK("todock", "预调到接驳"),
    PUSH("push", "推线边"),
    PULL("pull", "取线边");

    private final String code;
    private final String info;

    OpCmdEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(String code) {
        for (OpCmdEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}
