//package com.ruoyi.production.utils;
//
//import cn.hutool.core.io.FileUtil;
//import cn.hutool.extra.ftp.Ftp;
//import cn.hutool.extra.ftp.FtpMode;
//import org.apache.commons.lang.StringUtils;
//import org.apache.commons.net.ftp.FTP;
//import org.apache.commons.net.ftp.FTPClient;
//import org.apache.commons.net.ftp.FTPFile;
//import org.apache.commons.net.ftp.FTPReply;
//
//import java.io.File;
//import java.io.IOException;
//import java.io.OutputStream;
//import java.nio.charset.StandardCharsets;
//import java.nio.file.Files;
//import java.util.Arrays;
//
///**
// * <AUTHOR>
// * @description :
// * @date : 2023/3/10 14:42
// */
//public class FtpUtil {
//    /**
//     * 下载ftp服务器上的文件到本地
//     *
//     * @param remoteFile
//     * @param localFile
//     * @param ip
//     * @param port
//     * @param username
//     * @param password
//     * @param ftpMode
//     * @return 成功则返回字符串:success
//     */
//    public static String download(String remoteFile, String localFile, String ip, Integer port, String username, String password, FtpMode ftpMode) {
//        if (StringUtils.isBlank(localFile)) {
//            return "本地保存路径及名称不能为空";
//        }
//        File lFile = FileUtil.file(localFile);
//        Ftp ftp = null;
//        try {
//            //匿名登录（无需帐号密码的FTP服务器）
//            ftp = new Ftp(ip, port == null ? 21 : port, username, password);
//            if (ftpMode != null) {
//                ftp.setMode(ftpMode);
//            }
//            ftp.download(remoteFile, lFile);
//        } catch (Exception e) {
//            return e.getMessage();
//        } finally {
//            //关闭连接
//            try {
//                if (ftp != null) ftp.close();
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//        if (lFile.exists() && lFile.length() > 0) {
//            return "success";
//        } else {
//            lFile.delete();
//            return "download failure," + remoteFile + " maybe not exists !!";
//        }
//    }
//
//    /**
//     * 此方法不指定上传后保存的名称， 则按本地文件的名称保存
//     *
//     * @param remoteDir
//     * @param localFile
//     * @param ip
//     * @param port
//     * @param username
//     * @param password
//     * @return 成功则返回字符串:success
//     */
//    public static String upload(String remoteDir, String localFile, String ip, Integer port, String username, String password, FtpMode ftpMode) {
//        return upload(remoteDir, null, localFile, ip, port, username, password, ftpMode);
//    }
//
//    /**
//     * @param remoteDir      上传的ftp目录
//     * @param remoteFileName 保存到ftp服务器上的名称
//     * @param localFile      本地文件全名称
//     * @param ip
//     * @param port
//     * @param username
//     * @param password
//     * @return 成功则返回字符串:success
//     */
//    public static String upload(String remoteDir, String remoteFileName, String localFile, String ip, Integer port, String username, String password, FtpMode ftpMode) {
//        if (StringUtils.isBlank(localFile)) {
//            return "本地文件名称不能为空";
//        }
//        File lFile = FileUtil.file(localFile);
//        if (!lFile.exists()) {
//            return "本地文件不存在";
//        }
//        Ftp ftp = null;
//        try {
//            //匿名登录（无需帐号密码的FTP服务器）
//            ftp = new Ftp(ip, port == null ? 21 : port, username, password);
//            if (ftpMode != null) {
//                ftp.setMode(ftpMode);
//            }
//            if (StringUtils.isBlank(remoteFileName)) {
//                ftp.upload(remoteDir, lFile);
//            } else {
//                ftp.upload(remoteDir, remoteFileName, lFile);
//            }
//        } catch (Exception e) {
//            return e.getMessage();
//        } finally {
//            //关闭连接
//            try {
//                if (ftp != null) ftp.close();
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//        return "success";
//    }
//
//    public static void main(String[] args) {
//        //上传文件到ftp
//        String result = FtpUtil.upload("opt/upload", "fff.zip", "D:/STM.zip", "*************", 21, "ftpuser", "ftpuser!@#123", null);
//        System.out.println(result);
//
//        //下载远程文件
//        String result2 = FtpUtil.download("opt/upload/fff.zip", "D:/bbb.zip", "*************", 21, "ftpuser", "ftpuser!@#123", null);
//        System.out.println(result2);
//
//
//    }
//
//    public void batchDownLoadFileFromFtp() {
//        FTPClient client = new FTPClient();
//        try {
//            //设置主机与端口
//            client.connect("**************", 21);
//            //设置用户名及密码，这里以匿名用户登录为例，根据需求改为自己的用户名及密码
//            client.login("anonymous", "");
//            System.out.println("FTP服务器文件编码===>>" + client.getControlEncoding());
//            int reply = client.getReplyCode();
//            if (!FTPReply.isPositiveCompletion(reply)) {
//                client.disconnect();
//                System.out.println("Login Error,Please check if your username or password is correct");
//                return;
//            }
//            client.setControlEncoding("GBK");
//            System.out.println("设置后的文件编码：" + client.getControlEncoding());
//            client.enterLocalPassiveMode();
//            //切换到demo目录下
//            client.changeWorkingDirectory("demo");
//            System.out.println("---------------------------------------");
//            String[] names;
//            names = client.listNames();
//            for (String name : names) {
//                System.out.println(name);
//            }
//            System.out.println("ftp服务中,demo目录中的所有文件:" + Arrays.toString(names));
//
//            System.out.println("---------------------------------------");
//
//            FTPFile f = client.listFiles()[0];
//            System.out.println("getLink===>" + f.getLink());
//            //切换到根目录下
//            client.changeWorkingDirectory("/");
//            String path = "/demo";
//
//            client.setBufferSize(1024);
//            client.setFileType(FTP.BINARY_FILE_TYPE);
//            client.enterLocalPassiveMode();
//            //切换到demo目录下获取此目录中所有的文件，并进行一个下载
//            client.changeWorkingDirectory(path);
//            FTPFile[] fs = client.listFiles();
//            for (FTPFile ff : fs) {
//                String outFileName = ff.getName();
//                System.out.println(outFileName);
//                //本地目录文件不需要编码
//                File localFile = new File("F:\\ftpDownlaod\\" + ff.getName());
//                OutputStream fos = Files.newOutputStream(localFile.toPath());
//                // ftp默认使用ISO-8859-1编码格式,所以这里需要转换为ISO-8859-1，“解决文件名为中文时，下载后为空文件的问题”
//                String localFileName = new String(ff.getName().getBytes("GBK"), StandardCharsets.ISO_8859_1);
//                client.retrieveFile(localFileName, fos);
//                fos.close();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                client.disconnect();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//}
