<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ruoyi</groupId>
    <artifactId>ruoyi</artifactId>
    <version>3.5.0</version>

    <name>ruoyi</name>
    <url>http://www.ruoyi.vip</url>
    <description>若依微服务系统</description>

    <properties>
        <docker.registry>*************:8011</docker.registry>
        <ruoyi.version>3.5.0</ruoyi.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.6.14</spring-boot.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <alibaba.nacos.version>2.0.4</alibaba.nacos.version>
        <alibaba.seata.version>1.5.2</alibaba.seata.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <!--        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>-->
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <knife4j.version>2.0.9</knife4j.version>
        <redission.version>3.13.4</redission.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.4.1</pagehelper.boot.version>
        <druid.version>1.2.8</druid.version>
        <dynamic-ds.version>3.5.0</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>1.2.80</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <hutool.version>5.7.8</hutool.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <transmittable-thread-local.version>2.12.2</transmittable-thread-local.version>
        <shedlock.version>2.2.0</shedlock.version>
        <barcode4j.version>2.0</barcode4j.version>
        <samba.jcifs.version>1.3.3</samba.jcifs.version>
        <paho.client.mqttv3.version>1.2.2</paho.client.mqttv3.version>
        <net.version>3.6</net.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>


            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Alibaba Seata 配置 -->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${alibaba.seata.version}</version>
            </dependency>

            <!-- Alibaba Nacos 配置 -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${alibaba.nacos.version}</version>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--  SpringBoot 监控客户端 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>


            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <!--远程访问wondiws共享文件-->
            <dependency>
                <groupId>org.samba.jcifs</groupId>
                <artifactId>jcifs</artifactId>
                <version>${samba.jcifs.version}</version>
            </dependency>


            <!--整合barcode4j生成条形码-->
            <dependency>
                <groupId>net.sf.barcode4j</groupId>
                <artifactId>barcode4j-light</artifactId>
                <version>${barcode4j.version}</version>
            </dependency>

            <!--整合Knife4j-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${paho.client.mqttv3.version}</version>
            </dependency>

            <!--            &lt;!&ndash; redission &ndash;&gt;-->
            <!--            <dependency>-->
            <!--                <groupId>org.redisson</groupId>-->
            <!--                <artifactId>redisson-spring-boot-starter</artifactId>-->
            <!--                <version>${redission.version}</version>-->
            <!--            </dependency>-->


            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!--            &lt;!&ndash; Mybatis 依赖配置 &ndash;&gt;-->
            <!--            <dependency>-->
            <!--                <groupId>org.mybatis.spring.boot</groupId>-->
            <!--                <artifactId>mybatis-spring-boot-starter</artifactId>-->
            <!--                <version>${spring-boot.mybatis}</version>-->
            <!--            </dependency>-->

            <!--            &lt;!&ndash; pagehelper 分页插件 &ndash;&gt;-->
            <!--            <dependency>-->
            <!--                <groupId>com.github.pagehelper</groupId>-->
            <!--                <artifactId>pagehelper-spring-boot-starter</artifactId>-->
            <!--                <version>${pagehelper.boot.version}</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.1</version>
            </dependency>

            <!--            &lt;!&ndash;加入lombok简化项目&ndash;&gt;-->
            <!--            <dependency>-->
            <!--                <groupId>org.projectlombok</groupId>-->
            <!--                <artifactId>lombok</artifactId>-->
            <!--                <version>1.16.14</version>-->
            <!--                <scope>provided</scope>-->
            <!--            </dependency>-->

            <!-- pageHelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!--      分布式定时器      -->
<!--            <dependency>-->
<!--                <groupId>net.javacrumbs.shedlock</groupId>-->
<!--                <artifactId>shedlock-spring</artifactId>-->
<!--                <version>${shedlock.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>net.javacrumbs.shedlock</groupId>-->
<!--                <artifactId>shedlock-provider-jdbc-template</artifactId>-->
<!--                <version>${shedlock.version}</version>-->
<!--            </dependency>-->

            <!-- pagehelper 分页插件 -->
            <!--            <dependency>-->
            <!--                <groupId>com.github.pagehelper</groupId>-->
            <!--                <artifactId>pagehelper-spring-boot-starter</artifactId>-->
            <!--                <version>${pagehelper.boot.version}</version>-->
            <!--                <exclusions>-->
            <!--                    <exclusion>-->
            <!--                        <artifactId>mybatis-spring</artifactId>-->
            <!--                        <groupId>org.mybatis</groupId>-->
            <!--                    </exclusion>-->
            <!--                    <exclusion>-->
            <!--                        <artifactId>mybatis</artifactId>-->
            <!--                        <groupId>org.mybatis</groupId>-->
            <!--                    </exclusion>-->
            <!--                </exclusions>-->
            <!--            </dependency>-->

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Collection 增强Java集合框架 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-core</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-swagger</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-security</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-datascope</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-datasource</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-log</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common-redis</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-api-system</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- shengchan 接口 -->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>rouyi-api-production</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>rouyi-api-message</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${net.version}</version>
            </dependency>



        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ruoyi-auth</module>
        <module>ruoyi-gateway</module>
        <module>ruoyi-visual</module>
        <module>ruoyi-modules</module>
        <module>ruoyi-api</module>
        <module>ruoyi-common</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
