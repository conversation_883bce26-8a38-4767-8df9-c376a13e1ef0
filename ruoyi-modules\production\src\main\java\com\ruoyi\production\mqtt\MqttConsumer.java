//package com.ruoyi.production.mqtt;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.ruoyi.common.core.exception.ServiceException;
//import com.ruoyi.production.domain.EquipmentSpindleCollisionData;
//import com.ruoyi.production.service.IEquipmentSpindleCollisionDataService;
//import org.eclipse.paho.client.mqttv3.*;
//import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//
//import javax.annotation.PostConstruct;
//import java.text.DateFormat;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//
////@Component
//public class MqttConsumer implements MqttCallbackExtended {
//
//    private DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//    private static Logger log = LoggerFactory.getLogger(MqttConsumer.class);
//
//    MqttClient client;
//
//    MqttConnectOptions options;
//
//    @Value("${mqtt.broker.serverUri}")
//    private String MqttUrl;
//
//    @Value("${mqtt.topic}")
//    protected String topic;
//
//    @Value("${mqtt.qos}")
//    protected int qos;
//
//    @Value("${mqtt.broker.cleansession}")
//    private boolean cleansession;
//
//    @Value("${mqtt.broker.automaticreconnect}")
//    private boolean automaticreconnect;
//
//    @Autowired
//    private IEquipmentSpindleCollisionDataService collisionDataService;
//
////    @Value("${evjrsf.gateway.url}")
////    private String gatewayUrl;
//
//    /**
//     * 初始化mqtt消费者
//     */
//    @PostConstruct
//    public void init() {
//        try {
//            client = new MqttClient(MqttUrl, java.util.UUID.randomUUID().toString(), new MemoryPersistence());
//            // log.info("初始化mqtt客户端" + client);
//
//            // log.info("配置mqtt客户端配置信息");
//            options = new MqttConnectOptions();
//            options.setCleanSession(cleansession);
//            options.setConnectionTimeout(30);
//            options.setKeepAliveInterval(15);
//            options.setAutomaticReconnect(automaticreconnect);
//            options.setMqttVersion(MqttConnectOptions.MQTT_VERSION_DEFAULT);
//
//            client.setCallback(this);
//            client.connect(options);
//            client.subscribe(topic, qos);
//            log.info("初始化mqtt客户端！");
//        } catch (MqttSecurityException e) {
//            log.info("初始化mqtt客户端异常：MqttSecurityException！\n" + e.getLocalizedMessage());
//            throw new ServiceException(e.getLocalizedMessage());
//        } catch (MqttException e) {
//            log.info("初始化mqtt客户端异常：MqttException！\n" + e.getLocalizedMessage());
//            throw new ServiceException(e.getLocalizedMessage());
//        }
//
//    }
//
//
//    /**
//     * 断开链接，重连操作
//     */
//    public void connectionLost(Throwable cause) {
//        // log.info("MQTT>>" + cause + " 连接断开，1S之后尝试重连...");
//        while (true) {
//            try {
//                if (!client.isConnected()) {
//                    log.info("重连mqtt客户端");
//                    client.setCallback(this);
//                    client.connect(options);
//                }
//                if (client.isConnected()) {
//                    log.info("mqtt客户端已重新链接");
//                    break;
//                }
//            } catch (MqttException e) {
//                log.info("mqtt重连异常");
//                throw new ServiceException(e.getLocalizedMessage());
//            }
//        }
//    }
//
//
//    /**
//     * 连接成功+订阅消息
//     */
//    @Override
//    public void connectComplete(boolean reconnect, String serverURI) {
//        log.info("开始订阅.............");
//        try {
//            client.subscribe(topic, qos);
//            log.info("订阅主题>>" + topic + ",qos>>" + qos);
//        } catch (MqttException e) {
//            log.info("订阅主题：" + topic + ",qos:" + qos + "异常：" + e.getLocalizedMessage());
//            throw new ServiceException(e.getLocalizedMessage());
//        }
//        log.info("已经订阅.........");
//    }
//
//    /**
//     * 收到新的消息推送
//     */
//    public void messageArrived(String topic, MqttMessage message) throws Exception {
////        log.info("Consumer接收到的数据\n topic:" + topic + "\n message:" + message.toString());
//
//
//        // subscribe后得到的消息会执行到这里面
////        String result = new String(message.getPayload(), "UTF-8");
//        //log.info("接收消息主题 >> " + topic + " 接收消息Qos >> " + message.getQos() + " 接收消息内容 >> " + result);
//
///*        //这里可以针对收到的消息做处理
//        ElecMeasuringPointEntity elecMeasuringPointEntity = new ElecMeasuringPointEntity();
//        elecMeasuringPointEntity.setUEnterpriseId(1111);
////插入方法
//        PushCallback.elecMeasuringPointDao.insert(elecMeasuringPointEntity);*/
//
//        try {
//            String machCode = topic.substring(4,7);
//            JSONObject jsonObject = JSON.parseObject(message.toString());
//            String key = jsonObject.getString("TKey");
//            Date syncTime = sdf.parse(jsonObject.getString("Time"));
//            String value = jsonObject.getString("Tvalue");
//            EquipmentSpindleCollisionData data = EquipmentSpindleCollisionData.builder()
//                    .syncTime(syncTime)
//                    .mach(machCode)
//                    .coordinate(key)
//                    .value(value)
//                    .build();
////        System.out.println(JSON.toJSONString(data));
//            collisionDataService.save(data);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            //throw new ServiceException(e);
//        }
//
//    }
//
//    /**
//     * 消息处理结束
//     */
//    public void deliveryComplete(IMqttDeliveryToken token) {
//        // log.info("消息处理结束时调用");
//    }
//}