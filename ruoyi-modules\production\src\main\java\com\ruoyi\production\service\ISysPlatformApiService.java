package com.ruoyi.production.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.production.domain.SysPlatformApi;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
public interface ISysPlatformApiService extends IService<SysPlatformApi> {
    List<SysPlatformApi> listByCondition(SysPlatformApi platformApi);

    SysPlatformApi getEntityByCode (String code);
    SysPlatformApi getEntityLikeIp (String ip);

}
