package com.ruoyi.production.domain.vo;

import com.ruoyi.production.domain.ProdOrderMachRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "订单机床设置vo", description = "")
public class OrderMachConfigVo extends ProdOrderMachRelation {
    @ApiModelProperty(value = "配置机床是否启用")
    private Integer enable;
    @ApiModelProperty(value = "机床名称")
    private String eqpName;
    @ApiModelProperty(value = "机床离线/在线")
    private Integer eqpStatus;
}
