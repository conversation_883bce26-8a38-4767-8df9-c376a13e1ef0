<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.test.OrderMapper">
    <resultMap type="com.ruoyi.production.domain.test.Order" id="OrderResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="productId" column="product_id"/>
        <result property="amount" column="amount"/>
        <result property="totalPrice" column="total_price"/>
        <result property="status" column="status"/>
        <result property="addTime" column="add_time"/>
        <result property="lastUpdateTime" column="last_update_time"/>
    </resultMap>

    <insert id="insert" parameterType="com.ruoyi.production.domain.test.Order" useGeneratedKeys="true" keyProperty="id">
        insert into p_order (
        <if test="userId != null and userId != '' ">
            user_id,
        </if>
        <if test="productId != null and productId != '' ">
            product_id,
        </if>
        <if test="amount != null and amount != '' ">
            amount,
        </if>
        <if test="totalPrice != null and totalPrice != '' ">
            total_price,
        </if>
        <if test="status != null and status != ''">
            status,
        </if>
        add_time
        )values(
        <if test="userId != null and userId != ''">
            #{userId},
        </if>
        <if test="productId != null and productId != ''">
            #{productId},
        </if>
        <if test="amount != null and amount != ''">
            #{amount},
        </if>
        <if test="totalPrice != null and totalPrice != ''">
            #{totalPrice},
        </if>
        <if test="status != null and status != ''">
            #{status},
        </if>
        sysdate()
        )
    </insert>

    <update id="updateById" parameterType="com.ruoyi.production.domain.test.Order">
        update p_order
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="productId != null and productId != ''">
                product_id = #{productId},
            </if>
            <if test="amount != null and amount != ''">
                amount = #{amount},
            </if>
            <if test="totalPrice != null and totalPrice != ''">
                total_price = #{totalPrice},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            last_update_time = sysdate()
        </set>
        where id = #{id}
    </update>
</mapper>