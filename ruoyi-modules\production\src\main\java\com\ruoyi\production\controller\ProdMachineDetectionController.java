package com.ruoyi.production.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.production.domain.ProdMachineDetection;
import com.ruoyi.production.domain.dto.ProdMachineDetectionDto;
import com.ruoyi.production.domain.dto.detection.QualityStatisticsDto;
import com.ruoyi.production.service.IProdMachineDetectionService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("machineDetection")
public class ProdMachineDetectionController extends BaseModuleController {

    @Autowired
    private IProdMachineDetectionService detectionService;

    @GetMapping("/{id}")
    @ApiOperation("根据数据库id获取机内检测信息")
    public AjaxResult getDetectionById(@ApiParam(value = "基本检测主记录id") @PathVariable Long id) {
//        log.info("********** getDetection - 根据id获取机内检测信息, id={}", id);
        return AjaxResult.success(detectionService.selectById(id));
    }

    @GetMapping("getByOperationId/{operationId}")
    @ApiOperation("根据工单id获取机内检测信息")
    public AjaxResult getDetectionByOperationId(@ApiParam(value = "基本检测主记录id") @PathVariable String operationId) {
//        log.info("********** getDetection - 根据id获取机内检测信息, id={}", id);
        ProdMachineDetection detection = detectionService.selectByOperationId(operationId);

        return AjaxResult.success(detection == null ? "" : detection);
    }

    @ApiOperation("分页查询所有信息")
    @PostMapping("listForPage")
    public AjaxResult listForPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody ProdMachineDetectionDto params) {
        return AjaxResult.success(detectionService.listForPage(params, pageNum, pageSize));
    }

    @ApiOperation("产线在线检测数据")
    @PostMapping("qualityStatistics")
    public AjaxResult qualityStatistics(@RequestBody QualityStatisticsDto params) {
        return AjaxResult.success(detectionService.qualityStatistics(params, getLineId()));
    }

    @ApiOperation("机检专检对比图")
    @PostMapping("detectionCompare")
    public AjaxResult detectionCompare(@RequestBody QualityStatisticsDto params) {
        return AjaxResult.success(detectionService.detectionCompare(params, getLineId()));
    }

    @GetMapping("/getLikeSpecialBarcodeVos/{barcode}")
    public AjaxResult getLikeSpecialBarcodeVos(@PathVariable("barcode") String barcode) {
        return AjaxResult.success(detectionService.getLikeSpecialBarcodeVos(barcode));
    }

    @GetMapping("/getDetectionIdByBarcode/{barcode}")
    public AjaxResult getDetectionIdByBarcode(@PathVariable("barcode") String barcode) {
        Long id = detectionService.getDetectionIdByBarcode(barcode);

        return AjaxResult.success(id == null ? -1L : id);
    }

    @PutMapping("/printTag/{id}")
    public AjaxResult printTag(@PathVariable("id") Long id) {
        return AjaxResult.success(detectionService.printTag(id));
    }

}
