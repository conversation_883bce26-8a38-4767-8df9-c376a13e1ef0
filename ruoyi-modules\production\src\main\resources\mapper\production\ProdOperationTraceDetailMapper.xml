<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.ProdOperationTraceDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.ProdOperationTraceDetail">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="remark" property="remark"/>
        <result column="pid" property="pid"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        remark, pid, status
    </sql>

    <select id="getDetailList" resultType="com.ruoyi.production.domain.vo.operation.ProdOperationTraceDetailVo">
        select detail.create_time,detail.pid,detail.status,detail.remark,detail.id, trace.line_id,
        trace.cmd,trace.deleted,trace.update_time
        from prod_operation_trace_detail detail
        left join prod_operation_trace trace on detail.pid = trace.id
        <where>
            <if test="params.pidList != null and params.pidList.size()>0">
                AND trace.id IN
                <foreach item="item" index="index" collection="params.pidList" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.startTime != null">
                AND date_format(detail.create_time,'%y%m%d')  >= date_format(#{params.startTime},'%y%m%d')
            </if>
            <if test="params.endTime != null">
                AND date_format(#{params.endTime},'%y%m%d') >= date_format(detail.create_time,'%y%m%d')
            </if>
            <if test="params.status != null">
                AND trace.status = #{params.status}
            </if>
            <if test="params.pid != null">
                AND detail.pid = #{params.pid}
            </if>
        </where>
    </select>
</mapper>
