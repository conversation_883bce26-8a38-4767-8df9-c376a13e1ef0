package com.ruoyi.message.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.message.controller.MessageButtonController;
import com.ruoyi.message.domian.MessageGroup;
import com.ruoyi.message.domian.vo.MessageGroupButtonVo;
import com.ruoyi.message.domian.vo.MessagePopInfoVo;
import com.ruoyi.message.mapper.MessageNewsMapper;
import com.ruoyi.message.service.IMessageButtonService;
import com.ruoyi.message.service.IMessageGroupService;
import com.ruoyi.message.service.IMessageNewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Service
public class MessageNewsServiceImpl extends ServiceImpl<MessageNewsMapper, MessageNews> implements IMessageNewsService {

    @Autowired
    private IMessageButtonService buttonService;
    @Autowired
    private IMessageGroupService groupService;

    @Override
    public IPage<MessageNews> listForPage(MessageNews messageNews, Integer current, Integer pageSiz) {
        Page<MessageNews> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);

        LambdaQueryWrapper<MessageNews> wrapper = new LambdaQueryWrapper<>();
        if (null != messageNews.getIsRead()) {
            wrapper.eq(MessageNews::getIsRead, messageNews.getIsRead());
        }
        if (null != messageNews.getIsDeal()) {
            wrapper.eq(MessageNews::getIsDeal, messageNews.getIsDeal());
        }
        wrapper.orderByDesc(MessageNews::getCreateTime);
        return this.getBaseMapper().selectPage(page, wrapper);
    }

    @Override
    public List<MessageNews> getUnHandleList(MessageNews messageNews) {
        QueryWrapper<MessageNews> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper.eq("is_read", 0)
                .or().eq("is_deal", 0)
        );
        queryWrapper.eq("line_id", messageNews.getLineId());
//        queryWrapper.eq("title", messageNews.getTitle());
        queryWrapper.eq("content", messageNews.getContent());
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public void readMessage(Long id) {
    }

    @Override
    public JSONObject getPopNewsByLineId(Long id) {
        //测试添加
//        MessageNews messageNews1 = this.baseMapper.selectById("1027");
//        messageNews1.setIsRead(0);
//        this.updateById(messageNews1);
        QueryWrapper<MessageNews> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("line_id", id);
        queryWrapper.and(wrapper -> wrapper.eq("is_read", 0)
                .or().eq("is_deal", 0)
        );
        List<MessageNews> newsList = this.baseMapper.selectList(queryWrapper);

        MessageButtonController buttonController = SpringUtils.getBean(MessageButtonController.class);

        JSONObject data = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        //遍历所有未处理消息
        for (MessageNews messageNews : newsList) {
            MessagePopInfoVo popInfoVo = new MessagePopInfoVo();
            popInfoVo.setId(messageNews.getId());
            popInfoVo.setText(messageNews.getContent());
            popInfoVo.setIsDeal(messageNews.getIsDeal());
            popInfoVo.setTime(messageNews.getCreateTime().getTime());
            popInfoVo.setOperationId(messageNews.getOperationId());

            //给前端处理 开始
            if (messageNews.getButtonGroupId() == 1)
                popInfoVo.setMessageRenderId("1");
            else
                popInfoVo.setMessageRenderId("default");
            //给前端处理 结束

            //根据message 的 button——group id 找到第一级 button 信息
            List<MessageGroupButtonVo> buttonList = buttonController.getButtonsByGroupId(messageNews.getButtonGroupId()).getData();
            for (MessageGroupButtonVo vo : buttonList) {
                if (null != vo.getChildGroupId())
                    //遍历出下级按钮信息（如果有）
                    getChildPop(vo);
                vo.setButtonId(null);
                vo.setChildGroupId(null);
                vo.setGroupId(null);
            }
            //目标链接url 不为空 ，去处理
//            if (StringUtils.isNotBlank(messageNews.getTargetUrl())) {
//                for (MessageButton messageButton : buttonList) {
//                    if (!messageButton.getButtonType().equals("close")) {
//                        messageButton.setButtonUrl(messageNews.getTargetUrl());
//                        break;
//                    }
//                }
//            }
            popInfoVo.setActions(buttonList);
            jsonArray.add(popInfoVo);
        }
        data.put("list", jsonArray);

        return data;
    }

    @Override
    public Boolean reset(Long id) {
        MessageNews messageNews = new MessageNews();
        messageNews.setId(id);
        messageNews.setIsRead(0);
        messageNews.setIsDeal(0);
        return this.updateById(messageNews);
    }

    private MessageGroupButtonVo getChildPop(MessageGroupButtonVo buttonVo) {
        MessagePopInfoVo childPopInfo = new MessagePopInfoVo();
        MessageButtonController buttonController = SpringUtils.getBean(MessageButtonController.class);
        MessageGroup messageGroup = groupService.getById(buttonVo.getChildGroupId());
        List<MessageGroupButtonVo> buttonList = buttonController.getButtonsByGroupId(messageGroup.getId()).getData();

        childPopInfo.setText(messageGroup.getContent());
        childPopInfo.setActions(buttonList);
        buttonVo.setPopInfo(childPopInfo);

        for (MessageGroupButtonVo vo : buttonList) {
            if (null != vo.getChildGroupId())
                getChildPop(vo);
            vo.setButtonId(null);
            vo.setChildGroupId(null);
            vo.setGroupId(null);
        }

        return buttonVo;
    }
}
