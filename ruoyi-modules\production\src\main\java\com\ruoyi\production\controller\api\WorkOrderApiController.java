package com.ruoyi.production.controller.api;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.production.controller.BaseModuleController;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.service.IProdWorkOrderService;
import com.ruoyi.production.utils.ProdDateUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "订单接口", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequestMapping("api/workorder/")
public class WorkOrderApiController extends BaseModuleController {

    @Autowired
    private IProdWorkOrderService prodWorkOrderService;

    @PostMapping(value = "getWorkOrderListByParams", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public R<?> getWorkOrderListByParams(@RequestBody ProdWorkOrderDto prodWorkOrderDto){
        List<ProdWorkOrderVo> prodWorkOrderVoList = prodWorkOrderService.apiGetWorkOrderList(prodWorkOrderDto);
        return R.ok(prodWorkOrderVoList);
    }

    public static void main(String[] args) throws ParseException {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String start = "2023-03-09 00:00:00";
        String end = "2023-03-09 06:00:00";
        System.out.println(DateUtil.between(sdf.parse(start),sdf.parse(end),DateUnit.MINUTE));

    }


}
