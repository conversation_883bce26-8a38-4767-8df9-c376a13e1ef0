package com.ruoyi.production.core.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.message.api.RemoteMessageNewsService;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.production.core.observable.OperationDoneEvent;
import com.ruoyi.production.domain.ProdOperationTrace;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.service.IProdWorkOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OperationDonePopListener implements ApplicationListener<OperationDoneEvent> {

    @Autowired
    private RemoteMessageNewsService remoteMessageNewsService;
    @Autowired
    private IProdWorkOrderService workOrderService;

    @Override
    public void onApplicationEvent(OperationDoneEvent event) {
        ProdOperationTrace operationTrace = event.getOperationTrace();
        //出库料箱 到位通知
        if (9L == (operationTrace.getProcessId())) {
            JSONObject opreationData = JSON.parseObject(operationTrace.getOperationData());
            ProdWorkOrderDto params = new ProdWorkOrderDto();
            params.setBillCode(opreationData.getString("billcode"));
            params.setSchedulCode(opreationData.getString("schedulCode"));
            List<ProdWorkOrderVo> orderList = workOrderService.listByCondition(params);
            MessageNews messageNews = new MessageNews();
            messageNews.setContent("物料[" + orderList.get(0).getMatName() + "],任务号：" + opreationData.getString("billcode")
                    + ",工序号：" + opreationData.getString("schedulCode") + ",出库" + opreationData.getString("outstockNum") + "已到达装调站，请确认操作！");
            messageNews.setCreaterId(1L);
            messageNews.setLineId(operationTrace.getLineId());
            messageNews.setButtonGroupId(2L);
            remoteMessageNewsService.add(messageNews, SecurityConstants.INNER);
        }
    }
}
