package com.ruoyi.production.domain.vo.detection;

import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DetectionReportVo {

    @ApiModelProperty(value = "任务号")
    @Excel(name = "任务号")
    private String billCode;

    @Excel(name = "工序号")
    @ApiModelProperty(value = "工序号")
    private String schedulCode;

    @ApiModelProperty(value = "工序名")
    private String schedulName;

    @Excel(name = "名称")
    @ApiModelProperty(value = "物料名称")
    private String matName;

    @Excel(name = "订单数量", cellType = Excel.ColumnType.NUMERIC)
    private int totalNum;
    @ApiModelProperty("完成数量")
    private int doneNum;

    @Excel(name = "机检合格数量", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty("机检合格数量")
    private int qualifiedNum;

    @Excel(name = "机检合格率")
    @ApiModelProperty("机检合格率")
    private String qualifiedRate;

    @Excel(name = "自检合格数量", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty("自检合格数量")
    private int verifyQualifiedNum;

    @Excel(name = "专检合格数量", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty("专检合格数量")
    private int specialQualifiedNum;

    @ApiModelProperty("专检合格率")
    @Excel(name = "专检合格率")
    private String specialQualifiedRate;
}
