package com.ruoyi.production.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.production.domain.SysPlatformApi;
import com.ruoyi.production.mapper.SysPlatformApiMapper;
import com.ruoyi.production.service.ISysPlatformApiService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2022-04-26
 */
@Service
public class SysPlatformApiServiceImpl extends ServiceImpl<SysPlatformApiMapper, SysPlatformApi> implements ISysPlatformApiService {

    @Override
    public List<SysPlatformApi> listByCondition(SysPlatformApi platformApi) {

        QueryWrapper<SysPlatformApi> queryWrapper = new QueryWrapper();
        if (platformApi.getApiCode() != null)
            queryWrapper.eq("api_code", platformApi.getApiCode());
        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public SysPlatformApi getEntityByCode(String code) {
        if (StringUtils.isBlank(code))
            return null;
        QueryWrapper<SysPlatformApi> queryWrapper = new QueryWrapper();
        queryWrapper.eq("api_code", code);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public SysPlatformApi getEntityLikeIp(String ip) {
        QueryWrapper<SysPlatformApi> queryWrapper = new QueryWrapper();
        queryWrapper.like("url", ip);
        return this.getBaseMapper().selectOne(queryWrapper);
    }
}
