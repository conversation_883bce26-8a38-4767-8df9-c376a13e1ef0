package com.ruoyi.message.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.message.api.domain.MessageNews;
import com.ruoyi.message.api.domain.vo.WebSocketVo;
import com.ruoyi.message.service.IMessageNewsService;
import io.swagger.annotations.ApiOperation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("news")
public class MessageNewsController extends BaseModuleController {

    @Autowired
    private IMessageNewsService newsService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @ApiOperation("查询未读信息")
    @GetMapping("listByCondition")
    public AjaxResult listByCondition(@RequestBody MessageNews messageNews) {
        MessageNews params = new MessageNews();
        params.setLineId(getLineId());
        params.setIsRead(0);
        return AjaxResult.success(newsService.getUnHandleList(params));
    }

    @ApiOperation("分页查询所有信息")
    @PostMapping("listForPage")
    public AjaxResult listForPage(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestBody MessageNews messageNews) {
        return AjaxResult.success(newsService.listForPage(messageNews,pageNum,pageSize));
    }

    @ApiOperation("设置信息为已读")
    @PutMapping("readNews/{id}")
    public AjaxResult readNews(@PathVariable("id") Long id) {
        MessageNews messageNews = newsService.getById(id);
        if (null == messageNews){
            throw new ServiceException("消息不存在");
        }
        messageNews.setId(id);
        messageNews.setIsRead(1);
        //只读信息，已读后同时设置已处理
        if (messageNews.getType().equals("1")){
            messageNews.setIsDeal(1);
        }
        newsService.updateById(messageNews);

        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setLineId(messageNews.getLineId());
        rabbitTemplate.convertAndSend("direct_interface_exchange","pop", JSON.toJSONString(webSocketVo));
//        RabbitMsgUtils.SendMessage(messageNews.getLineId(), ProductionConstans.mq_websocket_message_fanout_exchange);
        return AjaxResult.success();
    }

    @ApiOperation("设置信息为已处理")
    @PutMapping("dealNews/{id}")
    public AjaxResult dealNews(@PathVariable("id") Long id) {
        MessageNews messageNews = newsService.getById(id);
        if (null == messageNews)
            throw new ServiceException("消息不存在");
        messageNews.setId(id);
        messageNews.setIsRead(1);
        messageNews.setIsDeal(1);
        newsService.updateById(messageNews);

        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setLineId(messageNews.getLineId());
        rabbitTemplate.convertAndSend("direct_interface_exchange","pop",JSON.toJSONString(webSocketVo));
//        RabbitMsgUtils.SendMessage(messageNews.getLineId(), ProductionConstans.mq_websocket_message_fanout_exchange);
        return AjaxResult.success();
    }

//    --------------------------------------------  inner interface -----------------------------------------

    @InnerAuth
    @ApiOperation("添加新信息")
    @PostMapping("add")
    public R add(@RequestBody MessageNews messageNews) {
        List<MessageNews> existNews = newsService.getUnHandleList(messageNews);
        if (existNews.size() > 0)
            return R.ok("相同信息已存在");
        newsService.save(messageNews);

        WebSocketVo webSocketVo = new WebSocketVo();
        webSocketVo.setLineId(messageNews.getLineId());
        rabbitTemplate.convertAndSend("direct_interface_exchange","pop",JSON.toJSONString(webSocketVo));
//        RabbitMsgUtils.SendMessage(messageNews.getLineId(), ProductionConstans.mq_websocket_message_fanout_exchange);
        return R.ok();
    }

//    @InnerAuth
    @ApiOperation("根据产线获取弹窗消息")
    @GetMapping("getPopNewsByLineId/{id}")
    public R<JSONObject> getPopNewsByLineId(@PathVariable("id") Long id) {
        return R.ok(newsService.getPopNewsByLineId(id));
    }

    @ApiOperation("重置指定消息为初始状态")
    @GetMapping("reset/{id}")
    public R<Boolean> reset(@PathVariable("id") Long id) {
        return R.ok(newsService.reset(id));
    }

}
