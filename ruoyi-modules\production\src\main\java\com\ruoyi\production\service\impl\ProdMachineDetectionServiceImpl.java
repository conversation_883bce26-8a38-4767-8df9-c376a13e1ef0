package com.ruoyi.production.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.production.api.domain.BaseEquipment;
import com.ruoyi.production.domain.ProdMachineDetection;
import com.ruoyi.production.domain.ProdWorkOrder;
import com.ruoyi.production.domain.dto.ProdMachineDetectionDto;
import com.ruoyi.production.domain.dto.ProdWorkOrderDto;
import com.ruoyi.production.domain.dto.detection.QualityStatisticsDto;
import com.ruoyi.production.domain.vo.detection.*;
import com.ruoyi.production.domain.vo.ProdWorkOrderVo;
import com.ruoyi.production.enums.DetectionStatusEnum;
import com.ruoyi.production.enums.EqpTypeEnum;
import com.ruoyi.production.enums.WorkOrderStatusEnum;
import com.ruoyi.production.mapper.ProdMachineDetectionMapper;
import com.ruoyi.production.service.IBaseEquipmentService;
import com.ruoyi.production.service.IProdMachineDetectionService;
import com.ruoyi.production.service.IProdWorkOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Service
public class ProdMachineDetectionServiceImpl extends ServiceImpl<ProdMachineDetectionMapper, ProdMachineDetection> implements IProdMachineDetectionService {

    @Autowired
    private IProdWorkOrderService orderService;
    @Autowired
    private IBaseEquipmentService equipmentService;

    @Override
    public ProdMachineDetectionVo selectById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    public ProdMachineDetection selectByOperationId(String operationId) {
        ProdWorkOrder prodWorkOrder = orderService.getEntityByOperationId(operationId);
        QueryWrapper<ProdMachineDetection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", operationId);
        ProdMachineDetection detection = this.getBaseMapper().selectOne(queryWrapper);
        if (null != detection && null != prodWorkOrder) {
            detection.setOperationSplit(prodWorkOrder.getOperationSplit());
        }
        return detection;
    }

    @Override
    public IPage<ProdMachineDetectionVo> listForPage(ProdMachineDetectionDto detection, Integer current, Integer pageSiz) {
        Page<ProdMachineDetectionVo> page = new Page<>();
        page.setCurrent(current);
        page.setSize(pageSiz);

        return this.getBaseMapper().listForPage(page, detection);
    }

    @Override
    public HashMap qualityStatistics(QualityStatisticsDto params, Long lineId) {
        Long start = System.currentTimeMillis();
        HashMap map = new HashMap();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        //查询时间范围内所有订单所有订单
        ProdWorkOrderDto orderParam = new ProdWorkOrderDto();
        orderParam.setSchedulStartDate(params.getStartTime());
        orderParam.setSchedulEndDate(params.getEndTime());
        orderParam.setLineId(lineId);
        List<ProdWorkOrderVo> orderList = orderService.listByCondition(orderParam);
        //按时间排序-从小到大
        orderList.sort(Comparator.comparingLong(o -> o.getSchedulStartNyr().getTime()));
        //1.质量情况统计
        //时间范围小于周--展示维度 --天  （目前都按天给数据）
        TreeMap<String, QualityVo> qualityMap = new TreeMap<>();
        TreeMap<String, MachineQuantityVo> machineQuantityVoHashMap = new TreeMap<>();
        QualityVo vo = null;
        MachineQuantityVo machineQuantityVo = null;
        for (ProdWorkOrderVo o : orderList) {
            String date = dateFormat.format(o.getSchedulStartNyr());
            //时间范围的质量情况
            if (qualityMap.containsKey(date)) {
                vo = qualityMap.get(date);
            } else {
                vo = new QualityVo();
            }
            vo.setPlannedNum(vo.getPlannedNum() + 1);
//            if (o.getStatus() >= WorkOrderStatusEnum.DONE.getCode()) {
//                vo.setCompletedNum(vo.getCompletedNum() + 1);
//            }
//            if (o.getDetectionStatus() != null && DetectionStatusEnum.QUALIFIED.getCode().equals(o.getDetectionStatus())) {
//                vo.setQualifiedNum(vo.getQualifiedNum() + 1);
//            }
            qualityMap.put(date, vo);
        }
        //时间缺少的补齐
        TreeSet<String> treeSet = DateUtils.getBetweenEveryday(params.getStartTime(), params.getEndTime());
        treeSet.forEach(date -> {
            if (!qualityMap.keySet().contains(date)) {
                qualityMap.put(date, new QualityVo());
            }
        });

        //时间排序
        map.put("qualityMap", qualityMap);

        //时间范围小于周--展示维度 --周 todo

        //2.设备产出质量指标图 根据实际
        orderParam.setSchedulStartDate(null);
        orderParam.setSchedulEndDate(null);
        orderParam.setRealEndDateBegin(params.getStartTime());
        orderParam.setRealEndDateStop(params.getEndTime());
        List<ProdWorkOrderVo> realEndDateOrderList = orderService.listByCondition(orderParam);
        for (ProdWorkOrderVo o : realEndDateOrderList) {
            if(null == o.getRealEqp() || StringUtils.isBlank(o.getRealEqp() ))
                continue;
            //day count
            String date = dateFormat.format(o.getRealEndTime());
            if (qualityMap.containsKey(date)) {
                vo = qualityMap.get(date);
                if (o.getStatus() >= WorkOrderStatusEnum.DONE.getCode()) {
                    vo.setCompletedNum(vo.getCompletedNum() + 1);
                }
                if (o.getDetectionStatus() != null && DetectionStatusEnum.QUALIFIED.getCode().equals(o.getDetectionStatus())) {
                    vo.setQualifiedNum(vo.getQualifiedNum() + 1);
                }
                qualityMap.put(date, vo);
            }
            //设备产出质量指标图
            if (machineQuantityVoHashMap.containsKey(o.getRealEqp())) {
                machineQuantityVo = machineQuantityVoHashMap.get(o.getRealEqp());
            } else {
                machineQuantityVo = new MachineQuantityVo();
            }
            //完成量+1
            if (o.getStatus() >= WorkOrderStatusEnum.DONE.getCode()) {
                machineQuantityVo.setDoneWorkOrderNum(machineQuantityVo.getDoneWorkOrderNum() + 1);
            }
            //TODO 合格产品数量的自增
            if (o.getDetectionStatus() != null && DetectionStatusEnum.QUALIFIED.getCode().equals(o.getDetectionStatus())) {
                machineQuantityVo.setQualifiedWorkOrderNum(machineQuantityVo.getQualifiedWorkOrderNum() + 1);
            }
            machineQuantityVoHashMap.put(o.getRealEqp(), machineQuantityVo);
        }
        //设备缺少的补齐
        BaseEquipment param = new BaseEquipment();
        param.setLineId(lineId);
        param.setType(EqpTypeEnum.MACHINE.getCode());
        List<BaseEquipment> machList = equipmentService.listByCondition(param);
        machList.forEach(mach -> {
            if (!machineQuantityVoHashMap.keySet().contains(mach.getEqpCode())) {
                machineQuantityVoHashMap.put(mach.getEqpCode(), new MachineQuantityVo());
            }
        });
        map.put("machineQuantityVoHashMap", machineQuantityVoHashMap);

        //3.任务情况统计
        OrderStatisticsVo orderStatisticsVo = new OrderStatisticsVo();
        orderStatisticsVo.setWorkOrderNum(orderList.size());
        List<ProdWorkOrderVo> tepmList = realEndDateOrderList.stream().filter(order -> order.getStatus()>=WorkOrderStatusEnum.DONE.getCode()).collect(Collectors.toList());
        orderStatisticsVo.setDoneWorkOrderNum(tepmList.size());
        HashSet set = new HashSet();
        orderList.forEach(order -> {
            set.add(order.getBillSchedulCode());
        });
        orderStatisticsVo.setOrderNum(set.size());
        //本期任务总数
        //获取上一个环比时间范围
        HashMap<String, Date> hbMap = DateUtils.getHuanBiDate(params.getStartTime(), params.getEndTime());
        orderParam.setRealEndDateBegin(null);
        orderParam.setRealEndDateStop(null);
        orderParam.setSchedulStartDate(hbMap.get("hbstartDate"));
        orderParam.setSchedulEndDate(hbMap.get("hbendDate"));
        List<ProdWorkOrderVo> hbOrderList = orderService.listByCondition(orderParam);
        orderStatisticsVo.setPreviousWorkOrderNum(hbOrderList.size());
        //环比完成数量
        orderParam.setSchedulStartDate(null);
        orderParam.setSchedulEndDate(null);
        orderParam.setRealEndDateBegin(hbMap.get("hbstartDate"));
        orderParam.setRealEndDateStop(hbMap.get("hbendDate"));
        List<ProdWorkOrderVo> hbRealEndDateOrderList = orderService.listByCondition(orderParam);
        tepmList = hbRealEndDateOrderList.stream().filter(order -> order.getStatus()>=WorkOrderStatusEnum.DONE.getCode()).collect(Collectors.toList());
        orderStatisticsVo.setPreviousDoneWorkOrderNum(tepmList.size());

        map.put("orderStatistics", orderStatisticsVo);

        return map;
    }

    @Override
    public HashMap detectionCompare(QualityStatisticsDto params, Long lineId) {
        HashMap map = new HashMap();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        //查询时间范围内所有订单所有订单
        ProdWorkOrderDto orderParam = new ProdWorkOrderDto();
        orderParam.setRealEndDateBegin(params.getStartTime());
        orderParam.setRealEndDateStop(params.getEndTime());
        List<ProdWorkOrderVo> orderList = orderService.listByCondition(orderParam);
        //按时间排序-从小到大
        orderList.sort(Comparator.comparingLong(o -> o.getSchedulStartNyr().getTime()));
        //1.质量情况统计
        //时间范围小于周--展示维度 --天  （目前都按天给数据）
        TreeMap<String, DetectionCompareVo> detectionCompareMap = new TreeMap<>();
        DetectionCompareVo vo = null;
        for (ProdWorkOrderVo o : orderList) {
            String date = dateFormat.format(o.getSchedulStartNyr());
            //时间范围的质量情况
            if (detectionCompareMap.containsKey(date)) {
                vo = detectionCompareMap.get(date);
            } else {
                vo = new DetectionCompareVo();
            }
            if(DetectionStatusEnum.QUALIFIED.getCode().equals(o.getDetectionStatus())){
                vo.setQualifiedNum(vo.getQualifiedNum() + 1);
            }
            if(DetectionStatusEnum.QUALIFIED.getCode().equals(o.getSelfDetectionStatus())){
                vo.setVerifyQualifiedNum(vo.getVerifyQualifiedNum() + 1);
            }
            if(DetectionStatusEnum.QUALIFIED.getCode().equals(o.getSpecialDetectionStatus())){
                vo.setSpecialQualifiedNum(vo.getSpecialQualifiedNum() + 1);
            }
            detectionCompareMap.put(date, vo);
        }
        //时间缺少的补齐
        TreeSet<String> treeSet = DateUtils.getBetweenEveryday(params.getStartTime(), params.getEndTime());
        treeSet.forEach(date -> {
            if (!detectionCompareMap.keySet().contains(date)) {
                detectionCompareMap.put(date, new DetectionCompareVo());
            }
        });

        //时间排序
        map.put("detectionCompareMap", detectionCompareMap);

        return map;
    }

    @Override
    public List<SpecialBarcodeVo> getLikeSpecialBarcodeVos(String barcode) {
        return this.baseMapper.getSpecialBarcodeVos(barcode);
    }

    @Override
    public Long getDetectionIdByBarcode(String barcode) {
        return this.baseMapper.getDetectionIdByBarcode(barcode);
    }

    @Override
    public int printTag(Long id) {
        ProdMachineDetection detection = this.baseMapper.selectById(id);
        detection.setPrintTag(1);
        return this.baseMapper.updateById(detection);
    }

    public static void main(String[] args) {
        Map<Date, Integer> qualityMap = new HashMap();
        Date date = new Date();
        qualityMap.put(date, 1);
        System.out.println(qualityMap.containsKey(date));
    }

}
