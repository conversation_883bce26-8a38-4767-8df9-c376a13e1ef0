<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.production.mapper.BaseFillerTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.production.domain.BaseFillerType">
    <result column="remark" property="remark" />
    <result column="id" property="id" />
    <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="filler_name" property="fillerName" />
        <result column="filler_type" property="fillerType" />
        <result column="parent_id" property="parentId" />
        <result column="line_id" property="lineId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        remark,
        id,
        deleted,
        create_by, create_time, update_by, update_time, filler_name, filler_type, parent_id, line_id
    </sql>

</mapper>
