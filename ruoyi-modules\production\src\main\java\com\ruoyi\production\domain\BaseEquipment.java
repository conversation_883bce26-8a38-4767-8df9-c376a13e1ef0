//package com.ruoyi.production.domain;
//
//import com.ruoyi.common.core.web.domain.MyBaseEntity;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
///**
// * <p>
// *
// * </p>
// *
// * <AUTHOR> * @since 2022-04-26
// */
//@Data
//@ApiModel(value = "BaseEquipment对象", description = "")
//public class BaseEquipment extends MyBaseEntity {
//
//    private Integer aipId;
//
//    @ApiModelProperty(value = "设备编码")
//    private String eqpCode;
//
//    @ApiModelProperty(value = "设备名称")
//    private String eqpName;
//
//    @ApiModelProperty(value = "设备类型")
//    private Integer type;
//
//    private Long lineId;
//
//    @ApiModelProperty(value = "状态 0：在线；1：离线")
//    private Integer status;
//
//    @ApiModelProperty(value = "别名")
//    private String alias;
//
//    @ApiModelProperty(value = "设备数据")
//    private String data;
//}
