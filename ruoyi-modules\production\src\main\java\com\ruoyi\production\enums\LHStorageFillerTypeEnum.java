package com.ruoyi.production.enums;

/**
 * 货位类型
 * 
 * <AUTHOR>
 */
public enum LHStorageFillerTypeEnum
{
    EMPTY(0, "empty"),
    PART(1, "part"),
    TRAYA(2, "tray_big"),
    TRAYB(3, "tray_small");

//    TRAYA(2, "tray1"),
//    TRAYB(3, "tray2"),
//    TRAYC(4, "tray3");

    private final Integer code;
    private final String info;

    LHStorageFillerTypeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByKey(Integer code) {
        for (LHStorageFillerTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getInfo();
            }
        }
        return null;
    }

    public static Integer getInfoByValue(String value) {
        for (LHStorageFillerTypeEnum type : values()) {
            if (type.getInfo().equals(value)) {
                return type.getCode();
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
